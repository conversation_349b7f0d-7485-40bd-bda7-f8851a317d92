// src/services/googlePhotosService.ts
import { google } from 'googleapis';
import { OAuth2Client } from 'google-auth-library'; // Wichtig für den Typ des Clients
import config from '../config/config';
import logger from '../utils/logger';
import * as userRepository from '../db/userRepository';
import { GoogleTokenData, User } from '../types/user';
import { MediaItem, GooglePhotosMediaItem } from '../types/googlePhotos'; // Unser interner Typ und der API-Typ

const log = logger.getLogger(__filename);

const PHOTOS_API_BASE_URL = 'https://photoslibrary.googleapis.com/v1';


/**
 * Findet Google Photos Medienobjekte für einen bestimmten Benutzer innerhalb eines Datumsbereichs.
 * Verwendet User-spezifische Tokens und macht einen direkten API-Aufruf.
 */
export async function findPhotosByDateRangeForUser(
    userId: number,
    activityStartDateLocal: Date,
    activityEndDateLocal: Date,
    pageSize: number = 25,
    pageToken?: string
): Promise<{ mediaItems: MediaItem[], nextPageToken?: string }> {
    const fnLogPrefix = `[GPhotosSvc FindByDateRange User:${userId}]`;

    // WICHTIGER HINWEIS: Google hat am 1. April 2025 die Google Photos Library API geändert
    // Der Scope 'photoslibrary.readonly' wurde entfernt und die API kann nur noch auf
    // von der App erstellte Inhalte zugreifen. Für das Durchsuchen von Benutzerfotos
    // muss die neue Google Photos Picker API verwendet werden.
    log.warn(`${fnLogPrefix} WARNUNG: Google Photos Library API wurde am 1. April 2025 geändert. Diese Funktion kann nur noch auf von der App erstellte Fotos zugreifen.`);

    throw new Error("Google Photos Suche ist aufgrund von API-Änderungen von Google (April 2025) nicht mehr verfügbar. Google hat die Berechtigung zum Durchsuchen von Benutzerfotos entfernt. Für die Fotoauswahl muss die neue Google Photos Picker API implementiert werden.");

    // Der folgende Code bleibt für zukünftige Implementierung mit Picker API erhalten
    const oauth2Client = await getAuthenticatedClient(userId);

    const convertDateToApiParts = (date: Date) => ({ year: date.getFullYear(), month: date.getMonth() + 1, day: date.getDate() });
    const filterStartDate = convertDateToApiParts(activityStartDateLocal);
    const filterEndDate = convertDateToApiParts(activityEndDateLocal);

    const searchRequestBody = {
        pageSize: Math.min(pageSize, 100),
        filters: { dateFilter: { ranges: [{ startDate: filterStartDate, endDate: filterEndDate }] }, mediaTypeFilter: { mediaTypes: ['PHOTO'] } },
        pageToken: pageToken || undefined
    };

    log.info(`${fnLogPrefix} Searching media items with date range: Start ${JSON.stringify(filterStartDate)}, End ${JSON.stringify(filterEndDate)}`);

    try {
        const response = await oauth2Client.request<{ mediaItems?: GooglePhotosMediaItem[], nextPageToken?: string }>({
            url: `${PHOTOS_API_BASE_URL}/mediaItems:search`,
            method: 'POST',
            data: searchRequestBody,
        });
        
        const googleMediaItems = response.data.mediaItems || [];
        const mediaItems: MediaItem[] = [];

        for (const item of googleMediaItems) {
            if (item && item.mimeType?.startsWith('image/') && item.id && item.productUrl && item.baseUrl && item.filename && item.mediaMetadata?.creationTime) {
                const photoCreationTime = new Date(item.mediaMetadata.creationTime);
                if (photoCreationTime >= activityStartDateLocal && photoCreationTime <= activityEndDateLocal) {
                    mediaItems.push({
                        id: item.id,
                        description: item.description || null,
                        productUrl: item.productUrl,
                        baseUrl: item.baseUrl,
                        mimeType: item.mimeType,
                        filename: item.filename,
                        mediaMetadata: {
                            creationTime: photoCreationTime,
                            width: item.mediaMetadata.width ? parseInt(String(item.mediaMetadata.width), 10) : undefined,
                            height: item.mediaMetadata.height ? parseInt(String(item.mediaMetadata.height), 10) : undefined,
                        }
                    });
                } else { /* log.debug(...) */ }
            }
        }
        
        log.info(`${fnLogPrefix} Found ${googleMediaItems.length} items from API, filtered to ${mediaItems.length}. NextPageToken: ${!!response.data.nextPageToken}`);
        return { mediaItems, nextPageToken: response.data.nextPageToken || undefined };
    } catch (error: any) {
        log.error(`${fnLogPrefix} Fehler beim Suchen von Medienobjekten:`, error.response?.data || error.message || error);

        // Spezifische Behandlung für Authentifizierungsfehler
        if (error.response?.status === 401 || (error.response?.data?.error?.status === 'UNAUTHENTICATED')) {
             log.warn(`${fnLogPrefix} Google API Token Problem für User ${userId}. Tokens werden gelöscht.`);
             await userRepository.clearUserGoogleTokens(userId);
             throw new Error("Google Photos Authentifizierung fehlgeschlagen. Bitte erneut in den Einstellungen verbinden.");
        }

        // Spezifische Behandlung für Scope-Probleme
        if (error.response?.status === 403 && error.response?.data?.error?.reason === 'forbidden') {
            const errorMessage = error.response.data.error.message || '';
            if (errorMessage.includes('insufficient authentication scopes')) {
                log.warn(`${fnLogPrefix} Google API Scope Problem für User ${userId}. Benötigte Berechtigung fehlt.`);
                await userRepository.clearUserGoogleTokens(userId);
                throw new Error("Google Photos Berechtigung unvollständig. Die Verbindung muss mit vollständigen Berechtigungen in den Einstellungen erneuert werden.");
            }
            throw new Error(`Google Photos Zugriff verweigert: ${errorMessage}`);
        }

        const apiErrorMessage = error.response?.data?.error?.message || error.message || "Unbekannter Google Photos API Fehler";
        throw new Error(`Fehler bei der Google Photos API: ${apiErrorMessage}`);
    }
}


/**
 * Erstellt und konfiguriert einen OAuth2-Client für den Zugriff auf die Google Photos API
 * mit den Tokens des spezifischen Benutzers. Erneuert Tokens bei Bedarf.
 * @param userId Die ID des Benutzers.
 * @returns Promise<OAuth2Client> Der konfigurierte OAuth2-Client.
 */
async function getAuthenticatedClient(userId: number): Promise<OAuth2Client> {
    const fnLogPrefix = `[GPhotosSvc Auth User:${userId}]`;
    // getUserGoogleTokens gibt jetzt GoogleTokenData | null zurück
    const userTokens: GoogleTokenData | null = await userRepository.getUserGoogleTokens(userId);



    // KORREKTUR: Zugriff auf userTokens.accessToken etc.
    if (!userTokens || !userTokens.accessToken) {
        log.error(`${fnLogPrefix} Keine Google Access Tokens für User ${userId} gefunden.`);
        throw new Error("Google Photos ist für diesen Benutzer nicht (oder nicht mehr korrekt) verbunden. Bitte in den Einstellungen erneut verbinden.");
    }

    const oauth2Client = new google.auth.OAuth2(
        config.googlePhotos.clientId,
        config.googlePhotos.clientSecret,
        config.googlePhotos.redirectUri
    );

    oauth2Client.setCredentials({
        access_token: userTokens.accessToken,         // KORREKTUR
        refresh_token: userTokens.refreshToken,       // KORREKTUR
        expiry_date: userTokens.expiresAt,            // KORREKTUR (ist number | null)
    });

    oauth2Client.on('tokens', async (newTokens) => {
        log.info(`${fnLogPrefix} Google Access Token wurde erneuert.`);
        const updatedTokenData: Partial<GoogleTokenData> = {};
        if (newTokens.access_token) updatedTokenData.accessToken = newTokens.access_token;
        if (newTokens.expiry_date) updatedTokenData.expiresAt = newTokens.expiry_date;
        if (newTokens.refresh_token) updatedTokenData.refreshToken = newTokens.refresh_token;
        if (newTokens.scope && typeof newTokens.scope === 'string') {
             const currentScopes = userTokens.scope ? userTokens.scope.split(' ') : []; // Verwende userTokens.scope hier
             const newScopesArray = newTokens.scope.split(' ');
             const allScopes = Array.from(new Set([...currentScopes, ...newScopesArray]));
             updatedTokenData.scope = allScopes.join(' ');
        }
        
        if (Object.keys(updatedTokenData).length > 0) {
            await userRepository.updateUserGoogleTokens(userId, updatedTokenData);
            log.info(`${fnLogPrefix} Erneuerte Google Tokens für User ${userId} gespeichert.`);
        }
    });
    
    // KORREKTUR: Zugriff auf userTokens.expiresAt
    if (userTokens.expiresAt && userTokens.expiresAt < Date.now() + 60000) { // 1 Min Puffer
        try {
            log.info(`${fnLogPrefix} Google Token ist abgelaufen oder läuft bald ab. Versuche Refresh...`);
            const { credentials } = await oauth2Client.refreshAccessToken();
            oauth2Client.setCredentials(credentials);
        } catch (refreshError: any) {
            log.error(`${fnLogPrefix} Fehler beim Erneuern des Google Access Tokens:`, refreshError.response?.data || refreshError.message);
            await userRepository.clearUserGoogleTokens(userId); 
            throw new Error("Google Photos Authentifizierung fehlgeschlagen (Token Refresh). Bitte erneut verbinden.");
        }
    }
    return oauth2Client;
}


/**
 * Sucht nach Medien-Items (Fotos) basierend auf Suchoptionen.
 * @param userId Die ID des Users, dessen Google Photos durchsucht werden sollen.
 * @param searchOptions Optionen für die Suche (albumId, filters, pageSize).
 */
// Die Funktionen listAlbums, searchMediaItems, getMediaItem bleiben wie in Ihrer hochgeladenen Version,
// da sie bereits den korrigierten getAuthenticatedClient verwenden.
export async function searchMediaItems(userId: number, searchOptions: any = {}): Promise<GooglePhotosMediaItem[]> {
    const fnLogPrefix = `[GPhotosSvc SearchMedia User:${userId}]`;
    log.info(`${fnLogPrefix} Searching with options:`, searchOptions);
    const oauth2Client = await getAuthenticatedClient(userId);
    const accessToken = oauth2Client.credentials.access_token; // Korrekter Zugriff
    const requestBodyPayload: any = { pageSize: searchOptions.pageSize || 100 };
    if (searchOptions.albumId) requestBodyPayload.albumId = searchOptions.albumId;
    if (searchOptions.filters) requestBodyPayload.filters = searchOptions.filters;

    try {
        const response = await fetch(`${PHOTOS_API_BASE_URL}/mediaItems:search`, {
            method: 'POST',
            headers: { 'Authorization': `Bearer ${accessToken}`, 'Content-Type': 'application/json' },
            body: JSON.stringify(requestBodyPayload)
        });
        if (!response.ok) { /* ... Fehlerbehandlung ... */ throw new Error('API Error'); }
        const data = await response.json();
        log.info(`${fnLogPrefix} Found ${data.mediaItems ? data.mediaItems.length : 0} items.`);
        return data.mediaItems || [];
    } catch (error: any) { /* ... Fehlerbehandlung ... */ throw error; }
}



/**
 * Ruft Details für ein einzelnes Medien-Item (Foto) ab.
 * @param userId Die ID des Users.
 * @param mediaItemId Die ID des Google Photo Items.
 */
export const getMediaItem = async (mediaItemId: string, userId: number): Promise<GooglePhotosMediaItem | null> => {
    const fnLogPrefix = `[GPhotosSvc GetMediaItem User:${userId} Item:${mediaItemId}]`;
    log.info(`${fnLogPrefix} Fetching details...`);
    try {
        const oauth2Client = await getAuthenticatedClient(userId);
        const accessToken = oauth2Client.credentials.access_token; // Korrekter Zugriff
        const response = await fetch(`${PHOTOS_API_BASE_URL}/mediaItems/${mediaItemId}`, {
            method: 'GET',
            headers: { 'Authorization': `Bearer ${accessToken}`, 'Content-Type': 'application/json' }
        });
        if (!response.ok) { /* ... Fehlerbehandlung ... */ throw new Error('API Error'); }
        return await response.json();
    } catch (error: any) { /* ... Fehlerbehandlung ... */ throw error; }
};

// listAlbums könnte ähnlich angepasst werden, um die userId zu akzeptieren,
// falls Alben User-spezifisch wären (was sie in Google Photos nicht direkt sind,
// aber der Zugriff erfolgt über den Token des Users).
export const listAlbums = async (userId: number): Promise<GooglePhotosMediaItem[]> => { // Typ sollte GooglePhotosAlbum[] sein
    const fnLogPrefix = `[GPhotosSvc ListAlbums User:${userId}]`;
    log.info(`${fnLogPrefix} Fetching albums...`);
     try {
        const oauth2Client = await getAuthenticatedClient(userId);
        const accessToken = oauth2Client.credentials.access_token; // Korrekter Zugriff
        const params = new URLSearchParams({ pageSize: String(config.googlePhotos.albumPageSize || 50) });
        const response = await fetch(`${PHOTOS_API_BASE_URL}/albums?${params.toString()}`, {
            method: 'GET',
            headers: { 'Authorization': `Bearer ${accessToken}`, 'Content-Type': 'application/json' }
        });
        if (!response.ok) { /* ... Fehlerbehandlung ... */ throw new Error('API Error'); }
        const data = await response.json();
        log.info(`${fnLogPrefix} Found ${data.albums ? data.albums.length : 0} albums.`);
        return data.albums || [];
    } catch (error: any) { /* ... Fehlerbehandlung ... */ throw error; }
};