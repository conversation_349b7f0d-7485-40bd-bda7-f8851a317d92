// src/controllers/authController.ts
import { Request, Response, NextFunction } from 'express';
import config from '../config/config';
import logger from '../utils/logger';
import passport from 'passport'; // Passport für die Login-Route importieren
import { google, Auth } from 'googleapis'; // Auth für OAuth2Client importieren
import * as userRepository from '../db/userRepository';
import * as sportGroupService from '../services/sportGroupService'; // NEU: Sportart-Obergruppen
import { StravaTokenData, GoogleTokenData, GoogleDriveTokenData } from '../types/user'; // Importiere deine User-Typen


const log = logger.getLogger(__filename);

// --- Login-Seiten Handler ---
// Diese Funktion ist dafür zuständig, die Login-Seite anzuzeigen.
// Sie wird von der Route GET /auth/login aufgerufen.
// --- Lo<PERSON>-Seiten Handler ---
export const showLoginPage = (req: Request, res: Response) => {
    const fnLogPrefix = '[Ctrl ShowLoginPage]';
    // req.user ist hier noch nicht zuverlässig gesetzt, bevor der Login-Prozess durchlaufen ist.
    // req.session.user ist hier relevant, FALLS Sie es nach dem Login manuell setzen würden,
    // was aber durch Passport und req.logIn() + serializeUser/deserializeUser gehandhabt wird.
    // Für "ist bereits eingeloggt" ist req.isAuthenticated() oder req.user die bessere Prüfung.
    if (req.isAuthenticated && req.isAuthenticated()) { // Von Passport
        log.info(`${fnLogPrefix} User ${req.user?.username} ist bereits eingeloggt. Redirect zum Dashboard.`);
        if (req.user?.role === 'admin') {
            return res.redirect('/admin/dashboard');
        }
        return res.redirect('/user/dashboard');
    }
    log.info(`${fnLogPrefix} Rendering login page.`);
    res.render('login', {
        pageTitle: 'Anmelden',
        layout: 'layouts/simple_layout',
        pageSpecificClass: 'login-page-body'
    });
};

// --- Login Verarbeitungs-Handler ---
export const handleLogin = (req: Request, res: Response, next: NextFunction) => {
    const fnLogPrefix = '[Ctrl HandleLogin]';
    log.debug(`${fnLogPrefix} Login attempt for user: ${req.body.username}`);

    passport.authenticate('local', (err: any, user: Express.User | false | null, info: any) => {
        if (err) {
            log.error(`${fnLogPrefix} Passport authentication error:`, err);
            return next(err);
        }
        if (!user) {
            log.warn(`${fnLogPrefix} Login failed for ${req.body.username}: ${info ? info.message : 'Unbekannter Fehler'}`);
            req.flash('error', info ? info.message : 'Login fehlgeschlagen');
            return res.redirect('/auth/login');
        }
        req.logIn(user, (errLogin: any) => {
            if (errLogin) {
                log.error(`${fnLogPrefix} Error during req.logIn for ${user.username}:`, errLogin);
                return next(errLogin);
            }
            req.session.save(async (errSave) => {
                if (errSave) {
                    log.error(`${fnLogPrefix} Error saving session after login for ${user.username}:`, errSave);
                    return next(errSave);
                }
                log.info(`${fnLogPrefix} User ${user.username} successfully logged in and session saved.`);

                // Initialisiere Sportart-Obergruppen für den Benutzer, falls noch nicht vorhanden
                try {
                    await sportGroupService.initializeDefaultSportGroupsForUser(user.id);
                    log.info(`${fnLogPrefix} Sport groups initialized for user ${user.username}`);
                } catch (sportGroupError) {
                    log.error(`${fnLogPrefix} Error initializing sport groups for user ${user.username}:`, sportGroupError);
                    // Wir brechen den Login-Prozess nicht ab, wenn die Initialisierung fehlschlägt
                }

                if (user.role === 'admin') {
                    return res.redirect('/admin/dashboard');
                }
                return res.redirect('/user/dashboard');
            });
        });
    })(req, res, next);
};


// --- Logout Handler ---
// Diese Funktion ist für den Logout zuständig und wird von der Route GET /auth/logout aufgerufen.
export const logout = (req: Request, res: Response, next: NextFunction) => {
    const usernameForLog = req.user?.username || req.session?.user?.username || 'Unbekannter Benutzer';
    const fnLogPrefix = `[Ctrl Logout User:${usernameForLog}]`;

    // Setze Flash-Nachricht VOR dem Zerstören der Session
    req.flash('success', 'Erfolgreich ausgeloggt.');

    req.logout((errLogout: any) => {
        if (errLogout) {
            log.error(`${fnLogPrefix} Fehler beim Passport Logout:`, errLogout);
        }
        if (req.session) {
            req.session.destroy((errSession) => {
                if (errSession) {
                    log.error(`${fnLogPrefix} Fehler beim Zerstören der Session:`, errSession);
                    // Keine Flash-Nachricht mehr hier, da Session bereits zerstört
                    return res.redirect('/auth/login');
                }
                log.info(`${fnLogPrefix} User erfolgreich ausgeloggt. Session zerstört.`);
                const sessionCookieName = config.session.name || 'connect.sid';
                res.clearCookie(sessionCookieName, { path: '/' });
                res.redirect('/auth/login');
            });
        } else {
            log.warn(`${fnLogPrefix} Keine Session zum Zerstören vorhanden beim Logout.`);
            const sessionCookieName = config.session.name || 'connect.sid';
            res.clearCookie(sessionCookieName, { path: '/' });
            res.redirect('/auth/login');
        }
    });
};



// --- Strava und Google Authentifizierungs-Handler ---
export const requestStravaAuthorization = (req: Request, res: Response) => {
    const user = req.user; // KORREKT: req.user verwenden, das von Passport gesetzt wird
    if (!user) {
        log.warn(`[StravaAuthRequest] Kein req.user gefunden. User nicht authentifiziert für Strava Verbindung.`);
        req.flash('error', 'Bitte zuerst einloggen, um Strava zu verbinden.');
        return res.redirect('/auth/login');
    }

    const clientId = config.strava.clientId;
    const redirectUri = config.strava.redirectUri;

    if (!clientId || !config.strava.clientSecret) {
        log.error("[StravaAuthRequest] Strava Client ID oder Client Secret nicht in der Konfiguration gefunden!");
        req.flash('error', 'Strava-Konfiguration unvollständig. Bitte Admin kontaktieren.');
        return res.redirect('/user/settings');
    }

    const authURL = new URL(config.strava.authUrl);
    authURL.searchParams.append('client_id', clientId.toString());
    authURL.searchParams.append('redirect_uri', redirectUri);
    authURL.searchParams.append('response_type', 'code');
    authURL.searchParams.append('approval_prompt', 'force');
    authURL.searchParams.append('scope', 'activity:read_all,profile:read_all');
    authURL.searchParams.append('state', `user:${user.id};service:strava`);
    log.info(`[StravaAuthRequest User:${user.id}] Redirecting to Strava: ${authURL.toString()}`);
    res.redirect(authURL.toString());
};

export const handleStravaCallback = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const code = req.query.code as string;
    const stateReceived = req.query.state as string;
    const fnLogPrefix = `[StravaCallback]`;
    // Wichtig: User-ID aus dem State oder req.user holen
    const userId = req.user?.id || (stateReceived?.split(';')[0]?.split(':')[1] ? parseInt(stateReceived.split(';')[0].split(':')[1], 10) : null);

    if (!userId) {
        log.error(`${fnLogPrefix} Konnte User-ID nicht aus State oder Session ermitteln.`);
        req.flash('error', 'Fehler bei der Strava-Authentifizierung: Benutzerkontext fehlt.');
        return res.redirect('/user/settings');
    }

    if (code && stateReceived) {
        log.info(`${fnLogPrefix} Strava Callback erfolgreich, Code erhalten. User-State: ${stateReceived}, User-ID: ${userId}`);
        try {
            // Hier Logik zum Token-Austausch und Speichern
            // Beispiel: await stravaService.exchangeCodeForTokensAndSave(code, userId);
            log.info(`${fnLogPrefix} Tokens für User ${userId} würden jetzt ausgetauscht und gespeichert.`);
            req.flash('success', 'Strava erfolgreich verbunden (Implementierung ausstehend).');
            res.redirect('/user/settings');
        } catch (error: any) {
            log.error(`${fnLogPrefix} Fehler beim Verarbeiten des Strava Callbacks für User ${userId}:`, error);
            req.flash('error', error.message || 'Fehler beim Verarbeiten der Strava-Antwort.');
            res.redirect('/user/settings');
        }
    } else {
        log.error(`${fnLogPrefix} Strava Callback Fehler oder Code/State fehlt.`);
        req.flash('error', 'Fehler bei der Strava-Authentifizierung.');
        res.redirect('/user/settings');
    }
};

export const requestGooglePhotosAuthorization = (req: Request, res: Response) => {
    requestGoogleAuthorization(req, res, 'photos');
};

export const requestGoogleDriveAuthorization = (req: Request, res: Response) => {
    requestGoogleAuthorization(req, res, 'drive');
};

/**
 * Gemeinsame Funktion für Google OAuth-Autorisierung (Photos und Drive)
 */
const requestGoogleAuthorization = (req: Request, res: Response, service: 'photos' | 'drive') => {
    const user = req.user;
    if (!user) {
        log.warn(`[GoogleAuthRequest] Kein req.user gefunden.`);
        req.flash('error', `Bitte zuerst einloggen, um Google ${service === 'photos' ? 'Photos' : 'Drive'} zu verbinden.`);
        return res.redirect('/auth/login');
    }
    if (!config.googlePhotos.clientId || !config.googlePhotos.clientSecret) {
        log.error("[GoogleAuthRequest] Google Client ID oder Secret nicht konfiguriert!");
        req.flash('error', `Google ${service === 'photos' ? 'Photos' : 'Drive'}-Konfiguration unvollständig.`);
        return res.redirect('/user/settings');
    }

    const oauth2Client = createGoogleOAuth2Client();

    // Kombiniere Scopes für beide Services
    const combinedScopes = [
        ...config.googlePhotos.scopes,
        ...config.googleDrive.scopes
    ];

    const authURL = oauth2Client.generateAuthUrl({
        access_type: 'offline',
        prompt: 'consent', // Wichtig, um immer einen Refresh-Token zu bekommen
        scope: combinedScopes,
        state: `user:${user.id};service:${service}` // User-ID und Service im State mitsenden
    });
    log.info(`[GoogleAuthRequest User:${user.id}] Redirecting to Google for ${service}: ${authURL}`);
    res.redirect(authURL);
};

export const handleGooglePhotosCallback = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    await handleGoogleCallback(req, res, next);
};

/**
 * Trennt die Verbindung zu Google Photos
 */
export const disconnectGooglePhotos = async (req: Request, res: Response) => {
    const userId = req.user?.id;
    const fnLogPrefix = `[GooglePhotosDisconnect User:${userId}]`;

    if (!userId) {
        log.error(`${fnLogPrefix} Kein User in der Session gefunden.`);
        req.flash('error', 'Bitte zuerst einloggen.');
        return res.redirect('/auth/login');
    }

    try {
        const success = await userRepository.clearUserGoogleTokens(userId);
        if (success) {
            log.info(`${fnLogPrefix} Google Photos Verbindung erfolgreich getrennt.`);
            req.flash('success', 'Google Photos Verbindung erfolgreich getrennt.');
        } else {
            log.warn(`${fnLogPrefix} Google Photos Verbindung konnte nicht getrennt werden.`);
            req.flash('warning', 'Google Photos Verbindung konnte nicht getrennt werden.');
        }
    } catch (error: any) {
        log.error(`${fnLogPrefix} Fehler beim Trennen der Google Photos Verbindung:`, error.message);
        req.flash('error', `Fehler beim Trennen der Google Photos Verbindung: ${error.message}`);
    }

    res.redirect('/user/settings');
};

/**
 * Gemeinsamer Google OAuth Callback-Handler für Photos und Drive
 */
const handleGoogleCallback = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const code = req.query.code as string;
    const stateReceived = req.query.state as string;
    const fnLogPrefix = `[GoogleCallback]`;

    // User-ID und Service aus dem State extrahieren
    let userIdFromState: number | null = null;
    let serviceFromState: string | null = null;
    if (stateReceived) {
        const parts = stateReceived.split(';');
        const userPart = parts.find(p => p.startsWith('user:'));
        const servicePart = parts.find(p => p.startsWith('service:'));
        if (userPart) {
            userIdFromState = parseInt(userPart.split(':')[1], 10);
        }
        if (servicePart) {
            serviceFromState = servicePart.split(':')[1];
        }
    }
    const userId = req.user?.id || userIdFromState;

    if (!userId) {
        log.error(`${fnLogPrefix} Konnte User-ID nicht aus State oder Session ermitteln.`);
        req.flash('error', 'Fehler bei der Google Photos-Authentifizierung: Benutzerkontext fehlt.');
        return res.redirect('/user/settings');
    }

    if (code) {
        const serviceName = serviceFromState === 'drive' ? 'Google Drive' :
                           serviceFromState === 'photos-picker' ? 'Google Photos Picker' :
                           'Google Photos';
        log.info(`${fnLogPrefix} ${serviceName} Callback erfolgreich, Code erhalten für User-ID: ${userId}. Code: ${code.substring(0,15)}...`);
        const oauth2Client = createGoogleOAuth2Client();
        try {
            const { tokens } = await oauth2Client.getToken(code);
            log.info(`${fnLogPrefix} Tokens von Google erhalten für User ${userId}. AccessToken vorhanden: ${!!tokens.access_token}, RefreshToken vorhanden: ${!!tokens.refresh_token}`);

            oauth2Client.setCredentials(tokens); // Wichtig für weitere API-Aufrufe, falls direkt hier nötig

            // Analysiere die erhaltenen Scopes um zu bestimmen, welche Services aktiviert wurden
            const grantedScopes = tokens.scope ? tokens.scope.split(' ') : [];
            const hasPhotosScope = grantedScopes.some(scope => scope.includes('photoslibrary'));
            const hasDriveScope = grantedScopes.some(scope => scope.includes('drive'));

            log.info(`${fnLogPrefix} Erhaltene Scopes: ${tokens.scope}. Photos: ${hasPhotosScope}, Drive: ${hasDriveScope}`);

            const successMessages: string[] = [];
            const errorMessages: string[] = [];

            // Speichere Google Photos Tokens, wenn Photos-Scope vorhanden ist
            if (hasPhotosScope) {
                const tokenDataToSave: GoogleTokenData = {
                    accessToken: tokens.access_token || null,
                    refreshToken: tokens.refresh_token || null,
                    expiresAt: tokens.expiry_date || null,
                    scope: tokens.scope || null
                };

                const success = await userRepository.updateUserGoogleTokens(userId, tokenDataToSave);
                if (success) {
                    log.info(`${fnLogPrefix} Google Photos Tokens erfolgreich für User ${userId} in DB gespeichert.`);
                    successMessages.push('Google Photos');
                } else {
                    log.error(`${fnLogPrefix} Fehler beim Speichern der Google Photos Tokens in DB für User ${userId}.`);
                    errorMessages.push('Google Photos');
                }
            }

            // Speichere Google Drive Tokens, wenn Drive-Scope vorhanden ist
            if (hasDriveScope) {
                const tokenDataToSave: GoogleDriveTokenData = {
                    accessToken: tokens.access_token || null,
                    refreshToken: tokens.refresh_token || null,
                    expiresAt: tokens.expiry_date || null,
                    scope: tokens.scope || null
                };

                const success = await userRepository.updateUserGoogleDriveTokens(userId, tokenDataToSave);
                if (success) {
                    log.info(`${fnLogPrefix} Google Drive Tokens erfolgreich für User ${userId} in DB gespeichert.`);
                    successMessages.push('Google Drive');
                } else {
                    log.error(`${fnLogPrefix} Fehler beim Speichern der Google Drive Tokens in DB für User ${userId}.`);
                    errorMessages.push('Google Drive');
                }
            }

            // Speichere Google Photos Picker Tokens, wenn Photos-Picker-Service angefragt wurde
            if (serviceFromState === 'photos-picker') {
                const tokenDataToSave = {
                    accessToken: tokens.access_token || null,
                    refreshToken: tokens.refresh_token || null,
                    expiresAt: tokens.expiry_date || null,
                    scope: tokens.scope || null
                };

                const success = await userRepository.updateUserGooglePhotosPickerTokens(userId, tokenDataToSave);
                if (success) {
                    log.info(`${fnLogPrefix} Google Photos Picker Tokens erfolgreich für User ${userId} in DB gespeichert.`);
                    successMessages.push('Google Photos Picker');
                } else {
                    log.error(`${fnLogPrefix} Fehler beim Speichern der Google Photos Picker Tokens in DB für User ${userId}.`);
                    errorMessages.push('Google Photos Picker');
                }
            }

            // Erstelle entsprechende Flash-Nachrichten
            if (successMessages.length > 0) {
                const message = successMessages.length === 1
                    ? `${successMessages[0]} erfolgreich verbunden!`
                    : `${successMessages.join(' und ')} erfolgreich verbunden!`;
                req.flash('success', message);
            }

            if (errorMessages.length > 0) {
                const message = errorMessages.length === 1
                    ? `Fehler beim Speichern der ${errorMessages[0]} Tokens.`
                    : `Fehler beim Speichern der ${errorMessages.join(' und ')} Tokens.`;
                req.flash('error', message);
            }

            // Fallback, falls keine Scopes erkannt wurden
            if (!hasPhotosScope && !hasDriveScope) {
                log.warn(`${fnLogPrefix} Keine bekannten Scopes in der Token-Antwort gefunden: ${tokens.scope}`);
                req.flash('warning', 'Google-Verbindung hergestellt, aber keine bekannten Services erkannt.');
            }
            res.redirect('/user/settings');
        } catch (error: any) {
            log.error(`${fnLogPrefix} Fehler beim Austauschen des Google Codes gegen Tokens für User ${userId}:`, error.response?.data || error.message || error);
            req.flash('error', error.message || `Fehler beim Verarbeiten der ${serviceName}-Antwort.`);
            res.redirect('/user/settings');
        }
    } else {
        const errorFromQuery = req.query.error;
        const serviceName = serviceFromState === 'drive' ? 'Google Drive' : 'Google Photos';
        log.error(`${fnLogPrefix} ${serviceName} Callback Fehler oder Code fehlt. Fehler von Google: ${errorFromQuery}`);
        req.flash('error', `Fehler bei der ${serviceName}-Authentifizierung: ${errorFromQuery || 'Kein Code erhalten.'}`);
        res.redirect('/user/settings');
    }
};


// --- Google Photos Authentifizierungs-Handler ---
function createGoogleOAuth2Client(): Auth.OAuth2Client {
    return new google.auth.OAuth2(
        config.googlePhotos.clientId,
        config.googlePhotos.clientSecret,
        config.googlePhotos.redirectUri
    );
}


// --- Token Refresh Logik (Beispiel für Strava, angepasst für User) ---
// Diese Funktion könnte auch in einem stravaService.ts liegen
export const refreshUserStravaToken = async (userId: number): Promise<string | null> => {
    const fnLogPrefix = `[StravaTokenRefresh User:${userId}]`;
    log.info(`${fnLogPrefix} Attempting to refresh Strava access token...`);

    const userTokens = await userRepository.getUserStravaTokens(userId);
    if (!userTokens || !userTokens.strava_refresh_token) {
        log.error(`${fnLogPrefix} No Strava refresh token found in DB for user.`);
        throw new Error("Kein Strava Refresh Token für den Benutzer verfügbar.");
    }

    const clientId = config.strava.clientId;
    const clientSecret = config.strava.clientSecret;
    const tokenUrl = config.strava.tokenUrl;

    if (!clientId || !clientSecret) {
        log.error(`${fnLogPrefix} Strava Client ID or Secret not configured!`);
        throw new Error("Serverkonfigurationsfehler: Strava Client ID oder Secret fehlen.");
    }

    try {
        const response = await fetch(tokenUrl, {
            method: 'POST',
            headers: { "Content-Type": "application/json; charset=UTF-8" },
            body: JSON.stringify({
                client_id: clientId,
                client_secret: clientSecret,
                refresh_token: userTokens.strava_refresh_token,
                grant_type: 'refresh_token'
            })
        });

        const stravaData: any = await response.json();
        if (!response.ok) {
            log.error(`${fnLogPrefix} Strava API error during token refresh:`, response.status, stravaData);
            if (response.status === 400 && stravaData.message?.includes("Invalid Refresh Token")) {
                // Refresh Token ist ungültig, lösche ihn aus der DB, damit User neu autorisieren muss
                await userRepository.updateUserStravaTokens(userId, { refreshToken: null, accessToken: null, expiresAt: null });
                log.warn(`${fnLogPrefix} Invalid Strava refresh token for user ${userId}. Tokens cleared from DB.`);
            }
            const apiErrorMessage = stravaData.message || (stravaData.errors && stravaData.errors[0]?.message) || 'Unbekannter Strava API Fehler beim Refresh';
            throw new Error(`Strava API Fehler (${response.status}): ${apiErrorMessage}`);
        }

        log.info(`${fnLogPrefix} Strava token refresh successful.`);
        const updatedTokenData: StravaTokenData = {
            accessToken: stravaData.access_token,
            refreshToken: stravaData.refresh_token || userTokens.strava_refresh_token, // Strava sendet manchmal neuen RT
            expiresAt: stravaData.expires_at,
            scope: userTokens.strava_scope, // Scope bleibt meist gleich
            athleteId: userTokens.strava_athlete_id
        };

        await userRepository.updateUserStravaTokens(userId, updatedTokenData);
        log.info(`${fnLogPrefix} Updated Strava tokens saved for user ${userId}.`);
        return updatedTokenData.accessToken;

    } catch (error: any) {
        log.error(`${fnLogPrefix} Critical error refreshing Strava token: ${error.message}`);
        throw error; // Fehler weiterwerfen
    }
};

// refreshGoogleAccessToken (ähnlich wie oben, aber für Google)
// Diese Funktion wird typischerweise vom googlePhotosService aufgerufen, wenn ein Token abgelaufen ist.
// Sie ist komplexer, da sie oft direkt mit dem google.auth.OAuth2Client arbeitet.
// Fürs Erste als Platzhalter, die Logik im googlePhotosService.ts ist hier relevanter.
export const refreshUserGoogleAccessTokenUtility = async (userId: number): Promise<string | null> => {
    const fnLogPrefix = `[GoogleTokenRefreshUtil User:${userId}]`;
    log.info(`${fnLogPrefix} Placeholder for refreshing Google access token for a user.`);
    // TODO: Implementiere Logik ähnlich zu refreshUserStravaToken, aber mit dem googleOAuth2Client.
    // 1. User's google_refresh_token aus DB holen.
    // 2. oauth2Client.setCredentials({ refresh_token: userRefreshToken });
    // 3. const { credentials } = await oauth2Client.refreshAccessToken();
    // 4. Neue credentials.access_token und credentials.expiry_date in DB für User speichern.
    // 5. Neuen access_token zurückgeben.
    // Diese Logik ist schon teilweise in googlePhotosService.getAuthenticatedClient,
    // es müsste nur User-spezifisch gemacht werden.
    return null;
};