// src/controllers/googlePhotoPickerController.ts
import { Request, Response } from 'express';
import config from '../config/config';
import logger from '../utils/logger';

const log = logger.getLogger(__filename);

/**
 * Liefert die Konfiguration für die Google Photos Picker API
 */
export const getPickerConfig = (req: Request, res: Response): void => {
    const fnLogPrefix = `[GooglePickerConfig User:${req.user?.id}]`;
    
    try {
        if (!config.googlePhotos.enablePickerApi) {
            log.warn(`${fnLogPrefix} Google Photos Picker API ist deaktiviert`);
            res.status(503).json({ 
                error: 'Google Photos Picker API ist nicht verfügbar',
                enabled: false 
            });
            return;
        }

        if (!config.googlePhotos.clientId) {
            log.error(`${fnLogPrefix} Google Photos Picker API nicht konfiguriert - Client ID fehlt`);
            res.status(500).json({
                error: 'Google Photos Picker API ist nicht konfiguriert - Client ID fehlt',
                enabled: false
            });
            return;
        }

        log.info(`${fnLogPrefix} Picker API Konfiguration bereitgestellt`);

        res.json({
            enabled: true,
            clientId: config.googlePhotos.clientId,
            // Picker API verwendet die gleiche Client ID wie OAuth - kein separater API Key nötig
        });
    } catch (error: any) {
        log.error(`${fnLogPrefix} Fehler beim Bereitstellen der Picker-Konfiguration:`, error);
        res.status(500).json({ 
            error: 'Interner Serverfehler',
            enabled: false 
        });
    }
};

/**
 * Erstellt eine neue Google Photos Picker Session (korrekte API)
 */
export const createPickerSession = async (req: Request, res: Response): Promise<void> => {
    const fnLogPrefix = `[GooglePickerSession User:${req.user?.id}]`;

    try {
        if (!req.user) {
            res.status(401).json({ error: 'Nicht authentifiziert' });
            return;
        }

        if (!config.googlePhotos.enablePickerApi) {
            res.status(503).json({
                error: 'Google Photos Picker API ist deaktiviert',
                enabled: false
            });
            return;
        }

        // Hole den Access Token aus der Datenbank (bestehende Integration)
        const userRepository = await import('../db/userRepository');
        const userTokens = await userRepository.getUserGoogleTokens(req.user.id);

        if (!userTokens || !userTokens.accessToken) {
            res.status(401).json({
                error: 'Keine Google Photos Verbindung gefunden. Bitte verbinde Google Photos in den Einstellungen.',
                needsReauth: true
            });
            return;
        }

        const accessToken = userTokens.accessToken;

        log.info(`${fnLogPrefix} Erstelle neue Picker Session`);

        // Erstelle Session mit der korrekten Google Photos Picker API
        const response = await fetch(`${config.googlePhotos.pickerApiUrl}/sessions`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${accessToken}`
            }
        });

        if (!response.ok) {
            const errorText = await response.text();
            log.error(`${fnLogPrefix} Fehler beim Erstellen der Picker Session: ${response.status} ${errorText}`);

            if (response.status === 401 || response.status === 403) {
                res.status(401).json({
                    error: 'Google Photos Berechtigung fehlt oder abgelaufen. Bitte erneut verbinden.',
                    needsReauth: true
                });
            } else {
                res.status(500).json({
                    error: `Google Photos API Fehler: ${response.status}`,
                    details: errorText
                });
            }
            return;
        }

        const sessionData = await response.json();
        log.info(`${fnLogPrefix} Picker Session erstellt: ${sessionData.id}`);

        res.json({
            success: true,
            session: sessionData,
            pickerUrl: sessionData.pickerUri
        });

    } catch (error: any) {
        log.error(`${fnLogPrefix} Fehler beim Erstellen der Picker Session:`, error);
        res.status(500).json({
            error: 'Fehler beim Erstellen der Picker Session',
            details: error.message
        });
    }
};

/**
 * Holt die ausgewählten Fotos aus einer Picker Session
 */
export const getPickerPhotos = async (req: Request, res: Response): Promise<void> => {
    const fnLogPrefix = `[GooglePickerPhotos User:${req.user?.id}]`;
    const { sessionId } = req.params;

    try {
        if (!req.user) {
            res.status(401).json({ error: 'Nicht authentifiziert' });
            return;
        }

        // Hole den Access Token aus der Datenbank (bestehende Integration)
        const userRepository = await import('../db/userRepository');
        const userTokens = await userRepository.getUserGoogleTokens(req.user.id);

        if (!userTokens || !userTokens.accessToken) {
            res.status(401).json({
                error: 'Keine Google Photos Verbindung gefunden. Bitte verbinde Google Photos in den Einstellungen.',
                needsReauth: true
            });
            return;
        }

        const accessToken = userTokens.accessToken;

        log.info(`${fnLogPrefix} Hole Fotos aus Session ${sessionId}`);

        // Hole Session-Status
        const sessionResponse = await fetch(`${config.googlePhotos.pickerApiUrl}/sessions/${sessionId}`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${accessToken}`
            }
        });

        if (!sessionResponse.ok) {
            const errorText = await sessionResponse.text();
            log.error(`${fnLogPrefix} Fehler beim Abrufen der Session: ${sessionResponse.status} ${errorText}`);
            res.status(500).json({ error: 'Fehler beim Abrufen der Session' });
            return;
        }

        const sessionData = await sessionResponse.json();

        // Hole Media Items aus der Session
        const mediaResponse = await fetch(`${config.googlePhotos.pickerApiUrl}/mediaItems?sessionId=${sessionId}&pageSize=50`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${accessToken}`
            }
        });

        if (!mediaResponse.ok) {
            const errorText = await mediaResponse.text();
            log.error(`${fnLogPrefix} Fehler beim Abrufen der Media Items: ${mediaResponse.status} ${errorText}`);
            res.status(500).json({ error: 'Fehler beim Abrufen der Fotos' });
            return;
        }

        const mediaData = await mediaResponse.json();
        log.info(`${fnLogPrefix} ${mediaData.mediaItems?.length || 0} Fotos aus Session abgerufen`);

        res.json({
            success: true,
            session: sessionData,
            photos: mediaData.mediaItems || []
        });

    } catch (error: any) {
        log.error(`${fnLogPrefix} Fehler beim Abrufen der Picker Fotos:`, error);
        res.status(500).json({
            error: 'Fehler beim Abrufen der Fotos',
            details: error.message
        });
    }
};

/**
 * Liefert den Status der Google Photos Integration
 */
export const getIntegrationStatus = (req: Request, res: Response): void => {
    const fnLogPrefix = `[GooglePickerStatus User:${req.user?.id}]`;
    
    try {
        const status = {
            pickerApiEnabled: config.googlePhotos.enablePickerApi || false,
            pickerApiConfigured: !!(config.googlePhotos.clientId), // Nur Client ID nötig
            libraryApiDeprecated: true, // Seit April 2025
            recommendedApproach: 'Google Photos Picker API',
            migrationDate: '2025-04-01',
            features: {
                photoSelection: config.googlePhotos.enablePickerApi || false,
                dateRangeFilter: false, // Picker API unterstützt keine Datumsfilterung
                bulkDownload: false,
                albumAccess: config.googlePhotos.enablePickerApi || false
            }
        };

        log.info(`${fnLogPrefix} Integration Status abgerufen`);
        res.json(status);
    } catch (error: any) {
        log.error(`${fnLogPrefix} Fehler beim Abrufen des Integration Status:`, error);
        res.status(500).json({ error: 'Interner Serverfehler' });
    }
};
