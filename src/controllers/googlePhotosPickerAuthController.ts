// src/controllers/googlePhotosPickerAuthController.ts
import { Request, Response } from 'express';
import config from '../config/config';
import logger from '../utils/logger';
import * as userRepository from '../db/userRepository';

const log = logger.getLogger(__filename);

/**
 * Startet den Google Photos Picker OAuth-Flow
 */
export const startGooglePhotosPickerAuth = (req: Request, res: Response): void => {
    const fnLogPrefix = `[GooglePhotosPickerAuth Start User:${req.user?.id}]`;
    
    try {
        if (!req.user) {
            res.status(401).json({ error: 'Nicht authentifiziert' });
            return;
        }

        // Erstelle OAuth-URL für Google Photos Picker
        const authUrl = new URL(config.googlePhotos.authUrl);
        authUrl.searchParams.set('client_id', config.googlePhotos.clientId);
        authUrl.searchParams.set('redirect_uri', `${config.server.baseUrl}/auth/google-photos-picker/callback`);
        authUrl.searchParams.set('response_type', 'code');
        authUrl.searchParams.set('scope', 'https://www.googleapis.com/auth/photospicker.mediaitems.readonly');
        authUrl.searchParams.set('access_type', 'offline');
        authUrl.searchParams.set('prompt', 'consent');
        authUrl.searchParams.set('state', `user_${req.user.id}`);

        log.info(`${fnLogPrefix} Redirecting to Google Photos Picker OAuth: ${authUrl.toString()}`);
        res.redirect(authUrl.toString());

    } catch (error: any) {
        log.error(`${fnLogPrefix} Error starting Google Photos Picker auth:`, error);
        res.status(500).json({ error: 'Fehler beim Starten der Authentifizierung' });
    }
};

/**
 * Behandelt den OAuth-Callback von Google Photos Picker
 */
export const handleGooglePhotosPickerCallback = async (req: Request, res: Response): Promise<void> => {
    const fnLogPrefix = `[GooglePhotosPickerAuth Callback]`;
    const { code, state, error } = req.query;

    try {
        if (error) {
            log.error(`${fnLogPrefix} OAuth error from Google:`, error);
            res.redirect('/user/settings?error=google_photos_picker_auth_failed');
            return;
        }

        if (!code || !state) {
            log.error(`${fnLogPrefix} Missing code or state parameter`);
            res.redirect('/user/settings?error=google_photos_picker_missing_params');
            return;
        }

        // Extrahiere User ID aus state
        const stateMatch = (state as string).match(/^user_(\d+)$/);
        if (!stateMatch) {
            log.error(`${fnLogPrefix} Invalid state parameter: ${state}`);
            res.redirect('/user/settings?error=google_photos_picker_invalid_state');
            return;
        }

        const userId = parseInt(stateMatch[1], 10);
        log.info(`${fnLogPrefix} Processing callback for user ${userId}`);

        // Tausche Authorization Code gegen Access Token
        const tokenResponse = await fetch(config.googlePhotos.tokenUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                client_id: config.googlePhotos.clientId,
                client_secret: config.googlePhotos.clientSecret,
                code: code as string,
                grant_type: 'authorization_code',
                redirect_uri: `${config.server.baseUrl}/auth/google-photos-picker/callback`,
            }),
        });

        if (!tokenResponse.ok) {
            const errorText = await tokenResponse.text();
            log.error(`${fnLogPrefix} Token exchange failed: ${tokenResponse.status} ${errorText}`);
            res.redirect('/user/settings?error=google_photos_picker_token_failed');
            return;
        }

        const tokenData = await tokenResponse.json();
        log.info(`${fnLogPrefix} Token exchange successful for user ${userId}`);

        // Speichere Tokens in der Datenbank
        const expiresAt = tokenData.expires_in ? Date.now() + (tokenData.expires_in * 1000) : null;
        
        await userRepository.updateUserGooglePhotosPickerTokens(userId, {
            accessToken: tokenData.access_token,
            refreshToken: tokenData.refresh_token || null,
            expiresAt: expiresAt,
            scope: tokenData.scope || 'https://www.googleapis.com/auth/photospicker.mediaitems.readonly'
        });

        log.info(`${fnLogPrefix} Google Photos Picker tokens saved for user ${userId}`);
        res.redirect('/user/settings?success=google_photos_picker_connected');

    } catch (error: any) {
        log.error(`${fnLogPrefix} Error handling Google Photos Picker callback:`, error);
        res.redirect('/user/settings?error=google_photos_picker_callback_error');
    }
};

/**
 * Trennt die Google Photos Picker Verbindung
 */
export const disconnectGooglePhotosPicker = async (req: Request, res: Response): Promise<void> => {
    const fnLogPrefix = `[GooglePhotosPickerAuth Disconnect User:${req.user?.id}]`;

    try {
        if (!req.user) {
            res.status(401).json({ error: 'Nicht authentifiziert' });
            return;
        }

        // Lösche Tokens aus der Datenbank
        await userRepository.clearUserGooglePhotosPickerTokens(req.user.id);

        log.info(`${fnLogPrefix} Google Photos Picker disconnected for user ${req.user.id}`);
        res.redirect('/user/settings?success=google_photos_picker_disconnected');

    } catch (error: any) {
        log.error(`${fnLogPrefix} Error disconnecting Google Photos Picker:`, error);
        res.redirect('/user/settings?error=google_photos_picker_disconnect_failed');
    }
};

/**
 * Prüft den Status der Google Photos Picker Verbindung
 */
export const getGooglePhotosPickerStatus = async (req: Request, res: Response): Promise<void> => {
    const fnLogPrefix = `[GooglePhotosPickerAuth Status User:${req.user?.id}]`;

    try {
        if (!req.user) {
            res.status(401).json({ error: 'Nicht authentifiziert' });
            return;
        }

        const tokens = await userRepository.getUserGooglePhotosPickerTokens(req.user.id);
        const isConnected = !!(tokens && tokens.accessToken);

        log.debug(`${fnLogPrefix} Google Photos Picker status: ${isConnected ? 'connected' : 'not connected'}`);

        res.json({
            connected: isConnected,
            scope: tokens?.scope || null,
            expiresAt: tokens?.expiresAt || null
        });

    } catch (error: any) {
        log.error(`${fnLogPrefix} Error getting Google Photos Picker status:`, error);
        res.status(500).json({ error: 'Fehler beim Abrufen des Status' });
    }
};
