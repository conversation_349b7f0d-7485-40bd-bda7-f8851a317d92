// src/controllers/userController.ts
import { Request, Response, NextFunction } from 'express';
import logger from '../utils/logger';
import config from '../config/config'; // Importiere die globale Konfiguration
import path from 'path';
import fs from 'fs/promises';
import * as fsExtra from 'fs-extra';
import * as userRepository from '../db/userRepository';
import * as activityRepository from '../db/activityRepository';
import * as activityService from '../services/activityService'; // Für Sync-Logik
import * as equipmentRepository from '../db/equipmentRepository'; // NEU
import * as activityEquipmentRepository from '../db/activityEquipmentRepository'; // NEU
import * as photoRepository from '../db/photoRepository';
import * as notificationRepository from '../db/notificationRepository'; // Importieren
import * as googlePhotosService from '../services/googlePhotosService'; // Importieren
import * as googleDriveService from '../services/googleDriveService'; // Importieren für Google Drive
import * as imageService from '../services/imageService'; // Importieren
import * as userConnectionRepository from '../db/userConnectionRepository'; // Für Freundschaftsverbindungen
import * as sportGroupRepository from '../db/sportGroupRepository'; // Für benutzerdefinierte Sportart-Obergruppen
import * as sportGroupService from '../services/sportGroupService'; // NEU: Sportart-Obergruppen
import * as mapLayerStyleRepository from '../db/mapLayerStyleRepository'; // NEU: Karten-Layer-Stile
import * as plannedRouteRepository from '../db/plannedRouteRepository'; // Für geplante Routen
import * as plannedRouteImageRepository from '../db/plannedRouteImageRepository';
import * as plannedRouteUrlRepository from '../db/plannedRouteUrlRepository';
import * as plannedRouteService from '../services/plannedRouteService';
import * as poiRepository from '../db/poiRepository';
import * as poiImageRepository from '../db/poiImageRepository';
import * as poiUrlRepository from '../db/poiUrlRepository';
import * as imageDownloadService from '../services/imageDownloadService';
import komootService from '../services/komootService'; // Für Komoot-Funktionen
import { ResultSetHeader } from 'mysql2';


import { Equipment } from '../types/equipment'; // NEU
import { ActivityUserListFilters, ActivityListOptions, ActivityUserListOptions } from '../types/activity'; // Importieren
import { MediaItem, Album } from '../types/googlePhotos';
import { NotificationType } from '../types/notification'; // Für Benachrichtigungen
import { StylesMap, StyleObject, ActivityTypeColors, MapLayerStyle, DefaultStyleProperties } from '../types/activity_styles';
import { PlannedRoute, PlannedRouteListFilters, PlannedRouteListOptions } from '../types/plannedRoute';
import { POI, POIImage, POIUrl } from '../types/poi';

import { SyncResult, UpdateSingleActivityResult } from '../types/serviceResults'; // UpdateSingleActivityResult ggf. auch hier verwenden

import { haversineDistance, calculateGradient } from '../utils/geoGpxUtils';
import {
    Activity, ActivityForDb, ActivityFromDB, ActivityCheckData, ActivityPiData, ElevationAnchorData, ActivityForGpx,
    ActivityForPopup, MinimalActivityGeoJson, ActivityForListing, ActivitySummary,
    UnrealisticSpeedActivity, ActivityDataForGeoJsonFeature, UserYearlySummaryStat,
    ActivityListFilters, ElevationPoint, VerticalAnchor
} from '../types/activity';
import { ActivityForUserListing } from '../types/activity';
import { AdminActivityListOptions } from '../types/activity';
import { RowDataPacket } from 'mysql2';
import { StravaActivityPayload } from '../types/strava';

const log = logger.getLogger(__filename);

interface RawClientPhotoData {
    id: string;
    baseUrl: string;
    filename: string;
    description: string | null;
    mediaMetadata: {
        creationTime: string; // Erwartet als String vom Client-JSON
        width?: string | number;
        height?: string | number;
    }
}

/**
 * Handle manual resync of an activity
 */
export const handleManualResyncActivity = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const activityDbId = parseInt(req.params.activityDbId, 10);
    const userId = req.user?.id;
    const fnLogPrefix = `[handleManualResyncActivity User:${userId} activityID:${activityDbId}]`;

    if (!userId) {
        log.warn(`${fnLogPrefix} No user ID found in request`);
        res.status(401).json({ success: false, message: 'Nicht angemeldet.' });
        return;
    }

    if (isNaN(activityDbId)) {
        log.warn(`${fnLogPrefix} Invalid activity ID: ${req.params.id}`);
        res.status(400).json({ success: false, message: 'Ungültige Aktivitäts-ID.' });
        return;
    }

    try {
        log.info(`${fnLogPrefix} User ${userId} requested resync of activity ${activityDbId}`);

        // Get the activity from the database
        const activity = await activityRepository.getActivityByPrimaryKey(activityDbId);
        if (!activity) {
            log.warn(`${fnLogPrefix} Activity ${activityDbId} not found in database`);
            res.status(404).json({ success: false, message: 'Aktivität nicht gefunden.' });
            return;
        }

        // Check if user owns the activity
        if (activity.user_id !== userId) {
            log.warn(`${fnLogPrefix} User ${userId} attempted to resync activity owned by user ${activity.user_id}`);
            res.status(403).json({ success: false, message: 'Sie haben keine Berechtigung für diese Aktivität.' });
            return;
        }

        if (activity.strava_id === null) {
            log.warn(`${fnLogPrefix} Activity ${activityDbId} has no Strava ID, cannot resync.`);
            res.status(400).json({ success: false, message: 'Aktivität hat keine Strava-ID, kann nicht aktualisiert werden.' });
            return;
        }

        // Reset GPX status to force regeneration
        await activityRepository.setGpxStatus(activityDbId, false);
        await activityRepository.setResource_state(activityDbId, 1);

        try {
            // updateSingleActivity kümmert sich um Details, Streams, Fotos, GPX etc.
            const singleUpdateResult: UpdateSingleActivityResult = await activityService.updateSingleStravaActivity(activity.strava_id, userId, true);

            if (singleUpdateResult.success) {
                log.info(`${fnLogPrefix} Activity ${activity.strava_id} updated successfully.`);
                // ToDo Besser noch redirect
                res.json({
                    success: true,
                    message: 'Aktivität wurde aktualisiert und GPX-Datei neu generiert.'
                });
                return;
            } else {
                log.warn(`${fnLogPrefix} Fehler beim Update der Einzelaktivität ${activity.strava_id}:`, singleUpdateResult.errors);
                res.status(400).json({
                    success: false,
                    message: `Fehler beim Update: ${singleUpdateResult.errors.join(', ')}`
                });
                return;
            }
        } catch (singleActivityError: any) {
            const errorMsg = `Kritischer Fehler bei Aktivität ${activity.strava_id}: ${singleActivityError.message}`;
            log.error(`${fnLogPrefix} ${errorMsg}`, singleActivityError);
            res.status(400).json({
                success: false,
                message: `Fehler beim Update: ${singleActivityError.message || 'Unbekannter Fehler'}`
            });
            return;
            // Hier entscheiden, ob der Gesamt-Sync als fehlgeschlagen gilt oder nur diese Aktivität
        }

        // Trigger GPX generation
        /* Selbst Implementierte Version ist Fehlerhaft und wird überspungen, wird von updateSingleStravaActivityselbst gemacht
        log.info(`${fnLogPrefix} Forcing GPX generation for internal ID ${activity.id}...`);
        const gpxResult = await activityService.generateGpxForActivity(activity.id);

        if (gpxResult && gpxResult.success) {
            res.json({
                success: true,
                message: 'Aktivität wurde aktualisiert und GPX-Datei neu generiert.'
            });
        } else {
            res.json({
                success: false,
                message: 'Aktivität gefunden, aber GPX-Generierung fehlgeschlagen.'
            });
        }
        */
    } catch (error: any) {
        log.error(`${fnLogPrefix} Error during manual resync:`, error);
        res.status(500).json({
            success: false,
            message: `Fehler beim Update: ${error.message || 'Unbekannter Fehler'}`
        });
    }
};

export const handleUnlinkPhotoFromActivityApi = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const loggedInUserId = res.locals.currentUser.id;
    const photoDbId = parseInt(req.params.photoDbId, 10);
    const activityStravaIdFromBody = req.body.activity_strava_id; // Zur zusätzlichen Prüfung
    const fnLogPrefix = `[API UnlinkPhoto User:${loggedInUserId} PhotoDB:${photoDbId}]`;

    if (isNaN(photoDbId)) {
        res.status(400).json({ success: false, message: "Ungültige Foto-Datenbank-ID." });
        return;
    }

    try {
        const photoEntry = await photoRepository.getPhotoByDbId(photoDbId);
        if (!photoEntry) {
            res.status(404).json({ success: false, message: "Fotoeintrag nicht gefunden." });
            return;
        }

        // Lade die Aktivität, zu der das Foto gehört, anhand der photoEntry.activity_id
        const activityOfPhoto = await activityRepository.getActivityByPrimaryKey(photoEntry.activity_id);
        if (!activityOfPhoto || activityOfPhoto.user_id !== loggedInUserId) {
            log.warn(`${fnLogPrefix} User ${loggedInUserId} is not the owner of activity for photo ${photoDbId}. Unlink denied.`);
            res.status(403).json({ success: false, message: "Zugriff verweigert. Sie sind nicht der Besitzer der Aktivität." });
            return;
        }

        // Zusätzliche Prüfung, ob die activityStravaId aus dem Body mit der Strava ID der Aktivität des Fotos übereinstimmt
        if (activityStravaIdFromBody && String(activityStravaIdFromBody) !== String(activityOfPhoto.strava_id)) {
            log.warn(`${fnLogPrefix} Mismatch between activityStravaId from body (${activityStravaIdFromBody}) and photo's activity strava_id (${activityOfPhoto.strava_id}).`);
            res.status(400).json({ success: false, message: "Inkonsistente Aktivitäts-IDs." });
            return;
        }

        const deleted = await photoRepository.deleteActivityPhotoById(photoDbId);
        if (deleted) {
            if (activityOfPhoto.strava_id) { // Nur dekrementieren, wenn Strava ID vorhanden
                await activityRepository.decrementActivityPhotoCount(activityOfPhoto.strava_id);
            }
            log.info(`${fnLogPrefix} Photo link (DB ID: ${photoDbId}) for activity ${activityOfPhoto.id} (Strava: ${activityOfPhoto.strava_id}) successfully removed.`);
            res.json({ success: true, message: "Verknüpfung zum Foto erfolgreich gelöscht." });
        } else {
            log.warn(`${fnLogPrefix} Failed to delete photo link (DB ID: ${photoDbId}) from database.`);
            res.status(500).json({ success: false, message: "Fehler beim Löschen der Fotoverknüpfung aus der Datenbank." });
        }
    } catch (error: any) {
        log.error(`${fnLogPrefix} Error unlinking photo:`, error);
        next(error);
    }
};


/**
 * Verknüpft ausgewählte Google Photos mit einer Aktivität.
 * Erwartet activityDbId im Pfad und ein Array von Google Photo Objekten (MediaItem) im Body.
 * POST /user/api/activity/:activityDbId/link-google-photos
 */
export const linkGooglePhotosToActivityApi = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const loggedInUserId = res.locals.currentUser.id;
    const activityDbId = parseInt(req.params.activityDbId, 10);
    // Erwartet ein Array von MediaItem-Objekten, die der Client aus der Google Photos Suche ausgewählt hat
    // Hier kommen die Daten als Strings für Zeitstempel an
    const photosFromClientRaw = req.body.photos as RawClientPhotoData[];

    const fnLogPrefix = `[API LinkGPhotos User:${loggedInUserId} ActDB:${activityDbId}]`;

    if (isNaN(activityDbId)) {
        res.status(400).json({ success: false, message: "Ungültige Aktivitäts-ID." });
        return;
    }
    if (!Array.isArray(photosFromClientRaw) || photosFromClientRaw.length === 0) {
        res.status(400).json({ success: false, message: "Keine Fotos zum Verknüpfen ausgewählt." });
        return;
    }

    try {
        const activity = await activityRepository.getActivityByPrimaryKey(activityDbId);
        if (!activity || activity.user_id !== loggedInUserId) {
            log.warn(`${fnLogPrefix} Activity not found or not owned by user.`);
            res.status(403).json({ success: false, message: "Aktivität nicht gefunden oder Zugriff verweigert." });
            return;
        }
        if (!activity.strava_id) { // activity_photos verwendet activity_strava_id
            log.warn(`${fnLogPrefix} Activity ${activityDbId} has no strava_id, cannot link photos.`);
            res.status(400).json({ success: false, message: "Aktivität hat keine Strava-ID, Fotos können nicht verknüpft werden." });
            return;
        }


        log.info(`${fnLogPrefix} Linking ${photosFromClientRaw.length} Google Photos to activity.`); // <-- photosFromClientRaw verwenden
        let linkedCount = 0;
        let errorCount = 0;
        const linkedPhotoDetails = [];

        for (const clientPhotoData of photosFromClientRaw) {
            if (!clientPhotoData || !clientPhotoData.id || !clientPhotoData.baseUrl || !clientPhotoData.filename || !clientPhotoData.mediaMetadata?.creationTime) {
                log.warn(`${fnLogPrefix} Skipping invalid Google Photo data object from client:`, clientPhotoData);
                errorCount++;
                continue;
            }

            // Konvertiere creationTime String zu Date-Objekt
            const creationTimeAsDate = new Date(clientPhotoData.mediaMetadata.creationTime);
            if (isNaN(creationTimeAsDate.getTime())) {
                log.warn(`${fnLogPrefix} Invalid creationTime string for Google Photo ${clientPhotoData.id}: ${clientPhotoData.mediaMetadata.creationTime}`);
                errorCount++;
                continue;
            }

            // Erstelle das MediaItem-Objekt, das der imageService erwartet
            const googlePhotoForService: MediaItem = { // <-- MediaItem direkt verwenden (siehe Fehler 4)
                id: clientPhotoData.id,
                baseUrl: clientPhotoData.baseUrl,
                filename: clientPhotoData.filename,
                description: clientPhotoData.description,
                productUrl: clientPhotoData.baseUrl, // Als Fallback, idealerweise vom Client oder Google API
                mimeType: 'image/jpeg', // Annahme, oder vom Client/Google API
                mediaMetadata: {
                    creationTime: creationTimeAsDate,
                    width: clientPhotoData.mediaMetadata.width ? parseInt(String(clientPhotoData.mediaMetadata.width), 10) : undefined,
                    height: clientPhotoData.mediaMetadata.height ? parseInt(String(clientPhotoData.mediaMetadata.height), 10) : undefined,
                }
            };

            const existingLink = await photoRepository.findActivityPhotoBySourceId((activity.id), googlePhotoForService.id, 'google');
            if (existingLink) {
                // ... (Logik für bereits existierenden Link)
                linkedPhotoDetails.push(existingLink); // Bereits vorhandenen Link für die Antwort verwenden
                log.info(`${fnLogPrefix} Google Photo ${googlePhotoForService.id} is already linked to activity ${activity.strava_id}. Skipping.`);
                continue;
            }

            try {
                const newLinkedPhoto = await imageService.processAndLinkGooglePhotoToActivity(
                    loggedInUserId,
                    activity.id,
                    activity.strava_id,
                    googlePhotoForService // Übergabe des Objekts mit creationTime als Date
                );
                if (newLinkedPhoto) {
                    linkedCount++;
                    linkedPhotoDetails.push(newLinkedPhoto);
                } else {
                    errorCount++;
                }
            } catch (processError: any) {
                log.error(`${fnLogPrefix} Error processing one Google Photo (${googlePhotoForService.id}): ${processError.message}`);
                errorCount++;
            }
        }
        const message = `Verknüpfung abgeschlossen: ${linkedCount} Foto(s) erfolgreich hinzugefügt. ${errorCount > 0 ? errorCount + ' Fehler.' : ''}`;
        log.info(`${fnLogPrefix} ${message}`);
        res.json({ success: true, message: message, linkedPhotos: linkedPhotoDetails, errorCount: errorCount });

    } catch (error: any) {
        log.error(`${fnLogPrefix} Error linking Google Photos:`, error);
        next(error); // An globalen Error Handler
    }
};


export const findGooglePhotosForActivityApi = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const loggedInUserId = res.locals.currentUser.id;
    const activityDbId = parseInt(req.params.activityDbId, 10);
    const pageToken = req.query.pageToken as string | undefined;
    const fnLogPrefix = `[API FindGPhotos User:${loggedInUserId} ActDB:${activityDbId}]`;

    if (isNaN(activityDbId)) {
        res.status(400).json({ message: "Ungültige Aktivitäts-ID." });
        return;
    }

    try {
        const activity = await activityRepository.getActivityByPrimaryKey(activityDbId);
        if (!activity || activity.user_id !== loggedInUserId) {
            res.status(403).json({ message: "Aktivität nicht gefunden oder Zugriff verweigert." });
            return;
        }

        if (!activity.start_date_local || !activity.elapsed_time) {
            res.status(400).json({ message: "Aktivität hat keine gültige Startzeit oder Dauer." });
            return;
        }

        // Zeitfenster für die Fotosuche (z.B. Startzeit bis Startzeit + Dauer)
        // Sie können hier Puffer hinzufügen (z.B. 1 Stunde vor/nach)
        const activityStartTime = new Date(activity.start_date_local);
        const activityEndTime = new Date(activityStartTime.getTime() + (activity.elapsed_time * 1000));

        // Optional: Puffer hinzufügen
        const bufferMilliseconds = 60 * 60 * 1000; // 1 Stunde Puffer
        const searchStartDate = new Date(activityStartTime.getTime() - bufferMilliseconds);
        const searchEndDate = new Date(activityEndTime.getTime() + bufferMilliseconds);

        log.info(`${fnLogPrefix} Searching photos between ${searchStartDate.toISOString()} and ${searchEndDate.toISOString()}`);

        const { mediaItems, nextPageToken: newPageToken } = await googlePhotosService.findPhotosByDateRangeForUser(
            loggedInUserId,
            searchStartDate,
            searchEndDate,
            20, // Page Size
            pageToken
        );

        res.json({
            photos: mediaItems,
            nextPageToken: newPageToken
        });

    } catch (error: any) {
        log.error(`${fnLogPrefix} Error finding Google Photos:`, error);

        // Spezifische Behandlung für Authentifizierungs- und Berechtigungsfehler
        if (error.message && (
            error.message.includes("Google Photos Authentifizierung fehlgeschlagen") ||
            error.message.includes("Google Photos Berechtigung unvollständig")
        )) {
            res.status(401).json({ message: error.message, needsReauth: true });
        } else {
            // Gebe die spezifische Fehlermeldung weiter, falls verfügbar
            const errorMessage = error.message || "Fehler beim Suchen von Google Photos.";
            res.status(500).json({ message: errorMessage });
        }
    }
};


export const showMyActivitiesPage = async (req: Request, res: Response, next: NextFunction) => {
    const loggedInUser = res.locals.currentUser;
    const page = parseInt(req.query.page as string, 10) || 1;
    const limit = config.activities.adminBrowserPageLimit;

    // req.query Werte können undefined (als JS-Wert) oder Strings sein (auch der String "undefined")
    const { search, type, date_from, date_to, participation, dist_min, dist_max, elev_min, elev_max, sort, order } = req.query as any;

    // Hilfsfunktion zum sicheren Parsen von Zahlen
    const parseOptionalFloat = (value: any): number | undefined => {
        if (value === undefined || value === null || String(value).trim() === '' || String(value).toLowerCase() === 'undefined') {
            return undefined;
        }
        const num = parseFloat(String(value));
        return isNaN(num) ? undefined : num;
    };

    // Hilfsfunktion zum sicheren Umgang mit Datums-Strings
    const sanitizeDateString = (dateStr: any): string | undefined => {
        if (dateStr === undefined || dateStr === null || String(dateStr).trim() === '' || String(dateStr).toLowerCase() === 'undefined') {
            return undefined;
        }
        // Optional: Weitere Validierung, ob es ein gültiges Datumsformat ist
        return String(dateStr);
    };

    const filters: ActivityUserListFilters = {
        searchTerm: (search && String(search).toLowerCase() !== 'undefined') ? String(search) : undefined,
        activityType: (type && String(type).toLowerCase() !== 'undefined') ? String(type) : undefined,
        dateFrom: sanitizeDateString(date_from),
        dateTo: sanitizeDateString(date_to),
        participation: (participation as ActivityUserListFilters['participation']) || 'all',
        distMin: parseOptionalFloat(dist_min),
        distMax: parseOptionalFloat(dist_max),
        elevMin: parseOptionalFloat(elev_min),
        elevMax: parseOptionalFloat(elev_max),
    };

    const validSortColumns = ['name', 'date', 'type', 'distance', 'elevation', 'photos'];
    const sortByValidated = validSortColumns.includes(sort as string) ? sort as string : 'date';
    const sortOrderValidated = (order as string)?.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';

    const listOptions: ActivityUserListOptions = {
        filters,
        sortBy: sortByValidated,
        sortOrder: sortOrderValidated,
        page,
        limit
    };

    const fnLogPrefix = `[Ctrl MyActivities User:${loggedInUser.username}]`;
    log.info(`${fnLogPrefix} Request for page ${page}, Filters: ${JSON.stringify(filters)}`);

    try {
        const [activities, totalActivities, activityTypesForFilter] = await Promise.all([
            activityRepository.listActivitiesForUser(loggedInUser.id, listOptions),
            activityRepository.countActivitiesForUser(loggedInUser.id, filters),
            activityRepository.getDistinctActivityTypesForUser(loggedInUser.id)
        ]);

        const totalPages = Math.ceil(totalActivities / limit);
        const currentPage = Math.max(1, Math.min(page, totalPages || 1));

        res.render('users/activities', {
            pageTitle: 'Meine Aktivitäten',
            activities: activities,
            activityTypesForFilter: activityTypesForFilter,
            pagination: { currentPage, totalPages, totalActivities, limit, queryString: req.query },
            filters: { // Für das Vorbelegen der Formularfelder, hier die Original-Query-Werte (oder Defaults) verwenden
                search: search || '', type: type || '',
                date_from: date_from && String(date_from).toLowerCase() !== 'undefined' ? date_from : '',
                date_to: date_to && String(date_to).toLowerCase() !== 'undefined' ? date_to : '',
                participation: filters.participation, // Schon korrekt behandelt
                dist_min: dist_min && String(dist_min).toLowerCase() !== 'undefined' ? dist_min : '',
                dist_max: dist_max && String(dist_max).toLowerCase() !== 'undefined' ? dist_max : '',
                elev_min: elev_min && String(elev_min).toLowerCase() !== 'undefined' ? elev_min : '',
                elev_max: elev_max && String(elev_max).toLowerCase() !== 'undefined' ? elev_max : ''
            },
            sort: { by: sortByValidated, order: sortOrderValidated }
        });

    } catch (error) {
        log.error(`${fnLogPrefix} Error:`, error);
        next(error);
    }
};


// Handler für das Löschen einer Aktivität durch den User
/**
 * Zeigt die Seite mit den geplanten Aktivitäten des Benutzers an.
 */
export const showMyPlannedActivitiesPage = async (req: Request, res: Response, next: NextFunction) => {
    const loggedInUser = res.locals.currentUser;
    const page = parseInt(req.query.page as string, 10) || 1;
    const limit = config.activities.adminBrowserPageLimit;

    // req.query Werte können undefined (als JS-Wert) oder Strings sein (auch der String "undefined")
    const { search, type, date_from, date_to, source, dist_min, dist_max, elev_min, elev_max, sort, order, sharing_status } = req.query as any;

    // Hilfsfunktion zum sicheren Parsen von Zahlen
    const parseOptionalFloat = (value: any): number | undefined => {
        if (value === undefined || value === null || String(value).trim() === '' || String(value).toLowerCase() === 'undefined') {
            return undefined;
        }
        const num = parseFloat(String(value));
        return isNaN(num) ? undefined : num;
    };

    // Hilfsfunktion zum sicheren Umgang mit Datums-Strings
    const sanitizeDateString = (dateStr: any): string | undefined => {
        if (dateStr === undefined || dateStr === null || String(dateStr).trim() === '' || String(dateStr).toLowerCase() === 'undefined') {
            return undefined;
        }
        // Optional: Weitere Validierung, ob es ein gültiges Datumsformat ist
        return String(dateStr);
    };

    const filters: PlannedRouteListFilters = {
        searchTerm: (search && String(search).toLowerCase() !== 'undefined') ? String(search) : undefined,
        sportType: (type && String(type).toLowerCase() !== 'undefined') ? String(type) : undefined,
        dateFrom: sanitizeDateString(date_from),
        dateTo: sanitizeDateString(date_to),
        source: (source && String(source).toLowerCase() !== 'undefined') ? String(source) : undefined,
        distMin: parseOptionalFloat(dist_min),
        distMax: parseOptionalFloat(dist_max),
        elevMin: parseOptionalFloat(elev_min),
        elevMax: parseOptionalFloat(elev_max),
        sharingStatus: (sharing_status && ['shared', 'not_shared'].includes(sharing_status)) ? sharing_status as 'shared' | 'not_shared' : undefined,
    };

    const validSortColumns = ['name', 'uploaded_at', 'sport_type', 'distance_m', 'elevation_gain_m', 'source', 'sharing_status'];
    const sortByValidated = validSortColumns.includes(sort as string) ? sort as string : 'uploaded_at';
    const sortOrderValidated = (order as string)?.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';

    const listOptions: PlannedRouteListOptions = {
        filters,
        sortBy: sortByValidated,
        sortOrder: sortOrderValidated,
        page,
        limit
    };

    const fnLogPrefix = `[Ctrl MyPlannedActivities User:${loggedInUser.username}]`;
    log.info(`${fnLogPrefix} Request for page ${page}, Filters: ${JSON.stringify(filters)}`);

    try {
        const [plannedRoutes, filteredPlannedRoutes, totalPlannedRoutes, sportTypesForFilter] = await Promise.all([
            plannedRouteRepository.listPlannedRoutesForUser(loggedInUser.id, listOptions),
            plannedRouteRepository.countPlannedRoutesForUser(loggedInUser.id, filters),
            plannedRouteRepository.countTotalPlannedRoutesForUser(loggedInUser.id),
            plannedRouteRepository.getDistinctSportTypesForUserPlannedRoutes(loggedInUser.id)
        ]);

        const totalPages = Math.ceil(filteredPlannedRoutes / limit);
        const currentPage = Math.max(1, Math.min(page, totalPages || 1));

        res.render('users/activities_planned', {
            pageTitle: 'Meine geplanten Aktivitäten',
            plannedRoutes: plannedRoutes,
            sportTypesForFilter: sportTypesForFilter,
            pagination: { currentPage, totalPages, filteredPlannedRoutes, totalPlannedRoutes, limit, queryString: req.query },
            filters: { // Für das Vorbelegen der Formularfelder, hier die Original-Query-Werte (oder Defaults) verwenden
                search: search || '',
                type: type || '',
                date_from: date_from && String(date_from).toLowerCase() !== 'undefined' ? date_from : '',
                date_to: date_to && String(date_to).toLowerCase() !== 'undefined' ? date_to : '',
                source: source || '',
                dist_min: dist_min && String(dist_min).toLowerCase() !== 'undefined' ? dist_min : '',
                dist_max: dist_max && String(dist_max).toLowerCase() !== 'undefined' ? dist_max : '',
                elev_min: elev_min && String(elev_min).toLowerCase() !== 'undefined' ? elev_min : '',
                elev_max: elev_max && String(elev_max).toLowerCase() !== 'undefined' ? elev_max : '',
                sharing_status: sharing_status || ''
            },
            sort: { by: sortByValidated, order: sortOrderValidated }
        });

    } catch (error) {
        log.error(`${fnLogPrefix} Error:`, error);
        next(error);
    }
};

/**
 * Zeigt die Detailseite einer geplanten Aktivität an.
 */
export const showPlannedRouteDetailPage = async (req: Request, res: Response, next: NextFunction) => {
    const loggedInUser = res.locals.currentUser;
    const plannedRouteId = parseInt(req.params.plannedRouteId, 10);
    const fnLogPrefix = `[Ctrl PlannedRouteDetail User:${loggedInUser.username} RouteID:${plannedRouteId}]`;

    if (isNaN(plannedRouteId)) {
        req.flash('error', 'Ungültige Routen-ID.');
        return res.redirect('/user/activities/planned');
    }

    try {
        const plannedRoute = await plannedRouteRepository.getPlannedRouteById(plannedRouteId);

        if (!plannedRoute) {
            req.flash('error', 'Geplante Route nicht gefunden.');
            return res.redirect('/user/activities/planned');
        }

        if (plannedRoute.user_id !== loggedInUser.id) {
            log.warn(`${fnLogPrefix} User attempted to view planned route not owned by them.`);
            req.flash('error', 'Zugriff verweigert. Sie können nur eigene geplante Routen ansehen.');
            return res.redirect('/user/activities/planned');
        }

        // Bestimme die Quelle der Route
        let source = 'gpx_upload';
        if (plannedRoute.komoot_id) {
            source = 'komoot';
        } else if (plannedRoute.gpx_filename.startsWith('google_drive_')) {
            source = 'google_drive';
        } else if (plannedRoute.gpx_filename.startsWith('url_import_')) {
            source = 'url_scraper';
        }

        // Lade zusätzliche Daten (Bilder und URLs)
        const [images, urls] = await Promise.all([
            plannedRouteImageRepository.getImagesByPlannedRouteId(plannedRouteId),
            plannedRouteUrlRepository.getUrlsByPlannedRouteId(plannedRouteId)
        ]);

        // Konvertiere die geplante Route in ein Activity-ähnliches Format für das Template
        const activityForTemplate = {
            id: plannedRoute.id,
            activity_name: plannedRoute.name || 'Unbenannte Route',
            sport_type: plannedRoute.sport_type || 'Unbekannt',
            distance: plannedRoute.distance_m,
            total_elevation_gain: plannedRoute.elevation_gain_m,
            moving_time: plannedRoute.moving_time,
            start_lat: null,
            start_lng: null,
            gpx_filename: plannedRoute.gpx_filename,
            is_planned_route: true // Flag, um zu kennzeichnen, dass es sich um eine geplante Route handelt
        };

        res.render('users/activity_planned_detail', {
            pageTitle: `Geplante Route: ${plannedRoute.name || 'Unbenannt'}`,
            plannedRoute: plannedRoute,
            activity: activityForTemplate, // Für das activity_map_with_charts.ejs Template
            source: source,
            isOwner: true,
            images: images,
            urls: urls
        });

    } catch (error) {
        log.error(`${fnLogPrefix} Error:`, error);
        next(error);
    }
};

/**
 * Löscht eine geplante Route.
 */
export const handleDeletePlannedRoute = async (req: Request, res: Response, next: NextFunction) => {
    const loggedInUserId = res.locals.currentUser.id;
    const plannedRouteId = parseInt(req.params.plannedRouteId, 10);
    const fnLogPrefix = `[Ctrl DeletePlannedRoute User:${loggedInUserId} RouteID:${plannedRouteId}]`;

    if (isNaN(plannedRouteId)) {
        req.flash('error', 'Ungültige Routen-ID.');
        return res.redirect('/user/activities/planned');
    }

    try {
        const routeToDelete = await plannedRouteRepository.getPlannedRouteById(plannedRouteId);

        if (!routeToDelete) {
            req.flash('error', 'Geplante Route nicht gefunden.');
            return res.redirect('/user/activities/planned');
        }

        if (routeToDelete.user_id !== loggedInUserId) {
            log.warn(`${fnLogPrefix} User attempted to delete planned route not owned by them.`);
            req.flash('error', 'Zugriff verweigert. Sie können nur eigene geplante Routen löschen.');
            return res.redirect('/user/activities/planned');
        }

        // 1. Lade alle zugehörigen Daten vor dem Löschen
        const [images, urls] = await Promise.all([
            plannedRouteImageRepository.getImagesByPlannedRouteId(plannedRouteId),
            plannedRouteUrlRepository.getUrlsByPlannedRouteId(plannedRouteId)
        ]);

        // 2. GPX-Datei löschen (korrekte Pfadstruktur für geplante Routen)
        const gpxFilePath = path.join(config.paths.gpxBaseDir, config.paths.plannedGpxSubDir, routeToDelete.gpx_filename);
        try {
            await fs.unlink(gpxFilePath);
            log.info(`${fnLogPrefix} Deleted GPX file: ${gpxFilePath}`);
        } catch (fileError: any) {
            // Wenn die Datei nicht existiert, ist das kein kritischer Fehler
            if (fileError.code !== 'ENOENT') {
                log.warn(`${fnLogPrefix} Error deleting GPX file: ${fileError.message}`);
            }
        }

        // 3. Bilder und Bildverzeichnis löschen
        if (images && images.length > 0) {
            const imageDir = path.join(process.cwd(), 'public', 'uploads', 'planned_routes', String(plannedRouteId));

            for (const image of images) {
                try {
                    const imagePath = path.join(imageDir, image.filename);
                    await fs.unlink(imagePath);
                    log.info(`${fnLogPrefix} Deleted image file: ${imagePath}`);
                } catch (error: any) {
                    if (error.code !== 'ENOENT') {
                        log.warn(`${fnLogPrefix} Could not delete image file ${image.filename}:`, error.message);
                    }
                }
            }

            // Versuche das leere Verzeichnis zu löschen
            try {
                await fs.rmdir(imageDir);
                log.info(`${fnLogPrefix} Deleted image directory: ${imageDir}`);
            } catch (error: any) {
                if (error.code !== 'ENOENT') {
                    log.warn(`${fnLogPrefix} Could not delete image directory ${imageDir}:`, error.message);
                }
            }
        }

        // Wenn die Route aus Komoot importiert wurde, setze das is_imported Flag zurück
        if (routeToDelete.komoot_id) {
            try {
                await komootService.resetImportedFlag(routeToDelete.komoot_id);
                log.info(`${fnLogPrefix} Reset is_imported flag for Komoot tour with ID ${routeToDelete.komoot_id}`);
            } catch (error) {
                log.warn(`${fnLogPrefix} Failed to reset is_imported flag for Komoot tour with ID ${routeToDelete.komoot_id}: ${error}`);
                // Wir wollen nicht, dass der gesamte Löschvorgang fehlschlägt, wenn nur das Flag nicht zurückgesetzt werden kann
            }
        }

        // Datenbankeintrag löschen
        const deleted = await plannedRouteRepository.deletePlannedRoute(plannedRouteId);

        if (deleted) {
            log.info(`${fnLogPrefix} Successfully deleted planned route with ID ${plannedRouteId}`);
            req.flash('success', 'Geplante Route erfolgreich gelöscht.');
        } else {
            log.warn(`${fnLogPrefix} Failed to delete planned route with ID ${plannedRouteId} from database.`);
            req.flash('error', 'Fehler beim Löschen der geplanten Route aus der Datenbank.');
        }

        return res.redirect('/user/activities/planned');

    } catch (error: any) {
        log.error(`${fnLogPrefix} Error:`, error);
        req.flash('error', `Fehler beim Löschen der geplanten Route: ${error.message}`);
        return res.redirect('/user/activities/planned');
    }
};

export const handleDeleteMyActivity = async (req: Request, res: Response, next: NextFunction) => {
    const loggedInUserId = res.locals.currentUser.id;
    const activityDbId = parseInt(req.params.activityDbId, 10);
    const fnLogPrefix = `[Ctrl DeleteMyActivity User:${loggedInUserId} ActDB:${activityDbId}]`;

    if (isNaN(activityDbId)) {
        req.flash('error', 'Ungültige Aktivitäts-ID.');
        return res.redirect('/user/activities');
    }

    try {
        const activityToDelete = await activityRepository.getActivityByPrimaryKey(activityDbId);
        if (!activityToDelete) {
            req.flash('error', 'Aktivität nicht gefunden.');
            return res.redirect('/user/my-activities');
        }
        if (activityToDelete.user_id !== loggedInUserId) {
            log.warn(`${fnLogPrefix} User attempted to delete activity not owned by them.`);
            req.flash('error', 'Zugriff verweigert. Sie können nur eigene Aktivitäten löschen.');
            return res.redirect('/user/my-activities');
        }

        // Hier die Logik zum Löschen von Fotos und GPX-Dateien, ähnlich wie im adminController.deleteActivity
        // (Diese Logik sollte idealerweise in einen activityService ausgelagert werden)
        log.info(`${fnLogPrefix} Deleting activity files and DB entry for activity owned by user.`);
        // 1. Fotos löschen (Dateien + DB-Einträge in activity_photos)
        if (activityToDelete.strava_id) {
            const photosInDb = await photoRepository.getPhotosForActivity(activityDbId);
            if (photosInDb.length > 0) {
                // Dateipfade erstellen und löschen
                const photoBaseUploadDir = config.paths.imageBaseUploadPath;
                for (const photo of photosInDb) {
                    // Der Pfad wird mit der internen activity_id des Fotos gebildet
                    const activityPhotoDirForFile = path.join(photoBaseUploadDir, String(photo.activity_id));

                    if (photo.computed_filename_original) {
                        await fs.unlink(path.join(activityPhotoDirForFile, photo.computed_filename_original)).catch(e => { if (e.code !== 'ENOENT') log.warn(`Failed to delete photo file: ${e.message}`); });
                    }
                    if (photo.computed_filename_medium) {
                        await fs.unlink(path.join(activityPhotoDirForFile, photo.computed_filename_medium)).catch(e => { if (e.code !== 'ENOENT') log.warn(`Failed to delete photo file: ${e.message}`); });
                    }
                    if (photo.computed_filename_small) {
                        await fs.unlink(path.join(activityPhotoDirForFile, photo.computed_filename_small)).catch(e => { if (e.code !== 'ENOENT') log.warn(`Failed to delete photo file: ${e.message}`); });
                    }
                }
                // Einträge aus activity_photos löschen (oder DB hat ON DELETE CASCADE)
                await photoRepository.deletePhotosForActivity(activityDbId);
            }
            // Ggf. leere Foto-Ordner löschen
            const activityPhotoDir = String(activityToDelete.strava_id);
            const dirsToDelete = [
                path.join(config.paths.imageBaseUploadPath, 'original', activityPhotoDir),
                path.join(config.paths.imageBaseUploadPath, 'medium', activityPhotoDir),
                path.join(config.paths.imageBaseUploadPath, 'small', activityPhotoDir)
            ];
            for (const dir of dirsToDelete) { try { const filesInDir = await fs.readdir(dir); if (filesInDir.length === 0) await fs.rmdir(dir); } catch (e: any) { if (e.code !== 'ENOENT') log.warn(`Failed to rmdir ${dir}: ${e.message}`); } }

        }

        // 2. GPX-Datei löschen
        if (activityToDelete.gpx === 1) {
            // Für Strava-Aktivitäten wird die strava_id als Dateiname verwendet
            // Für hochgeladene Aktivitäten wird die interne ID als Dateiname verwendet
            const possibleGpxFilePaths = [
                // Strava-Aktivität
                activityToDelete.strava_id ? path.join(config.paths.gpxBaseDir, `${activityToDelete.strava_id}.gpx`) : null,
                // Hochgeladene Aktivität
                path.join(config.paths.gpxBaseDir, `${activityDbId}.gpx`)
            ].filter(Boolean) as string[];

            for (const gpxFilePath of possibleGpxFilePaths) {
                try {
                    await fs.unlink(gpxFilePath);
                    log.info(`${fnLogPrefix} GPX file ${gpxFilePath} deleted.`);
                } catch (e: any) {
                    if (e.code !== 'ENOENT') {
                        log.warn(`${fnLogPrefix} Failed to delete GPX file ${gpxFilePath}: ${e.message}`);
                    }
                }
            }
        }

        // Lösche den Haupt-Fotoordner der Aktivität, wenn er leer ist
        const activityPhotoMainDir = path.join(config.paths.imageBaseUploadPath, String(activityDbId));
        try {
            const items = await fs.readdir(activityPhotoMainDir);
            if (items.length === 0) {
                await fs.rmdir(activityPhotoMainDir);
                log.info(`${fnLogPrefix} Removed empty activity photo directory: ${activityPhotoMainDir}`);
            }
        } catch (e: any) {
            if (e.code !== 'ENOENT') log.warn(`Could not check/remove activity photo directory ${activityPhotoMainDir}: ${e.message}`);
        }

        // 3. Alle Einträge in shared_activities löschen, die diese Aktivität referenzieren (als owner oder als geteilte)
        await activityRepository.deleteSharedEntriesForActivity(activityDbId);

        // 3.1 Wenn die Aktivität aus Google Drive importiert wurde, lösche den Eintrag in der google_drive_imports-Tabelle
        try {
            const googleDriveImportRepository = await import('../db/googleDriveImportRepository');
            await googleDriveImportRepository.deleteGoogleDriveImport(loggedInUserId, activityDbId);
        } catch (error) {
            log.warn(`${fnLogPrefix} Error deleting Google Drive import record: ${error}`);
            // Wir wollen nicht, dass der gesamte Löschvorgang fehlschlägt, wenn nur der Import-Eintrag nicht gelöscht werden kann
        }

        // Wenn die Aktivität aus Komoot importiert wurde, setze das is_imported Flag zurück
        if (activityToDelete.komoot_id) {
            try {
                await komootService.resetImportedFlag(activityToDelete.komoot_id);
                log.info(`${fnLogPrefix} Reset is_imported flag for Komoot tour with ID ${activityToDelete.komoot_id}`);
            } catch (error) {
                log.warn(`${fnLogPrefix} Failed to reset is_imported flag for Komoot tour with ID ${activityToDelete.komoot_id}: ${error}`);
                // Wir wollen nicht, dass der gesamte Löschvorgang fehlschlägt, wenn nur das Flag nicht zurückgesetzt werden kann
            }
        }

        // 4. Aktivität aus activities Tabelle löschen
        const deleted = await activityRepository.deleteActivityById(activityDbId);

        if (deleted) {
            log.info(`${fnLogPrefix} Activity ${activityDbId} deleted successfully by owner.`);
            req.flash('success', 'Aktivität erfolgreich gelöscht.');
            res.redirect('/user/my-activities');
        } else {
            req.flash('error', 'Aktivität konnte nicht gelöscht werden.');
            res.redirect('/user/my-activities');
        }
    } catch (error: any) {
        log.error(`${fnLogPrefix} Error deleting activity:`, error);
        next(error);
    }
};


export const showUserActivityDetailPage = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const userId = res.locals.currentUser.id; // ID des eingeloggten Benutzers
    const activityDbId = parseInt(req.params.activityDbId, 10);
    const fnLogPrefix = `[Ctrl ShowUserActivityDetail User:${userId} ActDB:${activityDbId}]`;

    if (isNaN(activityDbId)) {
        res.status(400).render('error', { pageTitle: 'Fehler', message: 'Ungültige Aktivitäts-ID.', statusCode: 400 });
        return;
    }

    try {
        const activity = await activityRepository.getActivityByPrimaryKey(activityDbId);

        // Berechtigungsprüfung:
        // User muss entweder Besitzer sein oder die Aktivität wurde mit ihm geteilt.
        // Für das Bearbeiten der Ausrüstung sollte er aber der Besitzer sein.
        let isOwner = false;
        let canView = false;

        if (activity) {
            if (activity.user_id === userId) {
                isOwner = true;
                canView = true;
            } else {
                // Prüfen, ob die Aktivität mit dem User geteilt wurde (nur Ansicht, keine Ausrüstungsänderung)
                const isShared = await activityRepository.isActivitySharedWithUser(activityDbId, userId);
                if (isShared) {
                    canView = true;
                }
            }
        }

        if (!activity || !canView) {
            log.warn(`${fnLogPrefix} Activity not found, not owned, or not shared with user.`);
            res.status(404).render('error', { pageTitle: 'Nicht gefunden', message: 'Aktivität nicht gefunden oder Zugriff verweigert.', statusCode: 404 });
            return;
        }

        // Nur der Besitzer kann Ausrüstung bearbeiten
        let userEquipment: Equipment[] = [];
        let linkedEquipmentDetails: Equipment[] = []; // Equipment-Objekte, die verknüpft sind
        let linkedEquipmentIds: number[] = [];     // Nur die IDs für die Checkboxen

        if (isOwner) {
            userEquipment = await equipmentRepository.getEquipmentForUser(userId);
        }
        // Details der verknüpften Ausrüstung immer laden, um sie anzuzeigen
        linkedEquipmentDetails = await activityEquipmentRepository.getEquipmentForActivity(activityDbId);
        linkedEquipmentIds = linkedEquipmentDetails.map(eq => eq.id as number); // Sicherstellen, dass ID eine Zahl ist


        log.info(`${fnLogPrefix} Rendering activity detail page. Owner: ${isOwner}`);
        res.render('users/activity', { // Pfad zu Ihrer EJS-Datei
            pageTitle: `Details: ${activity.activity_name || `Aktivität #${activity.strava_id || activity.id}`}`,
            activity: activity,
            userEquipment: userEquipment,          // Komplette Ausrüstungsliste des Besitzers (nur wenn isOwner)
            linkedEquipmentIds: linkedEquipmentIds,  // IDs der aktuell verknüpften Ausrüstung
            linkedEquipmentDetails: linkedEquipmentDetails, // Details der verknüpften Ausrüstung für die Anzeige
            isOwner: isOwner                       // Flag für die View, um Bearbeitungselemente anzuzeigen
        });

    } catch (error) {
        log.error(`${fnLogPrefix} Error rendering user activity detail page:`, error);
        next(error);
    }
};

export const handleLinkEquipmentToActivity = async (req: Request, res: Response, next: NextFunction): Promise<void> => { // Rückgabetyp ist Promise<void>
    const userId = res.locals.currentUser.id;
    const activityDbId = parseInt(req.params.activityDbId, 10);
    const { equipmentIds, notes } = req.body;
    const fnLogPrefix = `[Ctrl LinkEquipToAct User:${userId} Act:${activityDbId}]`;

    if (isNaN(activityDbId)) {
        res.status(400).json({ message: "Ungültige Aktivitäts-ID." });
        return; // Wichtig
    }

    try {
        const activity = await activityRepository.getActivityByPrimaryKey(activityDbId);
        if (!activity || activity.user_id !== userId) {
            log.warn(`${fnLogPrefix} Activity not found or not owned by user for equipment linking.`);
            // Passenden Redirect oder Fehler für den User
            req.flash('error', 'Aktivität nicht gefunden oder Zugriff verweigert.');
            return res.redirect(req.headers.referer || '/user/my-activities');
        }

        const currentLinkedIds = await activityEquipmentRepository.getLinkedEquipmentIdsForActivity(activityDbId);
        const newSelectedIds = (Array.isArray(equipmentIds) ? equipmentIds : (equipmentIds ? [equipmentIds] : []))
            .map(id => parseInt(id, 10)).filter(id => !isNaN(id));

        const idsToUnlink = currentLinkedIds.filter(id => !newSelectedIds.includes(id));
        const idsToLink = newSelectedIds.filter(id => !currentLinkedIds.includes(id));
        // IDs, deren Notizen aktualisiert werden müssen (sind schon gelinkt und bleiben gelinkt)
        const idsToUpdateNotes = newSelectedIds.filter(id => currentLinkedIds.includes(id));


        for (const equipId of idsToUnlink) {
            await activityEquipmentRepository.unlinkEquipmentFromActivity(activityDbId, equipId, userId);
        }

        for (const equipId of idsToLink) {
            const itemNote = (notes && typeof notes === 'object' && notes[String(equipId)]) ? notes[String(equipId)] : null;
            await activityEquipmentRepository.linkEquipmentToActivity(activityDbId, equipId, userId, itemNote);
        }

        for (const equipId of idsToUpdateNotes) {
            const itemNote = (notes && typeof notes === 'object' && notes[String(equipId)]) ? notes[String(equipId)] : null;
            // Hier bräuchten wir eine Update-Funktion, wenn linkEquipmentToActivity (mit ON DUPLICATE KEY UPDATE) nicht reicht
            // oder wenn Notizen explizit gelöscht werden sollen, wenn das Textfeld leer ist.
            // Fürs Erste nehmen wir an, linkEquipmentToActivity mit ON DUPLICATE KEY UPDATE notes = VALUES(notes) ist ausreichend.
            // Wenn eine Notiz explizit zu NULL werden soll, wenn das Feld leer ist:
            await activityEquipmentRepository.linkEquipmentToActivity(activityDbId, equipId, userId, itemNote || null);
        }

        log.info(`${fnLogPrefix} Equipment linking updated.`);
        req.flash('success', 'Ausrüstung erfolgreich aktualisiert.');
        res.redirect(`/user/activity/${activityDbId}/edit`);
        // Kein return; nötig, da es die letzte Anweisung ist.

    } catch (error: any) {
        log.error(`${fnLogPrefix} Error linking equipment:`, error);
        let userErrorMessage = "Fehler beim Speichern der Ausrüstung";
        if (error instanceof Error) {
            userErrorMessage += `: ${error.message}`;
        }
        req.flash('error', userErrorMessage);
        res.redirect(`/user/activity/${activityDbId}/edit`);
    }
};


// === Dashboard & Allgemeine User-Seiten ===
export const showEditActivityPage = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const loggedInUserId = res.locals.currentUser.id;
    const activityDbId = parseInt(req.params.activityDbId, 10); // Dies IST die interne DB ID der Aktivität
    const fnLogPrefix = `[Ctrl ShowEditActivity User:${loggedInUserId} ActDB:${activityDbId}]`;

    if (isNaN(activityDbId)) {
        log.warn(`${fnLogPrefix} Invalid activityDbId provided.`);
        res.status(400).render('error', { pageTitle: 'Fehler', message: 'Ungültige Aktivitäts-ID.', statusCode: 400, layout: 'layouts/simple_layout' });
        return;
    }

    try {
        const activity = await activityRepository.getActivityByPrimaryKey(activityDbId);

        if (!activity) {
            log.warn(`${fnLogPrefix} Activity not found.`);
            res.status(404).render('error', { pageTitle: 'Nicht gefunden', message: 'Aktivität nicht gefunden.', statusCode: 404, layout: 'layouts/simple_layout' });
            return;
        }

        // Sicherstellen, dass nur der Besitzer die Aktivität bearbeiten kann
        if (activity.user_id !== loggedInUserId) {
            log.warn(`${fnLogPrefix} User ${loggedInUserId} attempted to edit activity ${activityDbId} owned by user ${activity.user_id}. Access denied.`);
            res.status(403).render('error', { pageTitle: 'Zugriff verweigert', message: 'Sie haben keine Berechtigung, diese Aktivität zu bearbeiten.', statusCode: 403, layout: 'layouts/simple_layout' });
            return;
        }

        // Lade bereits verknüpfte Fotos für die Anzeige
        const linkedPhotos = await photoRepository.getPhotosForActivity(activityDbId);

        // Lade Ausrüstungsdaten für die Bearbeitung
        const userEquipment = await equipmentRepository.getEquipmentForUser(loggedInUserId);
        const linkedEquipmentDetails = await activityEquipmentRepository.getEquipmentForActivity(activityDbId);
        const linkedEquipmentIds = linkedEquipmentDetails.map(eq => eq.id as number);

        log.info(`${fnLogPrefix} Rendering edit page for activity '${activity.activity_name}'.`);
        res.render('users/edit_activity', {
            pageTitle: `Aktivität bearbeiten: ${activity.activity_name || 'Unbenannt'}`,
            activity: activity,
            linkedPhotos: linkedPhotos,
            userEquipment: userEquipment,
            linkedEquipmentDetails: linkedEquipmentDetails,
            linkedEquipmentIds: linkedEquipmentIds,
            pageSpecificClass: 'user-edit-activity-page',
            // currentUser und config sind über res.locals verfügbar
        });

    } catch (error) {
        log.error(`${fnLogPrefix} Error rendering edit activity page:`, error);
        next(error);
    }
};

/**
 * Handler zum Aktualisieren des Aktivitätstitels
 */
export const handleUpdateActivityTitle = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const loggedInUserId = res.locals.currentUser.id;
    const activityDbId = parseInt(req.params.activityDbId, 10);
    const newTitle = req.body.activityTitle?.trim() || null;
    const fnLogPrefix = `[Ctrl UpdateActivityTitle User:${loggedInUserId} ActDB:${activityDbId}]`;

    if (isNaN(activityDbId)) {
        log.warn(`${fnLogPrefix} Invalid activityDbId provided.`);
        req.flash('error', 'Ungültige Aktivitäts-ID.');
        res.redirect('/user/activities');
        return;
    }

    try {
        // Prüfen, ob die Aktivität existiert und dem Benutzer gehört
        const activity = await activityRepository.getActivityByPrimaryKey(activityDbId);
        if (!activity) {
            log.warn(`${fnLogPrefix} Activity not found.`);
            req.flash('error', 'Aktivität nicht gefunden.');
            res.redirect('/user/activities');
            return;
        }

        if (activity.user_id !== loggedInUserId) {
            log.warn(`${fnLogPrefix} User ${loggedInUserId} tried to update activity owned by ${activity.user_id}.`);
            req.flash('error', 'Sie haben keine Berechtigung, diese Aktivität zu bearbeiten.');
            res.redirect('/user/activities');
            return;
        }

        // Titel aktualisieren
        const result = await activityRepository.updateActivityTitle(activityDbId, newTitle);

        if (result.affectedRows > 0) {
            log.info(`${fnLogPrefix} Activity title updated successfully.`);
            req.flash('success', 'Aktivitätstitel wurde erfolgreich aktualisiert.');
        } else {
            log.warn(`${fnLogPrefix} No rows affected during title update.`);
            req.flash('warning', 'Titel konnte nicht aktualisiert werden.');
        }

        // Zurück zur Edit-Seite
        res.redirect(`/user/activity/${activityDbId}/edit`);

    } catch (error) {
        log.error(`${fnLogPrefix} Error updating activity title:`, error);
        req.flash('error', 'Fehler beim Aktualisieren des Titels.');
        res.redirect(`/user/activity/${activityDbId}/edit`);
    }
};

/**
 * Zeigt das Profil eines Freundes an.
 * GET /user/profile/:username
 */
export const showFriendProfile = async (req: Request, res: Response, next: NextFunction) => {
    const loggedInUserId = res.locals.currentUser.id;
    const friendUsername = req.params.username;
    const fnLogPrefix = `[Ctrl FriendProfile User:${loggedInUserId} Friend:${friendUsername}]`;
    log.debug(`${fnLogPrefix} Rendering friend profile.`);

    try {
        // Freund in der Datenbank suchen
        const friend = await userRepository.findUserByUsername(friendUsername);
        if (!friend) {
            log.warn(`${fnLogPrefix} Friend not found.`);
            return res.status(404).render('error', {
                pageTitle: 'Benutzer nicht gefunden',
                message: `Benutzer ${friendUsername} nicht gefunden.`,
                statusCode: 404
            });
        }

        // Prüfen, ob eine Freundschaftsverbindung besteht
        // Temporär deaktiviert, bis userConnectionRepository implementiert ist
        // const isFriend = await userConnectionRepository.checkFriendshipStatus(loggedInUserId, friend.id);
        const isFriend = true; // Temporär für Testzwecke
        if (!isFriend && !res.locals.currentUser.isAdmin) {
            log.warn(`${fnLogPrefix} No friendship connection.`);
            return res.status(403).render('error', {
                pageTitle: 'Zugriff verweigert',
                message: 'Sie haben keine Berechtigung, dieses Profil anzuzeigen.',
                statusCode: 403
            });
        }

        // Neueste Aktivitäten des Freundes laden
        const latestActivitiesOptions: ActivityUserListOptions = {
            filters: { participation: 'all' }, // Nur eigene Aktivitäten des Freundes
            sortBy: 'date',
            sortOrder: 'DESC',
            page: 1,
            limit: 5
        };

        // Aktivitäten laden
        const latestActivities = await activityRepository.listActivitiesForUser(friend.id, latestActivitiesOptions);
        const yearlySummaryData = await activityRepository.getYearlySummaryStatsForUser(friend.id);
        const availableYears = yearlySummaryData.length > 0 ? yearlySummaryData.map(entry => entry.year).sort((a, b) => b - a) : [new Date().getFullYear()];

        // Gesamtstatistiken aus yearlySummaryData berechnen
        const userStats_total = {
            totalActivities: 0,
            totalDistance: 0,
            totalElevation: 0,
            sportTypes: [] as string[]
        };

        // Statistiken für das aktuelle Jahr
        const currentYear = new Date().getFullYear();
        const userStats_current_year = {
            totalActivities: 0,
            totalDistance: 0,
            totalElevation: 0,
            sportTypes: [] as string[]
        };

        // Daten aus yearlySummaryData extrahieren
        if (yearlySummaryData.length > 0) {
            // Gesamtstatistiken
            yearlySummaryData.forEach(yearData => {
                userStats_total.totalActivities += yearData.total_count || 0;
                userStats_total.totalDistance += yearData.total_distance || 0;
                userStats_total.totalElevation += yearData.total_elevation || 0;

                // Für die sportTypes müssten wir die Aktivitätstypen separat abfragen,
                // da sie nicht direkt in den Jahresstatistiken enthalten sind

                // Statistiken für das aktuelle Jahr
                if (yearData.year === currentYear) {
                    userStats_current_year.totalActivities = yearData.total_count || 0;
                    userStats_current_year.totalDistance = yearData.total_distance || 0;
                    userStats_current_year.totalElevation = yearData.total_elevation || 0;
                }
            });
        }

        res.render('users/friend_profile', {
            pageTitle: `Profil von ${friendUsername}`,
            friend: friend,
            latestActivities: latestActivities,
            userStats_total: userStats_total,
            userStats_current_year: userStats_current_year
        });
    } catch (error) {
        log.error(`${fnLogPrefix} Error rendering friend profile:`, error);
        next(error);
    }
};

export const showUserDashboard = async (req: Request, res: Response, next: NextFunction) => {
    const loggedInUser = res.locals.currentUser;
    const fnLogPrefix = `[Ctrl ShowUserDashboard User:${loggedInUser.username}]`;
    log.info(`${fnLogPrefix} Rendering user dashboard.`);

    try {
        const userId = loggedInUser.id;
        const LATEST_ACTIVITIES_LIMIT = 5;
        const LATEST_NOTIFICATIONS_LIMIT = 3;

        // Lade die neuesten Aktivitäten des Benutzers
        const latestActivitiesOptions: ActivityUserListOptions = {
            filters: { participation: 'all' }, // Eigene und geteilte
            sortBy: 'date',
            sortOrder: 'DESC',
            page: 1,
            limit: LATEST_ACTIVITIES_LIMIT
        };
        const latestActivities = await activityRepository.listActivitiesForUser(userId, latestActivitiesOptions);

        // Lade die neuesten ungelesenen Benachrichtigungen
        // Die globale Anzahl ungelesener Benachrichtigungen (unreadNotificationsCount) kommt bereits von der Middleware
        const { notifications: latestUnreadNotifications } = await notificationRepository.getNotificationsForUser(userId, {
            limit: LATEST_NOTIFICATIONS_LIMIT,
            unreadOnly: true // Nur ungelesene für die Vorschau
        });

        // Optional: Mini-Statistiken (z.B. Aktivitäten der letzten 7 Tage)
        // Dies würde eine weitere Repository-Funktion erfordern, z.B.
        // const sevenDayStats = await activityRepository.getActivityStatsForPeriod(userId, 7);

        res.render('users/dashboard', {
            pageTitle: `Dashboard für ${loggedInUser.username}`,
            latestActivities: latestActivities,
            latestUnreadNotifications: latestUnreadNotifications,
            // sevenDayStats: sevenDayStats, // Falls implementiert
            // currentUser und config sind bereits in res.locals verfügbar
        });
    } catch (error) {
        log.error(`${fnLogPrefix} Error rendering user dashboard:`, error);
        next(error);
    }
};

export const showUserSettingsPage = async (req: Request, res: Response, next: NextFunction) => {
    const userId = res.locals.currentUser.id;
    const username = res.locals.currentUser.username;
    const fnLogPrefix = `[Ctrl UserSettings User:${userId}]`;
    log.debug(`${fnLogPrefix} Rendering user settings page for ${username}.`);

    try {
        const user = await userRepository.getFullUserDetailsByUsername(username);
        if (!user) {
            log.error(`${fnLogPrefix} User ${username} not found in DB, though session exists.`);
            req.session.destroy((err) => {
                if (err) log.error(`${fnLogPrefix} Error destroying session:`, err);
                req.flash('error', 'Benutzerkonto nicht gefunden. Bitte neu anmelden.');
                return res.redirect('/auth/login');
            });
            return;
        }

        res.render('users/settings', { // Sicherstellen, dass der Pfad zu Ihren EJS-Dateien korrekt ist
            pageTitle: 'Benutzereinstellungen',
            userData: user,
            stravaConnected: !!user.strava_access_token,
            googleConnected: !!user.google_access_token,
            googleDriveConnected: !!user.google_drive_access_token,
            komootConnected: !!user.komoot_connected,
            garminConnected: !!user.garmin_connected,
        });
    } catch (error) {
        log.error(`${fnLogPrefix} Error rendering settings page:`, error);
        next(error);
    }
};

export const handleUserSettingsUpdate = async (req: Request, res: Response, next: NextFunction) => {
    const userId = res.locals.currentUser.id;
    const fnLogPrefix = `[Ctrl UpdateUserSettings User:${userId}]`;
    log.debug(`${fnLogPrefix} Handling user settings update. Body:`, req.body);

    const {
        map_visibility,
        default_map_zoom, default_map_center_lat, default_map_center_lng,
        email, // NEU
        notify_on_friend_request_email, // NEU
        notify_on_activity_shared_email  // NEU
    } = req.body;

    let successMessages: string[] = [];
    let errorMessages: string[] = [];
    let infoMessages: string[] = [];
    let updatePerformedOverall = false;

    try {
        // Karten-Sichtbarkeit
        if (map_visibility !== undefined && (map_visibility === 'private' || map_visibility === 'public')) {
            await userRepository.updateUserMapVisibility(userId, map_visibility);
            successMessages.push("Sichtbarkeit der Karte aktualisiert.");
            updatePerformedOverall = true;
        }

        // Karten-Standardeinstellungen
        const zoom = default_map_zoom ? parseInt(default_map_zoom, 10) : null;
        const lat = default_map_center_lat ? parseFloat(default_map_center_lat) : null;
        const lng = default_map_center_lng ? parseFloat(default_map_center_lng) : null;
        if (zoom !== null || lat !== null || lng !== null) { // Nur wenn etwas geändert werden soll
            const isValidZoom = zoom === null || (!isNaN(zoom) && zoom >= 1 && zoom <= 20);
            // ... (weitere Validierungen für lat/lng)
            if (isValidZoom /* && isValidLat && isValidLng */) {
                await userRepository.updateUserMapDefaults(userId, { default_map_zoom: zoom, default_map_center_lat: lat, default_map_center_lng: lng });
                successMessages.push("Karten-Standardeinstellungen aktualisiert.");
                updatePerformedOverall = true;
            } else {
                errorMessages.push("Ungültige Werte für Karten-Standardeinstellungen.");
            }
        }

        // E-Mail aktualisieren
        if (email !== undefined) { // Erlaube auch leere E-Mail, um sie zu löschen
            try {
                await userRepository.updateUserProfile(userId, { email: email || null });
                successMessages.push("E-Mail-Adresse aktualisiert.");
                updatePerformedOverall = true;
            } catch (emailError: any) {
                errorMessages.push(`Fehler bei E-Mail-Aktualisierung: ${emailError.message}`);
            }
        }

        // Benachrichtigungseinstellungen
        // Checkboxen senden 'on' oder sind nicht im Body, wenn nicht ausgewählt.
        const notifSettingsToUpdate: Parameters<typeof userRepository.updateUserNotificationSettings>[1] = {};
        if (notify_on_friend_request_email !== undefined || req.body.hasOwnProperty('notify_on_friend_request_email_checkbox')) {
            notifSettingsToUpdate.notify_on_friend_request_email = !!notify_on_friend_request_email;
            updatePerformedOverall = true;
        }
        if (notify_on_activity_shared_email !== undefined || req.body.hasOwnProperty('notify_on_activity_shared_email_checkbox')) {
            notifSettingsToUpdate.notify_on_activity_shared_email = !!notify_on_activity_shared_email;
            updatePerformedOverall = true;
        }

        if (Object.keys(notifSettingsToUpdate).length > 0) {
            await userRepository.updateUserNotificationSettings(userId, notifSettingsToUpdate);
            successMessages.push("Benachrichtigungseinstellungen aktualisiert.");
            // updatePerformedOverall ist schon true, wenn eines der Felder da war
        }


        // Flash-Nachrichten setzen
        if (successMessages.length > 0) {
            successMessages.forEach(msg => req.flash('success', msg));
        }
        if (errorMessages.length > 0) {
            errorMessages.forEach(msg => req.flash('error', msg));
        }
        if (infoMessages.length > 0 && successMessages.length === 0 && errorMessages.length === 0) { // Nur Info, wenn keine anderen
            infoMessages.forEach(msg => req.flash('info', msg));
        }
        if (!updatePerformedOverall && errorMessages.length === 0) { // Nichts geändert, keine Fehler
            req.flash('info', "Keine Änderungen vorgenommen.");
        }

        return res.redirect('/user/settings');

    } catch (error: any) {
        log.error(`${fnLogPrefix} Error updating settings:`, error);
        let errorMessage = 'Ein unbekannter Fehler ist aufgetreten.';
        if (error instanceof Error) {
            errorMessage = error.message; // Jetzt ist der Zugriff auf .message sicher
        }
        // Alternativ, wenn Sie spezifischere Fehler vom Repository erwarten, die eine 'message'-Eigenschaft haben:
        // else if (error && typeof (error as any).message === 'string') {
        //     errorMessage = (error as any).message;
        // }
        req.flash('error', `Fehler beim Speichern der Einstellungen: ${errorMessage}`);
        return res.redirect('/user/settings');
    }
};



// === Ausrüstungsverwaltung ===
import * as sportTypeEquipmentRepository from '../db/sportTypeEquipmentRepository';
import * as sportTypeService from '../services/sportTypeService';

export const showEquipmentPage = async (req: Request, res: Response, next: NextFunction) => {
    const userId = res.locals.currentUser.id;
    const username = res.locals.currentUser.username;
    const fnLogPrefix = `[Ctrl ShowEquipment User:${userId}]`;
    log.debug(`${fnLogPrefix} Rendering equipment management page for ${username}.`);

    try {
        const equipmentList = await equipmentRepository.getEquipmentForUser(userId);
        const sportTypes = await sportTypeService.getAllSportTypes();
        const sportTypeEquipmentLinks = await sportTypeEquipmentRepository.getSportTypeEquipmentForUser(userId);

        // Statistiken für alle Ausrüstungsgegenstände abrufen
        const equipmentStats = await activityEquipmentRepository.getAllEquipmentStatsForUser(userId);

        // Erweitere die Equipment-Liste mit den Statistiken
        const equipmentListWithStats = equipmentList.map(equipment => {
            const stats = equipmentStats.get(equipment.id as number) || {
                activity_count: 0,
                total_distance: 0,
                total_duration: 0
            };

            return {
                ...equipment,
                activity_count: stats.activity_count,
                total_distance: stats.total_distance,
                total_duration: stats.total_duration
            };
        });

        // Gruppiere die Verknüpfungen nach SportType für eine bessere Anzeige
        const sportTypeEquipmentMap = new Map<string, { label: string, equipmentIds: number[] }>();

        for (const link of sportTypeEquipmentLinks) {
            if (!sportTypeEquipmentMap.has(link.sport_type)) {
                sportTypeEquipmentMap.set(link.sport_type, {
                    label: link.sport_type_label || link.sport_type,
                    equipmentIds: []
                });
            }
            sportTypeEquipmentMap.get(link.sport_type)?.equipmentIds.push(link.equipment_id);
        }

        res.render('users/equipment', { // NEUES EJS Template: views/users/equipment.ejs
            pageTitle: `Meine Ausrüstung - ${username}`,
            equipmentList: equipmentListWithStats,
            itemToEdit: null, // Für das Hinzufügen-Formular
            sportTypes: sportTypes,
            sportTypeEquipmentMap: sportTypeEquipmentMap
        });
    } catch (error) {
        log.error(`${fnLogPrefix} Error rendering equipment page:`, error);
        next(error);
    }
};

export const handleAddEquipment = async (req: Request, res: Response, next: NextFunction) => {
    const userId = res.locals.currentUser.id;
    const { name, description, type, notes } = req.body;
    const fnLogPrefix = `[Ctrl AddEquipment User:${userId}]`;

    if (!name || name.trim() === '') {
        req.flash('error', 'Name ist ein Pflichtfeld.');
        return res.redirect('/user/equipment');
    }

    try {
        const newEquipmentData: Omit<Equipment, 'id' | 'user_id' | 'created_at' | 'updated_at'> = {
            name: name.trim(),
            description: description || null,
            type: type || null,
            notes: notes || null
        };
        const newItem = await equipmentRepository.createEquipment(userId, newEquipmentData);
        if (newItem) {
            log.info(`${fnLogPrefix} Equipment '${name}' added with ID ${newItem.id}.`);
            req.flash('success', 'Ausrüstung erfolgreich hinzugefügt.');
            res.redirect('/user/equipment');
        } else {
            throw new Error('Ausrüstung konnte nicht erstellt werden.');
        }
    } catch (error: any) {
        log.error(`${fnLogPrefix} Error adding equipment:`, error);
        req.flash('error', 'Fehler beim Hinzufügen: ' + error.message);
        res.redirect('/user/equipment');
    }
};

// Verknüpft einen SportType mit einem Equipment
export const handleLinkSportTypeToEquipment = async (req: Request, res: Response, next: NextFunction) => {
    const userId = res.locals.currentUser.id;
    const { sportType, equipmentId } = req.body;
    const fnLogPrefix = `[Ctrl LinkSportTypeToEquipment User:${userId}]`;

    if (!sportType || !equipmentId) {
        req.flash('error', 'Sportart und Ausrüstung müssen angegeben werden.');
        return res.redirect('/user/equipment');
    }

    try {
        const equipmentIdNum = parseInt(equipmentId, 10);
        if (isNaN(equipmentIdNum)) {
            req.flash('error', 'Ungültige Ausrüstungs-ID.');
            return res.redirect('/user/equipment');
        }

        const success = await sportTypeEquipmentRepository.linkSportTypeToEquipment(userId, sportType, equipmentIdNum);
        if (success) {
            log.info(`${fnLogPrefix} Sport type '${sportType}' linked to equipment ID ${equipmentIdNum}.`);
            req.flash('success', 'Sportart erfolgreich mit Ausrüstung verknüpft.');
        } else {
            req.flash('error', 'Verknüpfung konnte nicht erstellt werden.');
        }
        res.redirect('/user/equipment');
    } catch (error: any) {
        log.error(`${fnLogPrefix} Error linking sport type to equipment:`, error);
        req.flash('error', 'Fehler bei der Verknüpfung: ' + error.message);
        res.redirect('/user/equipment');
    }
};

// Entfernt die Verknüpfung zwischen einem SportType und einem Equipment
export const handleUnlinkSportTypeFromEquipment = async (req: Request, res: Response, next: NextFunction) => {
    const userId = res.locals.currentUser.id;
    const { sportType, equipmentId } = req.body;
    const fnLogPrefix = `[Ctrl UnlinkSportTypeFromEquipment User:${userId}]`;

    if (!sportType || !equipmentId) {
        req.flash('error', 'Sportart und Ausrüstung müssen angegeben werden.');
        return res.redirect('/user/equipment');
    }

    try {
        const equipmentIdNum = parseInt(equipmentId, 10);
        if (isNaN(equipmentIdNum)) {
            req.flash('error', 'Ungültige Ausrüstungs-ID.');
            return res.redirect('/user/equipment');
        }

        const success = await sportTypeEquipmentRepository.unlinkSportTypeFromEquipment(userId, sportType, equipmentIdNum);
        if (success) {
            log.info(`${fnLogPrefix} Sport type '${sportType}' unlinked from equipment ID ${equipmentIdNum}.`);
            req.flash('success', 'Verknüpfung erfolgreich entfernt.');
        } else {
            req.flash('info', 'Verknüpfung nicht gefunden oder bereits entfernt.');
        }
        res.redirect('/user/equipment');
    } catch (error: any) {
        log.error(`${fnLogPrefix} Error unlinking sport type from equipment:`, error);
        req.flash('error', 'Fehler beim Entfernen der Verknüpfung: ' + error.message);
        res.redirect('/user/equipment');
    }
};

// Fügt automatisch Equipment zu einer Aktivität basierend auf dem SportType hinzu
export const addEquipmentToActivityBySportType = async (userId: number, activityDbId: number, sportType: string): Promise<number> => {
    const fnLogPrefix = `[Ctrl AddEquipByType User:${userId} Act:${activityDbId} Sport:${sportType}]`;
    log.info(`${fnLogPrefix} Adding equipment to activity based on sport type.`);

    try {
        if (!sportType) {
            log.warn(`${fnLogPrefix} No sport type provided.`);
            return 0;
        }

        // Hole alle Equipment-IDs für diesen SportType
        const equipmentIds = await sportTypeEquipmentRepository.getEquipmentIdsForSportType(userId, sportType);
        if (equipmentIds.length === 0) {
            log.info(`${fnLogPrefix} No equipment found for sport type '${sportType}'.`);
            return 0;
        }

        // Hole bereits verknüpfte Equipment-IDs für diese Aktivität
        const currentLinkedIds = await activityEquipmentRepository.getLinkedEquipmentIdsForActivity(activityDbId);

        // Nur Equipment hinzufügen, das noch nicht verknüpft ist
        const idsToLink = equipmentIds.filter(id => !currentLinkedIds.includes(id));

        // Verknüpfe das Equipment mit der Aktivität
        for (const equipId of idsToLink) {
            await activityEquipmentRepository.linkEquipmentToActivity(activityDbId, equipId, userId);
        }

        log.info(`${fnLogPrefix} Added ${idsToLink.length} equipment items to activity.`);
        return idsToLink.length;
    } catch (error) {
        log.error(`${fnLogPrefix} Error adding equipment to activity:`, error);
        throw error;
    }
};

// Fügt automatisch Equipment zu allen Aktivitäten eines Benutzers basierend auf dem SportType hinzu
export const handleAddEquipmentToAllActivities = async (req: Request, res: Response, next: NextFunction) => {
    const userId = res.locals.currentUser.id;
    const fnLogPrefix = `[Ctrl AddEquipToAllActivities User:${userId}]`;
    log.info(`${fnLogPrefix} Adding equipment to all activities based on sport type.`);

    try {
        // Hole alle Aktivitäten des Benutzers
        const activities = await activityRepository.listActivitiesForUser(userId, { limit: 1000 });

        let totalAdded = 0;
        let activitiesUpdated = 0;

        // Für jede Aktivität das passende Equipment hinzufügen
        for (const activity of activities) {
            if (activity.sport_type) {
                const added = await addEquipmentToActivityBySportType(userId, activity.id as number, activity.sport_type);
                if (added > 0) {
                    totalAdded += added;
                    activitiesUpdated++;
                }
            }
        }

        log.info(`${fnLogPrefix} Added ${totalAdded} equipment items to ${activitiesUpdated} activities.`);
        req.flash('success', `${totalAdded} Ausrüstungsgegenstände zu ${activitiesUpdated} Aktivitäten hinzugefügt.`);
        res.redirect('/user/equipment');
    } catch (error: any) {
        log.error(`${fnLogPrefix} Error adding equipment to all activities:`, error);
        req.flash('error', 'Fehler beim Hinzufügen von Ausrüstung: ' + error.message);
        res.redirect('/user/equipment');
    }
};

export const showEditEquipmentPage = async (req: Request, res: Response, next: NextFunction) => {
    const userId = res.locals.currentUser.id;
    const equipmentId = parseInt(req.params.id, 10);
    const fnLogPrefix = `[Ctrl ShowEditEquipment User:${userId} Item:${equipmentId}]`;

    if (isNaN(equipmentId)) {
        req.flash('error', 'Ungültige Ausrüstungs-ID.');
        return res.redirect('/user/equipment');
    }
    try {
        const itemToEdit = await equipmentRepository.getEquipmentByIdAndUser(equipmentId, userId);
        const equipmentList = await equipmentRepository.getEquipmentForUser(userId); // Für die Liste daneben
        const sportTypes = await sportTypeService.getAllSportTypes();
        const sportTypeEquipmentLinks = await sportTypeEquipmentRepository.getSportTypeEquipmentForUser(userId);

        // Statistiken für alle Ausrüstungsgegenstände abrufen
        const equipmentStats = await activityEquipmentRepository.getAllEquipmentStatsForUser(userId);

        // Erweitere die Equipment-Liste mit den Statistiken
        const equipmentListWithStats = equipmentList.map(equipment => {
            const stats = equipmentStats.get(equipment.id as number) || {
                activity_count: 0,
                total_distance: 0,
                total_duration: 0
            };

            return {
                ...equipment,
                activity_count: stats.activity_count,
                total_distance: stats.total_distance,
                total_duration: stats.total_duration
            };
        });

        // Gruppiere die Verknüpfungen nach SportType für eine bessere Anzeige
        const sportTypeEquipmentMap = new Map<string, { label: string, equipmentIds: number[] }>();

        for (const link of sportTypeEquipmentLinks) {
            if (!sportTypeEquipmentMap.has(link.sport_type)) {
                sportTypeEquipmentMap.set(link.sport_type, {
                    label: link.sport_type_label || link.sport_type,
                    equipmentIds: []
                });
            }
            sportTypeEquipmentMap.get(link.sport_type)?.equipmentIds.push(link.equipment_id);
        }

        if (!itemToEdit) {
            log.warn(`${fnLogPrefix} Equipment item not found or not owned by user.`);
            req.flash('error', 'Ausrüstung nicht gefunden oder Zugriff verweigert.');
            return res.redirect('/user/equipment');
        }

        // Statistiken für das zu bearbeitende Equipment abrufen
        const itemStats = equipmentStats.get(equipmentId) || {
            activity_count: 0,
            total_distance: 0,
            total_duration: 0
        };

        // Erweitere das zu bearbeitende Element mit den Statistiken
        const itemToEditWithStats = {
            ...itemToEdit,
            activity_count: itemStats.activity_count,
            total_distance: itemStats.total_distance,
            total_duration: itemStats.total_duration
        };

        res.render('users/equipment', {
            pageTitle: `Ausrüstung bearbeiten: ${itemToEdit.name}`,
            equipmentList: equipmentListWithStats,
            itemToEdit: itemToEditWithStats, // Das zu bearbeitende Element mit Statistiken
            sportTypes: sportTypes,
            sportTypeEquipmentMap: sportTypeEquipmentMap
        });
    } catch (error) {
        log.error(`${fnLogPrefix} Error showing edit equipment page:`, error);
        next(error);
    }
};

export const handleUpdateEquipment = async (req: Request, res: Response, next: NextFunction) => {
    const userId = res.locals.currentUser.id;
    const equipmentId = parseInt(req.params.id, 10);
    const { name, description, type, notes } = req.body;
    const fnLogPrefix = `[Ctrl UpdateEquipment User:${userId} Item:${equipmentId}]`;

    if (isNaN(equipmentId)) {
        req.flash('error', 'Ungültige Ausrüstungs-ID.');
        return res.redirect('/user/equipment');
    }
    if (!name || name.trim() === '') {
        // Redirect zur Bearbeitungsseite mit Fehler
        req.flash('error', 'Name ist ein Pflichtfeld.');
        return res.redirect(`/user/equipment/edit/${equipmentId}`);
    }

    try {
        const updateData: Partial<Omit<Equipment, 'id' | 'user_id' | 'created_at' | 'updated_at'>> = {
            name: name.trim(),
            description: description || null,
            type: type || null,
            notes: notes || null
        };
        const success = await equipmentRepository.updateEquipment(equipmentId, userId, updateData);
        if (success) {
            log.info(`${fnLogPrefix} Equipment '${name}' updated.`);
            req.flash('success', 'Ausrüstung erfolgreich aktualisiert.');
            res.redirect('/user/equipment');
        } else {
            // Entweder nicht gefunden, nicht dem User gehörig, oder keine Änderungen
            req.flash('info', 'Keine Änderungen vorgenommen oder Ausrüstung nicht gefunden.');
            res.redirect(`/user/equipment/edit/${equipmentId}`);
        }
    } catch (error: any) {
        log.error(`${fnLogPrefix} Error updating equipment:`, error);
        req.flash('error', 'Fehler beim Aktualisieren: ' + error.message);
        res.redirect(`/user/equipment/edit/${equipmentId}`);
    }
};

export const handleDeleteEquipment = async (req: Request, res: Response, next: NextFunction) => {
    const userId = res.locals.currentUser.id;
    const equipmentId = parseInt(req.params.id, 10); // ID kommt aus der URL für DELETE via POST
    const fnLogPrefix = `[Ctrl DeleteEquipment User:${userId} Item:${equipmentId}]`;

    if (isNaN(equipmentId)) {
        req.flash('error', 'Ungültige Ausrüstungs-ID.');
        return res.redirect('/user/equipment');
    }

    try {
        const success = await equipmentRepository.deleteEquipment(equipmentId, userId);
        if (success) {
            log.info(`${fnLogPrefix} Equipment deleted.`);
            req.flash('success', 'Ausrüstung erfolgreich gelöscht.');
            res.redirect('/user/equipment');
        } else {
            log.warn(`${fnLogPrefix} Equipment not found or not owned by user for deletion.`);
            req.flash('error', 'Ausrüstung nicht gefunden oder Zugriff verweigert.');
            res.redirect('/user/equipment');
        }
    } catch (error: any) {
        log.error(`${fnLogPrefix} Error deleting equipment:`, error);
        req.flash('error', 'Fehler beim Löschen: ' + error.message);
        res.redirect('/user/equipment');
    }
};

// === User-spezifische Karte ===
export const showUserMap = async (req: Request, res: Response, next: NextFunction) => {
    const usernameToView = req.params.username;
    const loggedInUser = res.locals.currentUser;
    const fnLogPrefix = `[Ctrl ShowUserMap Target:${usernameToView} LoggedIn:${loggedInUser?.username}]`;

    if (!loggedInUser || (loggedInUser.username !== usernameToView && !loggedInUser.isAdmin)) {
        log.warn(`${fnLogPrefix} Access denied to map of ${usernameToView}.`);
        return res.status(403).render('error', { pageTitle: 'Zugriff verweigert', message: 'Sie haben keine Berechtigung, diese Karte anzuzeigen.', statusCode: 403 });
    }

    try {
        const targetUser = await userRepository.getFullUserDetailsByUsername(usernameToView);
        if (!targetUser) {
            log.warn(`${fnLogPrefix} Target user ${usernameToView} for map not found.`);
            return res.status(404).render('error', { pageTitle: 'Benutzer nicht gefunden', message: `Benutzer ${usernameToView} nicht gefunden.`, statusCode: 404 });
        }

        const userSpecificStyles: MapLayerStyle[] = await mapLayerStyleRepository.getStylesForUser(targetUser.id);

        // Konfiguration für das clientseitige JavaScript
        const clientConfig = {
            pageContext: 'user', // oder 'owner', um klarzustellen, dass es die eigene Karte ist
            currentUserData: { // Daten des eingeloggten Benutzers (der die Seite sieht)
                username: loggedInUser.username,
                id: loggedInUser.id
            },
            targetUserForMap: { // Daten des Benutzers, dessen Karte angezeigt wird
                username: targetUser.username,
                id: targetUser.id,
                default_map_zoom: targetUser.default_map_zoom,
                default_map_center_lat: targetUser.default_map_center_lat,
                default_map_center_lng: targetUser.default_map_center_lng,
            },
            maps: {
                baseLayers: config.maps.baseLayers.map(layer => ({
                    name: layer.name, url: layer.url, options: layer.options, active: !!layer.active
                })),
                defaultMapOptions: config.maps.defaultMapOptions, // Globale Defaults
                styleColors: config.maps.styleColors,
                geojsonStyleDefault: config.maps.geojsonStyleDefault,
                plannedRouteStyle: config.maps.plannedRouteStyle
            },
            apiEndpoints: { // User-spezifische Endpunkte (dynamisch mit targetUser.username)
                layerStructure: `/api/geojson/user/${encodeURIComponent(targetUser.username)}/layer_structure`,
                categoryGeoJson: `/api/geojson/user/${encodeURIComponent(targetUser.username)}/by_category`,
                plannedRouteGeoJson: `/api/geojson/user/${encodeURIComponent(targetUser.username)}/planned_routes`,
                poisGeoJson: `/api/geojson/user/${encodeURIComponent(targetUser.username)}/pois`
            }
        };
        log.info(`${fnLogPrefix} Rendering map for user ${targetUser.username}.`);
        res.render('users/map', { // Pfad zu Ihrer User-Karten-EJS-Datei
            pageTitle: `Karte für ${targetUser.username}`,
            pageContext: 'user', // Für EJS-Logik im Template
            targetUserForMap: clientConfig.targetUserForMap, // Für Header etc. im EJS
            clientConfig: clientConfig // Das ganze Objekt für window.APP_CONFIG
        });
    } catch (error) {
        log.error(`${fnLogPrefix} Error rendering user map:`, error);
        next(error);
    }
};


// === User-spezifische Statistiken ===

// Hilfsfunktion (kann auch in eine utils-Datei ausgelagert werden)
const processYearDataForChart = (activities: Array<{ activityDate: string, distance: number }>, year: number): Array<{ x: number, y: number }> => {
    const cumulativeData: { x: number, y: number }[] = [];
    const isLeap = (year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0);
    const daysInYear = isLeap ? 366 : 365;
    let currentCumulativeKm = 0;
    let activityIndex = 0;
    activities.sort((a, b) => new Date(a.activityDate).getTime() - new Date(b.activityDate).getTime());
    const NORMALIZED_YEAR = 2000; // Für die X-Achse des Charts

    for (let day = 1; day <= daysInYear; day++) {
        const currentDateNormalized = new Date(Date.UTC(NORMALIZED_YEAR, 0, day));
        const originalDateForLoop = new Date(Date.UTC(year, 0, day));
        const originalDateStr = originalDateForLoop.toISOString().split('T')[0];
        let dailyDistanceMeters = 0;
        while (activityIndex < activities.length) {
            const activityDateStr = activities[activityIndex].activityDate.split('T')[0];
            if (activityDateStr === originalDateStr) {
                const dist = activities[activityIndex].distance;
                if (typeof dist === 'number' && !isNaN(dist)) {
                    dailyDistanceMeters += dist;
                }
                activityIndex++;
            } else if (new Date(activityDateStr) > originalDateForLoop) {
                break;
            } else {
                activityIndex++;
            }
        }
        currentCumulativeKm += (dailyDistanceMeters / 1000);
        cumulativeData.push({
            x: currentDateNormalized.getTime(),
            y: parseFloat(currentCumulativeKm.toFixed(1))
        });
    }
    return cumulativeData;
};


export const showUserStatsPage = async (req: Request, res: Response, next: NextFunction) => {
    const usernameToView = req.params.username;
    const loggedInUser = res.locals.currentUser;
    const fnLogPrefix = `[Ctrl ShowUserStats Target:${usernameToView} LoggedIn:${loggedInUser?.username}]`;

    if (!loggedInUser || (loggedInUser.username !== usernameToView && !loggedInUser.isAdmin)) {
        log.warn(`${fnLogPrefix} Access denied.`);
        return res.status(403).render('error', { pageTitle: 'Zugriff verweigert', message: 'Sie haben keine Berechtigung, diese Statistikseite anzuzeigen.', statusCode: 403 });
    }

    try {
        const targetUser = await userRepository.findUserByUsername(usernameToView);
        if (!targetUser) {
            log.warn(`${fnLogPrefix} Target user ${usernameToView} not found.`);
            return res.status(404).render('error', { pageTitle: 'Benutzer nicht gefunden', message: `Benutzer ${usernameToView} nicht gefunden.`, statusCode: 404 });
        }

        // Basisstatistiken abrufen
        const yearlySummaryData = await activityRepository.getYearlySummaryStatsForUser(targetUser.id);
        const availableYears = yearlySummaryData.length > 0 ? yearlySummaryData.map(entry => entry.year).sort((a, b) => b - a) : [new Date().getFullYear()];

        // Benutzerdefinierte Sportart-Obergruppen abrufen
        const sportGroups = await sportGroupRepository.getSportGroupsForUser(targetUser.id);

        log.info(`${fnLogPrefix} Rendering stats page for user ${targetUser.username} (ID: ${targetUser.id})`);
        res.render('users/stats', {
            pageTitle: `Statistiken für ${targetUser.username}`,
            targetUserForStats: { username: targetUser.username, id: targetUser.id },
            initialData: {
                yearlySummary: yearlySummaryData,
                availableYears: availableYears,
                sportGroups: sportGroups
            },
        });
    } catch (error) {
        log.error(`${fnLogPrefix} Error rendering user stats page:`, error);
        next(error);
    }
};

// API Handler für User-Statistiken
export const handleUserMonthlyStatsApi = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const loggedInUser = res.locals.currentUser;
    const targetUsername = req.params.username;
    const year = parseInt(req.query.year as string, 10);
    const fnLogPrefix = `[API UserMonthlyStats Target:${targetUsername} LoggedIn:${loggedInUser?.username}]`;

    try {
        const targetUser = await userRepository.findUserByUsername(targetUsername);
        if (!targetUser) {
            res.status(404).json({ message: "Zugehöriger Benutzer nicht gefunden." });
            return; // Wichtig: return; nach res.json()
        }
        if (!loggedInUser || (loggedInUser.username !== targetUsername && !loggedInUser.isAdmin)) {
            res.status(403).json({ message: "Zugriff verweigert." });
            return; // Wichtig
        }
        if (isNaN(year)) {
            res.status(400).json({ message: "Ungültiges Jahr." });
            return; // Wichtig
        }

        const monthlyData = await activityRepository.getMonthlyStats(year, targetUser.id);
        res.json(monthlyData.map(m => m.total_km));
        // Kein return; hier nötig, da es die letzte Anweisung im try-Block ist
    } catch (error) {
        log.error(`${fnLogPrefix} Error:`, error);
        next(error); // Fehler an die nächste Middleware weitergeben
    }
};

export const handleUserYearlySummaryApi = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const loggedInUser = res.locals.currentUser;
    const targetUsername = req.params.username;
    const fnLogPrefix = `[API UserYearlySummary Target:${targetUsername} LoggedIn:${loggedInUser?.username}]`;

    try {
        const targetUser = await userRepository.findUserByUsername(targetUsername);
        if (!targetUser) {
            res.status(404).json({ message: "Zugehöriger Benutzer nicht gefunden." });
            return;
        }
        if (!loggedInUser || (loggedInUser.username !== targetUsername && !loggedInUser.isAdmin)) {
            res.status(403).json({ message: "Zugriff verweigert." });
            return;
        }
        const summary = await activityRepository.getYearlySummaryStatsForUser(targetUser.id);
        res.json(summary);
    } catch (error) {
        log.error(`${fnLogPrefix} Error:`, error);
        next(error);
    }
};

export const handleUserDailyProgressionApi = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const loggedInUser = res.locals.currentUser;
    const targetUsername = req.params.username;
    const yearA = parseInt(req.query.yearA as string, 10);
    const yearB = parseInt(req.query.yearB as string, 10);
    const activityGroup = (req.query.activityGroup as string || 'all').toLowerCase();
    const fnLogPrefix = `[API UserDailyProgression Target:${targetUsername} LoggedIn:${loggedInUser?.username}]`;

    try {
        const targetUser = await userRepository.findUserByUsername(targetUsername);
        if (!targetUser) {
            res.status(404).json({ message: "Zugehöriger Benutzer nicht gefunden." });
            return;
        }
        if (!loggedInUser || (loggedInUser.username !== targetUsername && !loggedInUser.isAdmin)) {
            res.status(403).json({ message: "Zugriff verweigert." });
            return;
        }

        if (isNaN(yearA) || isNaN(yearB)) {
            res.status(400).json({ message: "Ungültige Parameter." });
            return;
        }

        let dataYearA, dataYearB;

        // Prüfen, ob es sich um eine benutzerdefinierte Sportart-Obergruppe handelt
        if (!['all', 'rad', 'fuss'].includes(activityGroup)) {
            // Es könnte eine benutzerdefinierte Gruppe sein
            const sportGroups = await sportGroupRepository.getSportGroupsForUser(targetUser.id);
            const group = sportGroups.find(g => g.name.toLowerCase() === activityGroup);

            if (group && group.sport_types && group.sport_types.length > 0) {
                // Für jede Sportart in der Gruppe die Aktivitäten abrufen und zusammenführen
                const activitiesYearAPromises = group.sport_types.map(sportType =>
                    activityRepository.getActivitiesForYearType(yearA, sportType, targetUser.id)
                );

                const activitiesYearBPromises = group.sport_types.map(sportType =>
                    activityRepository.getActivitiesForYearType(yearB, sportType, targetUser.id)
                );

                const activitiesYearAArrays = await Promise.all(activitiesYearAPromises);
                const activitiesYearBArrays = await Promise.all(activitiesYearBPromises);

                // Alle Aktivitäten zusammenführen
                dataYearA = activitiesYearAArrays.flat();
                dataYearB = activitiesYearBArrays.flat();

                // Duplikate entfernen (falls eine Aktivität mehreren Sportarten zugeordnet ist)
                const uniqueActivitiesYearA = new Map();
                dataYearA.forEach(activity => {
                    if (activity.id) {
                        uniqueActivitiesYearA.set(activity.id, activity);
                    }
                });

                const uniqueActivitiesYearB = new Map();
                dataYearB.forEach(activity => {
                    if (activity.id) {
                        uniqueActivitiesYearB.set(activity.id, activity);
                    }
                });

                dataYearA = Array.from(uniqueActivitiesYearA.values());
                dataYearB = Array.from(uniqueActivitiesYearB.values());
            } else {
                // Wenn die Gruppe nicht gefunden wurde, verwenden wir 'all'
                [dataYearA, dataYearB] = await Promise.all([
                    activityRepository.getActivitiesForYearGroup(yearA, 'all', targetUser.id),
                    activityRepository.getActivitiesForYearGroup(yearB, 'all', targetUser.id)
                ]);
            }
        } else {
            // Standardverhalten für 'all', 'rad', 'fuss'
            [dataYearA, dataYearB] = await Promise.all([
                activityRepository.getActivitiesForYearGroup(yearA, activityGroup as 'all' | 'rad' | 'fuss', targetUser.id),
                activityRepository.getActivitiesForYearGroup(yearB, activityGroup as 'all' | 'rad' | 'fuss', targetUser.id)
            ]);
        }

        // Die processYearDataForChart Funktion aus dem userController verwenden
        const progressionA = processYearDataForChart(dataYearA, yearA);
        const progressionB = processYearDataForChart(dataYearB, yearB);

        res.json({
            yearA: { year: yearA, activities: progressionA },
            yearB: { year: yearB, activities: progressionB },
            activityGroup: activityGroup,
            comparisonScope: `User ${targetUser.username}`
        });
    } catch (error) {
        log.error(`${fnLogPrefix} Error:`, error);
        next(error);
    }
};


// === Synchronisation ===
export const handleStravaSyncLatest = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const userId = req.session.user!.id;
    const count = parseInt(req.body.count, 10) || 10;
    const fnLogPrefix = `[CtrlStravaSyncLatest User:${userId}]`;

    log.info(`${fnLogPrefix} Initiating sync for latest ${count} Strava activities.`);
    try {
        const result: SyncResult = await activityService.syncLatestStravaActivitiesForUser(userId, count);
        // ... (Logik zur Erstellung der Redirect-Nachricht basierend auf result, wie zuvor)
        let message = `Strava Synchronisation für die neuesten ${result.processedCount} Aktivitäten abgeschlossen.`;

        if (result.newActivities > 0) message += ` ${result.newActivities} neu hinzugefügt.`;
        if (result.updatedActivities > 0) message += ` ${result.updatedActivities} aktualisiert.`;

        if (result.errors.length > 0) {
            message += ` (${result.errors.length} Fehler bei einzelnen Aktivitäten).`;
            req.flash('info', message); // Teilweiser Erfolg ist eher eine Info
            log.warn(`${fnLogPrefix} Sync completed with errors:`, result.errors);
        } else if (!result.success && result.rateLimitHit) {
            message = `Strava API Limit erreicht.`;
            if (result.retryAfterMinutes) {
                message += ` Bitte versuchen Sie es in ca. ${result.retryAfterMinutes} Minuten erneut.`;
            } else {
                message += ` Bitte versuchen Sie es später erneut.`;
            }
            req.flash('error', message);
        } else if (!result.success) {
            message = `Strava Synchronisation fehlgeschlagen: ${result.message}`;
            req.flash('error', message);
        } else {
            req.flash('success', message);
        }

        log.info(`${fnLogPrefix} Redirecting with message: ${message}`);
        res.redirect('/user/settings');

    } catch (error: any) {
        // ... (Fehlerbehandlung für kritische Fehler, wie zuvor)
        log.error(`${fnLogPrefix} Critical error during Strava sync initiation:`, error);
        let userErrorMessage = 'Ein kritischer Fehler ist während der Synchronisation aufgetreten.';
        if (error.isRateLimitError) {
            userErrorMessage = `Strava API Limit erreicht.`;
            if (error.retryAfterMinutes) userErrorMessage += ` Bitte versuchen Sie es in ca. ${error.retryAfterMinutes} Minuten erneut.`;
            else userErrorMessage += ` Bitte versuchen Sie es später erneut.`;
        } else if (error.message?.includes("Strava ist für diesen Benutzer nicht")) {
            userErrorMessage = error.message;
        }
        req.flash('error', userErrorMessage);
        res.redirect('/user/settings');
    }
};

/**
 * API-Endpunkt zum Abrufen der Freundesliste für das Teilen von Aktivitäten
 * GET /user/api/friends
 */
export const getFriendsListApi = async (req: Request, res: Response, next: NextFunction) => {
    const userId = res.locals.currentUser.id;
    const fnLogPrefix = `[API GetFriendsList User:${userId}]`;
    log.debug(`${fnLogPrefix} Fetching friends list for sharing.`);

    try {
        // Freundesliste aus der Datenbank abrufen
        const connections = await userConnectionRepository.listUserConnections(userId);
        const friends = connections.filter(c => c.status === 'accepted' && c.direction === 'friends');

        // Freundesliste formatieren und zurückgeben
        const formattedFriends = friends.map((friend: any) => ({
            friend_user_id: friend.friend_user_id,
            friend_username: friend.friend_username
        }));

        res.json({
            success: true,
            friends: formattedFriends
        });
    } catch (error: any) {
        log.error(`${fnLogPrefix} Error fetching friends list:`, error);
        res.status(500).json({
            success: false,
            message: 'Fehler beim Abrufen der Freundesliste.',
            error: error.message
        });
    }
};

/**
 * Teilt eine Aktivität mit ausgewählten Freunden
 * POST /user/activity/:activityDbId/share
 */
export const shareActivityWithFriends = async (req: Request, res: Response, next: NextFunction) => {
    const userId = res.locals.currentUser.id;
    const activityDbId = parseInt(req.params.activityDbId, 10);
    const friendIds = Array.isArray(req.body.friendIds) ? req.body.friendIds : [req.body.friendIds].filter(Boolean);

    const fnLogPrefix = `[Ctrl ShareActivity User:${userId} Act:${activityDbId}]`;
    log.info(`${fnLogPrefix} Sharing activity with friends: ${friendIds.join(', ')}`);

    if (!friendIds.length) {
        req.flash('info', 'Keine Freunde zum Teilen ausgewählt.');
        return res.redirect(`/user/activity-detail/${activityDbId}`);
    }

    try {
        // Prüfen, ob der Benutzer der Eigentümer der Aktivität ist
        const activity = await activityRepository.getActivityByPrimaryKey(activityDbId);
        if (!activity) {
            req.flash('error', 'Aktivität nicht gefunden.');
            return res.redirect(`/user/activity-detail/${activityDbId}`);
        }

        if (activity.user_id !== userId) {
            req.flash('error', 'Du kannst nur deine eigenen Aktivitäten teilen.');
            return res.redirect(`/user/activity-detail/${activityDbId}`);
        }

        // Aktivität mit jedem ausgewählten Freund teilen
        const sharePromises = friendIds.map(async (friendId: string) => {
            const numericFriendId = parseInt(friendId, 10);
            if (isNaN(numericFriendId)) return { success: false, friendId, error: 'Ungültige Freund-ID' };

            try {
                // Prüfen, ob eine Freundschaftsverbindung besteht
                const isValidFriend = await userConnectionRepository.checkFriendshipStatus(userId, numericFriendId);

                if (!isValidFriend) {
                    return { success: false, friendId, error: 'Keine gültige Freundschaftsverbindung' };
                }

                // Aktivität teilen (in einer Tabelle für geteilte Aktivitäten speichern)
                await activityRepository.shareActivityWithUser(activityDbId, numericFriendId);

                // Benachrichtigung an den Freund senden
                const activityName = activity.activity_name ||
                    `${activity.sport_type || 'Aktivität'}${activity.start_date ? ` vom ${new Date(activity.start_date).toLocaleDateString('de-DE')}` : ''}`;
                await notificationRepository.createNotification(
                    numericFriendId,
                    'activity_shared',
                    `${res.locals.currentUser.username} hat "${activityName}" mit dir geteilt.`,
                    `/user/activity-detail/${activityDbId}`
                );

                return { success: true, friendId };
            } catch (error: any) {
                log.error(`${fnLogPrefix} Error sharing with friend ${friendId}:`, error);
                return { success: false, friendId, error: error.message };
            }
        });

        const results = await Promise.all(sharePromises);
        const successCount = results.filter(r => r.success).length;
        const errorCount = results.filter(r => !r.success).length;

        if (successCount > 0) {
            const message = `Aktivität erfolgreich mit ${successCount} Freund${successCount !== 1 ? 'en' : ''} geteilt.${errorCount > 0 ? ` (${errorCount} Fehler)` : ''}`;
            req.flash('success', message);
            return res.redirect(`/user/activity-detail/${activityDbId}`);
        } else {
            req.flash('error', 'Fehler beim Teilen der Aktivität.');
            return res.redirect(`/user/activity-detail/${activityDbId}`);
        }
    } catch (error: any) {
        log.error(`${fnLogPrefix} Error sharing activity:`, error);
        req.flash('error', 'Fehler: ' + (error.message || 'Unbekannter Fehler'));
        return res.redirect(`/user/activity-detail/${activityDbId}`);
    }
};

// ===== SHARING FUNCTIONS FOR PLANNED ROUTES =====

/**
 * Löscht mehrere geplante Routen auf einmal
 * POST /user/activities/planned/bulk-delete
 */
export const bulkDeletePlannedRoutes = async (req: Request, res: Response, next: NextFunction) => {
    const userId = res.locals.currentUser.id;
    const routeIds = Array.isArray(req.body.routeIds) ? req.body.routeIds : [req.body.routeIds].filter(Boolean);

    const fnLogPrefix = `[Ctrl BulkDeletePlannedRoutes User:${userId}]`;
    log.info(`${fnLogPrefix} Deleting planned routes: ${routeIds.join(', ')}`);

    if (!routeIds.length) {
        req.flash('info', 'Keine Routen zum Löschen ausgewählt.');
        return res.redirect('/user/activities/planned');
    }

    try {
        const deletePromises = routeIds.map(async (routeId: string) => {
            const numericRouteId = parseInt(routeId, 10);
            if (isNaN(numericRouteId)) return { success: false, routeId, error: 'Ungültige Route-ID' };

            try {
                // Prüfen, ob der Benutzer der Eigentümer der Route ist
                const plannedRoute = await plannedRouteRepository.getPlannedRouteById(numericRouteId);
                if (!plannedRoute) {
                    return { success: false, routeId, error: 'Route nicht gefunden' };
                }

                if (plannedRoute.user_id !== userId) {
                    return { success: false, routeId, error: 'Keine Berechtigung' };
                }

                await plannedRouteRepository.deletePlannedRoute(numericRouteId);
                return { success: true, routeId };
            } catch (error: any) {
                log.error(`${fnLogPrefix} Error deleting route ${routeId}:`, error);
                return { success: false, routeId, error: error.message };
            }
        });

        const results = await Promise.all(deletePromises);
        const successCount = results.filter(r => r.success).length;
        const errorCount = results.filter(r => !r.success).length;

        if (successCount > 0) {
            const message = `${successCount} geplante Route${successCount !== 1 ? 'n' : ''} erfolgreich gelöscht.${errorCount > 0 ? ` (${errorCount} Fehler)` : ''}`;
            req.flash('success', message);
        } else {
            req.flash('error', 'Fehler beim Löschen der geplanten Routen.');
        }

        return res.redirect('/user/activities/planned');

    } catch (error: any) {
        log.error(`${fnLogPrefix} Error:`, error);
        req.flash('error', 'Fehler beim Löschen der geplanten Routen.');
        return res.redirect('/user/activities/planned');
    }
};

/**
 * Teilt mehrere geplante Routen mit ausgewählten Freunden
 * POST /user/activities/planned/bulk-share
 */
export const bulkSharePlannedRoutes = async (req: Request, res: Response, next: NextFunction) => {
    const userId = res.locals.currentUser.id;
    const routeIds = Array.isArray(req.body.routeIds) ? req.body.routeIds : [req.body.routeIds].filter(Boolean);
    const friendIds = Array.isArray(req.body.friendIds) ? req.body.friendIds : [req.body.friendIds].filter(Boolean);

    const fnLogPrefix = `[Ctrl BulkSharePlannedRoutes User:${userId}]`;
    log.info(`${fnLogPrefix} Sharing ${routeIds.length} planned routes with ${friendIds.length} friends`);

    if (!routeIds.length) {
        req.flash('info', 'Keine Routen zum Teilen ausgewählt.');
        return res.redirect('/user/activities/planned');
    }

    if (!friendIds.length) {
        req.flash('info', 'Keine Freunde zum Teilen ausgewählt.');
        return res.redirect('/user/activities/planned');
    }

    try {
        let totalShares = 0;
        let totalErrors = 0;

        for (const routeId of routeIds) {
            const numericRouteId = parseInt(routeId, 10);
            if (isNaN(numericRouteId)) {
                totalErrors++;
                continue;
            }

            // Prüfen, ob der Benutzer der Eigentümer der Route ist
            const plannedRoute = await plannedRouteRepository.getPlannedRouteById(numericRouteId);
            if (!plannedRoute || plannedRoute.user_id !== userId) {
                totalErrors++;
                continue;
            }

            // Route mit jedem ausgewählten Freund teilen
            for (const friendId of friendIds) {
                const numericFriendId = parseInt(friendId, 10);
                if (isNaN(numericFriendId)) {
                    totalErrors++;
                    continue;
                }

                try {
                    await plannedRouteRepository.sharePlannedRouteWithUser(numericRouteId, numericFriendId);

                    // Benachrichtigung erstellen mit mehr Kontext
                    const routeName = plannedRoute.name || `${plannedRoute.sport_type || 'Route'} (${Math.round((plannedRoute.distance_m || 0) / 1000)}km)`;
                    await notificationRepository.createNotification(
                        numericFriendId,
                        'planned_route_shared',
                        `${res.locals.currentUser.username} hat die geplante Route "${routeName}" mit dir geteilt.`,
                        `/user/activity/planned/${numericRouteId}`
                    );

                    totalShares++;
                } catch (error: any) {
                    log.error(`${fnLogPrefix} Error sharing route ${routeId} with friend ${friendId}:`, error);
                    totalErrors++;
                }
            }
        }

        if (totalShares > 0) {
            const message = `${totalShares} Route${totalShares !== 1 ? 'n' : ''} erfolgreich geteilt.${totalErrors > 0 ? ` (${totalErrors} Fehler)` : ''}`;
            req.flash('success', message);
        } else {
            req.flash('error', 'Fehler beim Teilen der geplanten Routen.');
        }

        return res.redirect('/user/activities/planned');

    } catch (error: any) {
        log.error(`${fnLogPrefix} Error:`, error);
        req.flash('error', 'Fehler beim Teilen der geplanten Routen.');
        return res.redirect('/user/activities/planned');
    }
};

/**
 * Teilt eine geplante Route mit ausgewählten Freunden
 * POST /user/activity/planned/:plannedRouteId/share
 */
export const sharePlannedRouteWithFriends = async (req: Request, res: Response, next: NextFunction) => {
    const userId = res.locals.currentUser.id;
    const plannedRouteId = parseInt(req.params.plannedRouteId, 10);
    const friendIds = Array.isArray(req.body.friendIds) ? req.body.friendIds : [req.body.friendIds].filter(Boolean);

    const fnLogPrefix = `[Ctrl SharePlannedRoute User:${userId} Route:${plannedRouteId}]`;
    log.info(`${fnLogPrefix} Sharing planned route with friends: ${friendIds.join(', ')}`);

    if (!friendIds.length) {
        req.flash('info', 'Keine Freunde zum Teilen ausgewählt.');
        return res.redirect(`/user/activity/planned/${plannedRouteId}`);
    }

    try {
        // Prüfen, ob der Benutzer der Eigentümer der geplanten Route ist
        const plannedRoute = await plannedRouteRepository.getPlannedRouteById(plannedRouteId);
        if (!plannedRoute) {
            req.flash('error', 'Geplante Route nicht gefunden.');
            return res.redirect(`/user/activity/planned/${plannedRouteId}`);
        }

        if (plannedRoute.user_id !== userId) {
            req.flash('error', 'Du kannst nur deine eigenen geplanten Routen teilen.');
            return res.redirect(`/user/activity/planned/${plannedRouteId}`);
        }

        // Geplante Route mit jedem ausgewählten Freund teilen
        const sharePromises = friendIds.map(async (friendId: string) => {
            const numericFriendId = parseInt(friendId, 10);
            if (isNaN(numericFriendId)) return { success: false, friendId, error: 'Ungültige Freund-ID' };

            try {
                // Prüfen, ob eine Freundschaftsverbindung besteht
                const isValidFriend = await userConnectionRepository.checkFriendshipStatus(userId, numericFriendId);

                if (!isValidFriend) {
                    return { success: false, friendId, error: 'Keine gültige Freundschaftsverbindung' };
                }

                // Geplante Route teilen
                await plannedRouteRepository.sharePlannedRouteWithUser(plannedRouteId, numericFriendId);

                // Benachrichtigung an den Freund senden mit mehr Kontext
                const routeName = plannedRoute.name || `${plannedRoute.sport_type || 'Route'} (${Math.round((plannedRoute.distance_m || 0) / 1000)}km)`;
                await notificationRepository.createNotification(
                    numericFriendId,
                    'planned_route_shared',
                    `${res.locals.currentUser.username} hat die geplante Route "${routeName}" mit dir geteilt.`,
                    `/user/activity/planned/${plannedRouteId}`
                );

                return { success: true, friendId };
            } catch (error: any) {
                log.error(`${fnLogPrefix} Error sharing with friend ${friendId}:`, error);
                return { success: false, friendId, error: error.message };
            }
        });

        const results = await Promise.all(sharePromises);
        const successCount = results.filter(r => r.success).length;
        const errorCount = results.filter(r => !r.success).length;

        if (successCount > 0) {
            const message = `Geplante Route erfolgreich mit ${successCount} Freund${successCount !== 1 ? 'en' : ''} geteilt.${errorCount > 0 ? ` (${errorCount} Fehler)` : ''}`;
            req.flash('success', message);
            return res.redirect(`/user/activity/planned/${plannedRouteId}`);
        } else {
            req.flash('error', 'Fehler beim Teilen der geplanten Route.');
            return res.redirect(`/user/activity/planned/${plannedRouteId}`);
        }
    } catch (error: any) {
        log.error(`${fnLogPrefix} Error sharing planned route:`, error);
        req.flash('error', 'Fehler: ' + (error.message || 'Unbekannter Fehler'));
        return res.redirect(`/user/activity/planned/${plannedRouteId}`);
    }
};

// ===== SHARING FUNCTIONS FOR POIS =====

/**
 * Teilt einen POI mit ausgewählten Freunden
 * POST /user/poi/:poiId/share
 */
export const sharePOIWithFriends = async (req: Request, res: Response, next: NextFunction) => {
    const userId = res.locals.currentUser.id;
    const poiId = parseInt(req.params.poiId, 10);
    const friendIds = Array.isArray(req.body.friendIds) ? req.body.friendIds : [req.body.friendIds].filter(Boolean);

    const fnLogPrefix = `[Ctrl SharePOI User:${userId} POI:${poiId}]`;
    log.info(`${fnLogPrefix} Sharing POI with friends: ${friendIds.join(', ')}`);

    if (!friendIds.length) {
        req.flash('info', 'Keine Freunde zum Teilen ausgewählt.');
        return res.redirect(`/user/poi/${poiId}`);
    }

    try {
        // Prüfen, ob der Benutzer der Eigentümer des POI ist
        const poi = await poiRepository.getPOIById(poiId);
        if (!poi) {
            req.flash('error', 'POI nicht gefunden.');
            return res.redirect(`/user/poi/${poiId}`);
        }

        if (poi.user_id !== userId) {
            req.flash('error', 'Du kannst nur deine eigenen POIs teilen.');
            return res.redirect(`/user/poi/${poiId}`);
        }

        // POI mit jedem ausgewählten Freund teilen
        const sharePromises = friendIds.map(async (friendId: string) => {
            const numericFriendId = parseInt(friendId, 10);
            if (isNaN(numericFriendId)) return { success: false, friendId, error: 'Ungültige Freund-ID' };

            try {
                // Prüfen, ob eine Freundschaftsverbindung besteht
                const isValidFriend = await userConnectionRepository.checkFriendshipStatus(userId, numericFriendId);

                if (!isValidFriend) {
                    return { success: false, friendId, error: 'Keine gültige Freundschaftsverbindung' };
                }

                // POI teilen
                await poiRepository.sharePOIWithUser(poiId, numericFriendId);

                // Benachrichtigung an den Freund senden mit mehr Kontext
                const poiName = poi.title || `${poi.poi_type || 'POI'} (${poi.latitude.toFixed(4)}, ${poi.longitude.toFixed(4)})`;
                await notificationRepository.createNotification(
                    numericFriendId,
                    'poi_shared',
                    `${res.locals.currentUser.username} hat den POI "${poiName}" mit dir geteilt.`,
                    `/user/poi/${poiId}`
                );

                return { success: true, friendId };
            } catch (error: any) {
                log.error(`${fnLogPrefix} Error sharing with friend ${friendId}:`, error);
                return { success: false, friendId, error: error.message };
            }
        });

        const results = await Promise.all(sharePromises);
        const successCount = results.filter(r => r.success).length;
        const errorCount = results.filter(r => !r.success).length;

        if (successCount > 0) {
            const message = `POI erfolgreich mit ${successCount} Freund${successCount !== 1 ? 'en' : ''} geteilt.${errorCount > 0 ? ` (${errorCount} Fehler)` : ''}`;
            req.flash('success', message);
            return res.redirect(`/user/poi/${poiId}`);
        } else {
            req.flash('error', 'Fehler beim Teilen des POI.');
            return res.redirect(`/user/poi/${poiId}`);
        }
    } catch (error: any) {
        log.error(`${fnLogPrefix} Error sharing POI:`, error);
        req.flash('error', 'Fehler: ' + (error.message || 'Unbekannter Fehler'));
        return res.redirect(`/user/poi/${poiId}`);
    }
};

/**
 * Zeigt die Bearbeitungsseite für eine geplante Route an.
 */
export const showEditPlannedRoutePage = async (req: Request, res: Response, next: NextFunction) => {
    const loggedInUser = res.locals.currentUser;
    const plannedRouteId = parseInt(req.params.plannedRouteId, 10);
    const fnLogPrefix = `[Ctrl EditPlannedRoute User:${loggedInUser.username} RouteID:${plannedRouteId}]`;

    if (isNaN(plannedRouteId)) {
        req.flash('error', 'Ungültige Routen-ID.');
        return res.redirect('/user/activities/planned');
    }

    try {
        const plannedRoute = await plannedRouteRepository.getPlannedRouteById(plannedRouteId);

        if (!plannedRoute) {
            req.flash('error', 'Geplante Route nicht gefunden.');
            return res.redirect('/user/activities/planned');
        }

        if (plannedRoute.user_id !== loggedInUser.id) {
            log.warn(`${fnLogPrefix} User attempted to edit planned route not owned by them.`);
            req.flash('error', 'Zugriff verweigert. Sie können nur eigene geplante Routen bearbeiten.');
            return res.redirect('/user/activities/planned');
        }

        // Lade zusätzliche Daten
        const [images, urls] = await Promise.all([
            plannedRouteImageRepository.getImagesByPlannedRouteId(plannedRouteId),
            plannedRouteUrlRepository.getUrlsByPlannedRouteId(plannedRouteId)
        ]);

        res.render('users/planned_route_edit', {
            pageTitle: `Bearbeiten: ${plannedRoute.name || 'Unbenannt'}`,
            plannedRoute,
            images,
            urls
        });

    } catch (error: any) {
        log.error(`${fnLogPrefix} Error:`, error);
        next(error);
    }
};

/**
 * Verarbeitet die Aktualisierung einer geplanten Route.
 */
export const handleUpdatePlannedRoute = async (req: Request, res: Response, next: NextFunction) => {
    const loggedInUser = res.locals.currentUser;
    const plannedRouteId = parseInt(req.params.plannedRouteId, 10);
    const fnLogPrefix = `[Ctrl UpdatePlannedRoute User:${loggedInUser.username} RouteID:${plannedRouteId}]`;

    if (isNaN(plannedRouteId)) {
        req.flash('error', 'Ungültige Routen-ID.');
        return res.redirect('/user/activities/planned');
    }

    try {
        const plannedRoute = await plannedRouteRepository.getPlannedRouteById(plannedRouteId);

        if (!plannedRoute) {
            req.flash('error', 'Geplante Route nicht gefunden.');
            return res.redirect('/user/activities/planned');
        }

        if (plannedRoute.user_id !== loggedInUser.id) {
            log.warn(`${fnLogPrefix} User attempted to update planned route not owned by them.`);
            req.flash('error', 'Zugriff verweigert. Sie können nur eigene geplante Routen bearbeiten.');
            return res.redirect('/user/activities/planned');
        }

        const { name, description, recalculate_gpx, new_urls, image_files, image_urls } = req.body;

        // Grunddaten aktualisieren
        const updateData: Partial<PlannedRoute> = {};
        if (name !== undefined) updateData.name = name.trim() || null;
        if (description !== undefined) updateData.description = description.trim() || null;

        if (Object.keys(updateData).length > 0) {
            await plannedRouteRepository.updatePlannedRoute(plannedRouteId, updateData);
            log.info(`${fnLogPrefix} Basic data updated`);
        }

        // GPX neu berechnen falls gewünscht
        if (recalculate_gpx === 'true') {
            try {
                const gpxResult = await plannedRouteService.recalculateGpxData(plannedRouteId);
                if (gpxResult) {
                    log.info(`${fnLogPrefix} GPX recalculated - Distance: ${gpxResult.distance_m}m, Elevation: ${gpxResult.elevation_gain_m}m`);
                    req.flash('success', `GPX-Daten neu berechnet: ${Math.round(gpxResult.distance_m / 1000 * 100) / 100} km, ${gpxResult.elevation_gain_m} Hm`);
                } else {
                    req.flash('warning', 'GPX-Neuberechnung fehlgeschlagen.');
                }
            } catch (error: any) {
                log.error(`${fnLogPrefix} GPX recalculation error:`, error);
                req.flash('error', 'Fehler bei der GPX-Neuberechnung.');
            }
        }

        // URLs hinzufügen - Parse die Form-Daten
        const parsedNewUrls = [];
        for (const key in req.body) {
            if (key.startsWith('new_urls[') && key.endsWith('][url]')) {
                const index = key.match(/\[(\d+)\]/)?.[1];
                if (index !== undefined) {
                    const url = req.body[key];
                    const title = req.body[`new_urls[${index}][title]`] || '';
                    const description = req.body[`new_urls[${index}][description]`] || '';
                    if (url && url.trim()) {
                        parsedNewUrls.push({
                            url: url.trim(),
                            title: title.trim(),
                            description: description.trim()
                        });
                    }
                }
            }
        }

        if (parsedNewUrls.length > 0) {
            for (const urlData of parsedNewUrls) {
                if (urlData.url && urlData.url.trim()) {
                    await plannedRouteUrlRepository.insertPlannedRouteUrl({
                        planned_route_id: plannedRouteId,
                        url: urlData.url.trim(),
                        title: urlData.title?.trim() || null,
                        description: urlData.description?.trim() || null
                    });
                }
            }
            log.info(`${fnLogPrefix} Added ${new_urls.length} new URLs`);
        }

        // Bilder verarbeiten
        log.info(`${fnLogPrefix} Checking for uploaded files:`, req.files);
        if (req.files && req.files.image_files) {
            const files = Array.isArray(req.files.image_files) ? req.files.image_files : [req.files.image_files];
            const uploadDir = path.join(config.paths.baseDir, 'public', 'uploads', 'planned_routes', plannedRouteId.toString());

            // Upload-Verzeichnis erstellen
            await fsExtra.ensureDir(uploadDir);

            for (const file of files) {
                if (file && file.name) {
                    try {
                        const fileExtension = path.extname(file.name);
                        const fileName = `${Date.now()}_${Math.random().toString(36).substr(2, 9)}${fileExtension}`;
                        const filePath = path.join(uploadDir, fileName);

                        await file.mv(filePath);

                        await plannedRouteImageRepository.insertPlannedRouteImage({
                            planned_route_id: plannedRouteId,
                            source: 'upload',
                            filename: fileName,
                            original_filename: file.name,
                            file_size: file.size
                        });

                        log.info(`${fnLogPrefix} Uploaded image: ${fileName}`);
                    } catch (error: any) {
                        log.error(`${fnLogPrefix} Error uploading image ${file.name}:`, error);
                    }
                }
            }
        }

        // Bilder von URLs herunterladen - Parse die Form-Daten
        const parsedImageUrls = [];
        for (const key in req.body) {
            if (key.startsWith('image_urls[') && key.endsWith('][url]')) {
                const index = key.match(/\[(\d+)\]/)?.[1];
                if (index !== undefined) {
                    const url = req.body[key];
                    const caption = req.body[`image_urls[${index}][caption]`] || '';
                    if (url && url.trim()) {
                        parsedImageUrls.push({ url: url.trim(), caption: caption.trim() });
                    }
                }
            }
        }



        if (parsedImageUrls.length > 0) {
            const uploadDir = path.join(config.paths.baseDir, 'public', 'uploads', 'planned_routes', plannedRouteId.toString());
            await fsExtra.ensureDir(uploadDir);

            for (const imageUrlData of parsedImageUrls) {
                if (imageUrlData.url && imageUrlData.url.trim()) {
                    try {
                        const sanitizedUrl = imageDownloadService.sanitizeImageUrl(imageUrlData.url.trim());
                        const downloadResult = await imageDownloadService.downloadImageFromUrl(
                            sanitizedUrl,
                            uploadDir,
                            10 * 1024 * 1024 // 10MB max
                        );

                        if (downloadResult) {
                            await plannedRouteImageRepository.insertPlannedRouteImage({
                                planned_route_id: plannedRouteId,
                                source: 'url',
                                source_url: sanitizedUrl,
                                filename: downloadResult.filename,
                                caption: imageUrlData.caption?.trim() || null,
                                file_size: downloadResult.fileSize
                            });

                            log.info(`${fnLogPrefix} Downloaded image from URL: ${downloadResult.filename}`);
                        }
                    } catch (error: any) {
                        log.error(`${fnLogPrefix} Error downloading image from ${imageUrlData.url}:`, error);
                        req.flash('warning', `Fehler beim Herunterladen des Bildes von ${imageUrlData.url}: ${error.message}`);
                    }
                }
            }
        }

        req.flash('success', 'Geplante Route erfolgreich aktualisiert.');
        return res.redirect(`/user/activity/planned/${plannedRouteId}`);

    } catch (error: any) {
        log.error(`${fnLogPrefix} Error:`, error);
        req.flash('error', 'Fehler beim Aktualisieren der geplanten Route.');
        return res.redirect(`/user/activity/planned/${plannedRouteId}/edit`);
    }
};

/**
 * Löscht ein Bild einer geplanten Route.
 */
export const deletePlannedRouteImage = async (req: Request, res: Response, next: NextFunction) => {
    const loggedInUser = res.locals.currentUser;
    const plannedRouteId = parseInt(req.params.plannedRouteId, 10);
    const imageId = parseInt(req.params.imageId, 10);
    const fnLogPrefix = `[Ctrl DeletePlannedRouteImage User:${loggedInUser.username} RouteID:${plannedRouteId} ImageID:${imageId}]`;

    if (isNaN(plannedRouteId) || isNaN(imageId)) {
        req.flash('error', 'Ungültige Parameter.');
        return res.redirect('/user/activities/planned');
    }

    try {
        const plannedRoute = await plannedRouteRepository.getPlannedRouteById(plannedRouteId);

        if (!plannedRoute || plannedRoute.user_id !== loggedInUser.id) {
            req.flash('error', 'Zugriff verweigert.');
            return res.redirect('/user/activities/planned');
        }

        // Bild aus Datenbank löschen
        const deleted = await plannedRouteImageRepository.deletePlannedRouteImage(imageId);

        if (deleted) {
            req.flash('success', 'Bild erfolgreich gelöscht.');
        } else {
            req.flash('error', 'Bild konnte nicht gelöscht werden.');
        }

        return res.redirect(`/user/activity/planned/${plannedRouteId}/edit`);

    } catch (error: any) {
        log.error(`${fnLogPrefix} Error:`, error);
        req.flash('error', 'Fehler beim Löschen des Bildes.');
        return res.redirect(`/user/activity/planned/${plannedRouteId}/edit`);
    }
};

/**
 * Löscht eine URL einer geplanten Route.
 */
export const deletePlannedRouteUrl = async (req: Request, res: Response, next: NextFunction) => {
    const loggedInUser = res.locals.currentUser;
    const plannedRouteId = parseInt(req.params.plannedRouteId, 10);
    const urlId = parseInt(req.params.urlId, 10);
    const fnLogPrefix = `[Ctrl DeletePlannedRouteUrl User:${loggedInUser.username} RouteID:${plannedRouteId} UrlID:${urlId}]`;

    if (isNaN(plannedRouteId) || isNaN(urlId)) {
        req.flash('error', 'Ungültige Parameter.');
        return res.redirect('/user/activities/planned');
    }

    try {
        const plannedRoute = await plannedRouteRepository.getPlannedRouteById(plannedRouteId);

        if (!plannedRoute || plannedRoute.user_id !== loggedInUser.id) {
            req.flash('error', 'Zugriff verweigert.');
            return res.redirect('/user/activities/planned');
        }

        // URL aus Datenbank löschen
        const deleted = await plannedRouteUrlRepository.deletePlannedRouteUrl(urlId);

        if (deleted) {
            req.flash('success', 'Link erfolgreich gelöscht.');
        } else {
            req.flash('error', 'Link konnte nicht gelöscht werden.');
        }

        return res.redirect(`/user/activity/planned/${plannedRouteId}/edit`);

    } catch (error: any) {
        log.error(`${fnLogPrefix} Error:`, error);
        req.flash('error', 'Fehler beim Löschen des Links.');
        return res.redirect(`/user/activity/planned/${plannedRouteId}/edit`);
    }
};

// ===== POI-CONTROLLER-FUNKTIONEN =====

/**
 * Zeigt die Seite mit den POIs des Benutzers an.
 */
export const showMyPOIsPage = async (req: Request, res: Response, next: NextFunction) => {
    const loggedInUser = res.locals.currentUser;
    const page = parseInt(req.query.page as string, 10) || 1;
    const limit = 20; // POIs pro Seite

    const { search, type, sort, order } = req.query as any;

    const fnLogPrefix = `[Ctrl MyPOIs User:${loggedInUser.username}]`;
    log.info(`${fnLogPrefix} Request for page ${page}`);

    try {
        const [pois, totalPOIs, poiTypesForFilter] = await Promise.all([
            poiRepository.getPOIsForUser(loggedInUser.id),
            // TODO: Implementiere countPOIsForUser wenn Paginierung benötigt wird
            Promise.resolve(0),
            poiRepository.getDistinctPOITypesForUser(loggedInUser.id)
        ]);

        // Lade Bild- und URL-Anzahl für alle POIs
        const poiIds = pois.map(poi => poi.id!);
        const [allImages, allUrls] = await Promise.all([
            poiIds.length > 0 ? poiImageRepository.getImagesByPOIIds(poiIds) : [],
            poiIds.length > 0 ? poiUrlRepository.getUrlsByPOIIds(poiIds) : []
        ]);

        // Gruppiere Bilder nach POI ID und zähle sie
        const imageCountByPOIId = allImages.reduce((acc, image) => {
            acc[image.poi_id] = (acc[image.poi_id] || 0) + 1;
            return acc;
        }, {} as Record<number, number>);

        // Gruppiere URLs nach POI ID und zähle sie
        const urlCountByPOIId = allUrls.reduce((acc, url) => {
            acc[url.poi_id] = (acc[url.poi_id] || 0) + 1;
            return acc;
        }, {} as Record<number, number>);

        // Füge Bild- und URL-Anzahl zu jedem POI hinzu
        const poisWithCounts = pois.map(poi => ({
            ...poi,
            imageCount: imageCountByPOIId[poi.id!] || 0,
            urlCount: urlCountByPOIId[poi.id!] || 0
        }));

        res.render('users/pois', {
            pageTitle: 'Meine POIs',
            pois: poisWithCounts,
            poiTypesForFilter: poiTypesForFilter,
            pagination: { currentPage: page, totalPages: 1, totalPOIs: pois.length, limit },
            filters: { search: search || '', type: type || '' },
            sort: { by: sort || 'created_at', order: order || 'DESC' }
        });

    } catch (error) {
        log.error(`${fnLogPrefix} Error:`, error);
        next(error);
    }
};

/**
 * Zeigt die Seite zum Hinzufügen eines neuen POI an.
 */
export const showAddPOIPage = async (req: Request, res: Response, next: NextFunction) => {
    const loggedInUser = res.locals.currentUser;
    const fnLogPrefix = `[Ctrl AddPOI User:${loggedInUser.username}]`;

    try {
        res.render('users/poi_add', {
            pageTitle: 'Neuen POI hinzufügen'
        });

    } catch (error: any) {
        log.error(`${fnLogPrefix} Error:`, error);
        next(error);
    }
};

/**
 * Behandelt das Hinzufügen eines neuen POI.
 */
export const handleAddPOI = async (req: Request, res: Response, next: NextFunction) => {
    const loggedInUser = res.locals.currentUser;
    const fnLogPrefix = `[Ctrl HandleAddPOI User:${loggedInUser.username}]`;

    try {
        const { poi_type, title, description, latitude, longitude } = req.body;

        // Validierung
        if (!title || !poi_type) {
            req.flash('error', 'Titel und POI-Typ sind erforderlich.');
            return res.redirect('/user/poi/add');
        }

        const lat = parseFloat(latitude);
        const lng = parseFloat(longitude);
        if (isNaN(lat) || isNaN(lng)) {
            req.flash('error', 'Ungültige Koordinaten.');
            return res.redirect('/user/poi/add');
        }

        // POI erstellen
        const poiData: POI = {
            user_id: loggedInUser.id,
            poi_type,
            title,
            description: description || null,
            latitude: lat,
            longitude: lng,
            source: 'manual'
        };

        const poiId = await poiRepository.insertPOI(poiData);

        if (poiId) {
            req.flash('success', 'POI erfolgreich erstellt.');
            return res.redirect(`/user/poi/${poiId}`);
        } else {
            req.flash('error', 'Fehler beim Erstellen des POI.');
            return res.redirect('/user/poi/add');
        }

    } catch (error: any) {
        log.error(`${fnLogPrefix} Error:`, error);
        req.flash('error', 'Fehler beim Erstellen des POI.');
        return res.redirect('/user/poi/add');
    }
};

/**
 * Zeigt die Detailseite eines POI an.
 */
export const showPOIDetailPage = async (req: Request, res: Response, next: NextFunction) => {
    const loggedInUser = res.locals.currentUser;
    const poiId = parseInt(req.params.poiId, 10);
    const fnLogPrefix = `[Ctrl POIDetail User:${loggedInUser.username} POIID:${poiId}]`;

    if (isNaN(poiId)) {
        req.flash('error', 'Ungültige POI-ID.');
        return res.redirect('/user/pois');
    }

    try {
        const poi = await poiRepository.getPOIById(poiId);

        if (!poi) {
            req.flash('error', 'POI nicht gefunden.');
            return res.redirect('/user/pois');
        }

        if (poi.user_id !== loggedInUser.id) {
            log.warn(`${fnLogPrefix} User attempted to view POI not owned by them.`);
            req.flash('error', 'Zugriff verweigert. Sie können nur eigene POIs ansehen.');
            return res.redirect('/user/pois');
        }

        // Lade zugehörige Bilder und URLs
        const [images, urls] = await Promise.all([
            poiImageRepository.getImagesByPOIId(poiId),
            poiUrlRepository.getUrlsByPOIId(poiId)
        ]);

        res.render('users/poi_detail', {
            pageTitle: `POI: ${poi.title}`,
            poi: poi,
            images: images,
            urls: urls
        });

    } catch (error: any) {
        log.error(`${fnLogPrefix} Error:`, error);
        next(error);
    }
};

/**
 * Zeigt die Bearbeitungsseite für einen POI an.
 */
export const showEditPOIPage = async (req: Request, res: Response, next: NextFunction) => {
    const loggedInUser = res.locals.currentUser;
    const poiId = parseInt(req.params.poiId, 10);
    const fnLogPrefix = `[Ctrl EditPOI User:${loggedInUser.username} POIID:${poiId}]`;

    if (isNaN(poiId)) {
        req.flash('error', 'Ungültige POI-ID.');
        return res.redirect('/user/pois');
    }

    try {
        const poi = await poiRepository.getPOIById(poiId);

        if (!poi) {
            req.flash('error', 'POI nicht gefunden.');
            return res.redirect('/user/pois');
        }

        if (poi.user_id !== loggedInUser.id) {
            log.warn(`${fnLogPrefix} User attempted to edit POI not owned by them.`);
            req.flash('error', 'Zugriff verweigert. Sie können nur eigene POIs bearbeiten.');
            return res.redirect('/user/pois');
        }

        // Lade zugehörige Bilder und URLs
        const [images, urls] = await Promise.all([
            poiImageRepository.getImagesByPOIId(poiId),
            poiUrlRepository.getUrlsByPOIId(poiId)
        ]);

        res.render('users/poi_edit', {
            pageTitle: `POI bearbeiten: ${poi.title}`,
            poi: poi,
            images: images,
            urls: urls
        });

    } catch (error: any) {
        log.error(`${fnLogPrefix} Error:`, error);
        next(error);
    }
};

/**
 * Behandelt die Aktualisierung eines POI.
 */
export const handleUpdatePOI = async (req: Request, res: Response, next: NextFunction) => {
    const loggedInUser = res.locals.currentUser;
    const poiId = parseInt(req.params.poiId, 10);
    const fnLogPrefix = `[Ctrl UpdatePOI User:${loggedInUser.username} POIID:${poiId}]`;

    if (isNaN(poiId)) {
        req.flash('error', 'Ungültige POI-ID.');
        return res.redirect('/user/pois');
    }

    try {
        const poi = await poiRepository.getPOIById(poiId);

        if (!poi || poi.user_id !== loggedInUser.id) {
            req.flash('error', 'POI nicht gefunden oder Zugriff verweigert.');
            return res.redirect('/user/pois');
        }

        const { poi_type, title, description, latitude, longitude } = req.body;

        // Validierung
        if (!title || !poi_type) {
            req.flash('error', 'Titel und POI-Typ sind erforderlich.');
            return res.redirect(`/user/poi/${poiId}/edit`);
        }

        const lat = parseFloat(latitude);
        const lng = parseFloat(longitude);
        if (isNaN(lat) || isNaN(lng)) {
            req.flash('error', 'Ungültige Koordinaten.');
            return res.redirect(`/user/poi/${poiId}/edit`);
        }

        // POI aktualisieren
        const updateData: Partial<POI> = {
            poi_type,
            title,
            description: description || null,
            latitude: lat,
            longitude: lng
        };

        const success = await poiRepository.updatePOI(poiId, updateData);

        if (!success) {
            req.flash('error', 'Fehler beim Aktualisieren des POI.');
            return res.redirect(`/user/poi/${poiId}/edit`);
        }

        // Neue URLs hinzufügen
        const newUrls = [];
        for (const key in req.body) {
            if (key.startsWith('new_urls[') && key.endsWith('][url]')) {
                const index = key.match(/\[(\d+)\]/)?.[1];
                if (index !== undefined) {
                    const url = req.body[key];
                    const title = req.body[`new_urls[${index}][title]`] || '';
                    const description = req.body[`new_urls[${index}][description]`] || '';
                    if (url && url.trim()) {
                        newUrls.push({
                            url: url.trim(),
                            title: title.trim(),
                            description: description.trim()
                        });
                    }
                }
            }
        }

        // URLs zur Datenbank hinzufügen
        for (const urlData of newUrls) {
            try {
                await poiUrlRepository.insertPOIUrl({
                    poi_id: poiId,
                    url: urlData.url,
                    title: urlData.title || null,
                    description: urlData.description || null,
                    display_order: 0
                });
                log.info(`${fnLogPrefix} Added URL: ${urlData.url}`);
            } catch (error: any) {
                log.error(`${fnLogPrefix} Error adding URL ${urlData.url}:`, error);
            }
        }

        // Neue Bilder von URLs herunterladen
        const imageUrls = [];
        for (const key in req.body) {
            if (key.startsWith('image_urls[') && key.endsWith('][url]')) {
                const index = key.match(/\[(\d+)\]/)?.[1];
                if (index !== undefined) {
                    const url = req.body[key];
                    const caption = req.body[`image_urls[${index}][caption]`] || '';
                    if (url && url.trim()) {
                        imageUrls.push({ url: url.trim(), caption: caption.trim() });
                    }
                }
            }
        }

        // Bilder herunterladen und speichern
        if (imageUrls.length > 0) {
            const uploadDir = path.join(config.paths.baseDir, 'public', 'uploads', 'pois', poiId.toString());
            await fsExtra.ensureDir(uploadDir);

            for (const imageData of imageUrls) {
                try {
                    const downloadedImage = await imageDownloadService.downloadImageFromUrl(imageData.url, uploadDir, 10 * 1024 * 1024, true);

                    if (downloadedImage) {
                        await poiImageRepository.insertPOIImage({
                            poi_id: poiId,
                            source: 'url',
                            source_url: imageData.url,
                            filename: downloadedImage.filename,
                            original_filename: downloadedImage.originalUrl,
                            caption: imageData.caption || null,
                            display_order: 0,
                            file_size: downloadedImage.fileSize,
                            thumbnail_filename: downloadedImage.thumbnail?.filename || null
                        });
                        log.info(`${fnLogPrefix} Downloaded and saved image: ${downloadedImage.filename}`);
                    }
                } catch (error: any) {
                    log.error(`${fnLogPrefix} Error downloading image ${imageData.url}:`, error);
                }
            }
        }

        // Datei-Uploads verarbeiten
        if (req.files && req.files.poi_images) {
            const files = Array.isArray(req.files.poi_images) ? req.files.poi_images : [req.files.poi_images];
            const uploadDir = path.join(config.paths.baseDir, 'public', 'uploads', 'pois', poiId.toString());
            await fsExtra.ensureDir(uploadDir);

            for (const file of files) {
                try {
                    // Verarbeite hochgeladene Datei
                    const timestamp = Date.now();
                    const randomSuffix = Math.random().toString(36).substr(2, 9);
                    const extension = path.extname(file.name).toLowerCase();
                    const filename = `${timestamp}_${randomSuffix}${extension}`;
                    const filePath = path.join(uploadDir, filename);

                    // Speichere die Datei
                    await file.mv(filePath);

                    const processedImage = {
                        filename,
                        originalFilename: file.name,
                        fileSize: file.size
                    };

                    if (processedImage) {
                        await poiImageRepository.insertPOIImage({
                            poi_id: poiId,
                            source: 'upload',
                            filename: processedImage.filename,
                            original_filename: processedImage.originalFilename,
                            caption: null,
                            display_order: 0,
                            file_size: processedImage.fileSize,
                            thumbnail_filename: null
                        });
                        log.info(`${fnLogPrefix} Uploaded and saved image: ${processedImage.filename}`);
                    }
                } catch (error: any) {
                    log.error(`${fnLogPrefix} Error processing uploaded image:`, error);
                }
            }
        }

        req.flash('success', 'POI erfolgreich aktualisiert.');
        return res.redirect(`/user/poi/${poiId}`);

    } catch (error: any) {
        log.error(`${fnLogPrefix} Error:`, error);
        req.flash('error', 'Fehler beim Aktualisieren des POI.');
        return res.redirect(`/user/poi/${poiId}/edit`);
    }
};

/**
 * Löscht ein POI-Bild.
 */
export const deletePOIImage = async (req: Request, res: Response, next: NextFunction) => {
    const loggedInUser = res.locals.currentUser;
    const poiId = parseInt(req.params.poiId, 10);
    const imageId = parseInt(req.params.imageId, 10);
    const fnLogPrefix = `[Ctrl DeletePOIImage User:${loggedInUser.username} POIID:${poiId} ImageID:${imageId}]`;

    if (isNaN(poiId) || isNaN(imageId)) {
        req.flash('error', 'Ungültige IDs.');
        return res.redirect('/user/pois');
    }

    try {
        const poi = await poiRepository.getPOIById(poiId);

        if (!poi || poi.user_id !== loggedInUser.id) {
            req.flash('error', 'POI nicht gefunden oder Zugriff verweigert.');
            return res.redirect('/user/pois');
        }

        const success = await poiImageRepository.deletePOIImage(imageId);

        if (success) {
            req.flash('success', 'Bild erfolgreich gelöscht.');
        } else {
            req.flash('error', 'Bild konnte nicht gelöscht werden.');
        }

        return res.redirect(`/user/poi/${poiId}/edit`);

    } catch (error: any) {
        log.error(`${fnLogPrefix} Error:`, error);
        req.flash('error', 'Fehler beim Löschen des Bildes.');
        return res.redirect(`/user/poi/${poiId}/edit`);
    }
};

/**
 * Löscht eine POI-URL.
 */
export const deletePOIUrl = async (req: Request, res: Response, next: NextFunction) => {
    const loggedInUser = res.locals.currentUser;
    const poiId = parseInt(req.params.poiId, 10);
    const urlId = parseInt(req.params.urlId, 10);
    const fnLogPrefix = `[Ctrl DeletePOIUrl User:${loggedInUser.username} POIID:${poiId} UrlID:${urlId}]`;

    if (isNaN(poiId) || isNaN(urlId)) {
        req.flash('error', 'Ungültige IDs.');
        return res.redirect('/user/pois');
    }

    try {
        const poi = await poiRepository.getPOIById(poiId);

        if (!poi || poi.user_id !== loggedInUser.id) {
            req.flash('error', 'POI nicht gefunden oder Zugriff verweigert.');
            return res.redirect('/user/pois');
        }

        const success = await poiUrlRepository.deletePOIUrl(urlId);

        if (success) {
            req.flash('success', 'Link erfolgreich gelöscht.');
        } else {
            req.flash('error', 'Link konnte nicht gelöscht werden.');
        }

        return res.redirect(`/user/poi/${poiId}/edit`);

    } catch (error: any) {
        log.error(`${fnLogPrefix} Error:`, error);
        req.flash('error', 'Fehler beim Löschen des Links.');
        return res.redirect(`/user/poi/${poiId}/edit`);
    }
};

/**
 * Löscht einen POI komplett inklusive aller zugehörigen Dateien.
 */
export const handleDeletePOI = async (req: Request, res: Response, next: NextFunction) => {
    const loggedInUser = res.locals.currentUser;
    const poiId = parseInt(req.params.poiId, 10);
    const fnLogPrefix = `[Ctrl DeletePOI User:${loggedInUser.username} POIID:${poiId}]`;

    if (isNaN(poiId)) {
        req.flash('error', 'Ungültige POI-ID.');
        return res.redirect('/user/pois');
    }

    try {
        const poi = await poiRepository.getPOIById(poiId);

        if (!poi || poi.user_id !== loggedInUser.id) {
            req.flash('error', 'POI nicht gefunden oder Zugriff verweigert.');
            return res.redirect('/user/pois');
        }

        log.info(`${fnLogPrefix} Deleting POI and associated files...`);

        // 1. POI-Bilder und Dateien löschen
        const poiImages = await poiImageRepository.getImagesByPOIId(poiId);
        if (poiImages.length > 0) {
            const imageBaseUploadDir = config.paths.imageBaseUploadPath;

            for (const image of poiImages) {
                // Bildpfade für POIs: /uploads/planned_routes/{poiId}/
                const poiImageDir = path.join(imageBaseUploadDir, 'planned_routes', String(poiId));

                // Verschiedene Bildgrößen löschen
                const imagesToDelete = [
                    path.join(poiImageDir, image.filename),
                    image.thumbnail_filename ? path.join(poiImageDir, image.thumbnail_filename) : null
                ].filter(Boolean) as string[];

                for (const imagePath of imagesToDelete) {
                    try {
                        await fs.unlink(imagePath);
                        log.info(`${fnLogPrefix} Deleted image file: ${imagePath}`);
                    } catch (fileError: any) {
                        if (fileError.code !== 'ENOENT') {
                            log.warn(`${fnLogPrefix} Error deleting image file ${imagePath}: ${fileError.message}`);
                        }
                    }
                }
            }

            // Leeren POI-Bildordner löschen
            try {
                const poiImageDir = path.join(imageBaseUploadDir, 'planned_routes', String(poiId));
                const filesInDir = await fs.readdir(poiImageDir);
                if (filesInDir.length === 0) {
                    await fs.rmdir(poiImageDir);
                    log.info(`${fnLogPrefix} Deleted empty POI image directory: ${poiImageDir}`);
                }
            } catch (dirError: any) {
                if (dirError.code !== 'ENOENT') {
                    log.warn(`${fnLogPrefix} Error deleting POI image directory: ${dirError.message}`);
                }
            }
        }

        // 2. POI aus Datenbank löschen (CASCADE löscht automatisch Bilder und URLs)
        const success = await poiRepository.deletePOI(poiId);

        if (success) {
            req.flash('success', 'POI und alle zugehörigen Dateien erfolgreich gelöscht.');
            log.info(`${fnLogPrefix} POI and associated files deleted successfully`);
        } else {
            req.flash('error', 'POI konnte nicht gelöscht werden.');
        }

        return res.redirect('/user/pois');

    } catch (error: any) {
        log.error(`${fnLogPrefix} Error:`, error);
        req.flash('error', 'Fehler beim Löschen des POI.');
        return res.redirect('/user/pois');
    }
};