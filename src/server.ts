// src/server.ts
import express, { Request, Response, NextFunction } from 'express';
import session from 'express-session';
import path from 'path';
import expressEjsLayouts from 'express-ejs-layouts';
import passport from 'passport';
import flash from 'connect-flash';
import fileUpload from 'express-fileupload';
import initializePassport from './config/passport-config';

import logger from './utils/logger';
import config from './config/config';

import authRoutes from './routes/authRoutes';
import adminProtectedRoutes from './routes/adminProtectedRoutes';
import userRoutes from './routes/userRoutes';
import viewRoutes from './routes/viewRoutes';
import geojsonRoutes from './routes/geojsonRoutes';
import apiRoutes from './routes/apiRoutes'; // Falls vorhanden
import stravaWebhookRoutes from './routes/stravaWebhookRoutes'; // NEU
import garminRoutes from './routes/garminRoutes'; // Garmin-Routen

// Import für die Strava API Queue und Sport-Typen
import * as stravaApiQueueProcessor from './services/stravaApiQueueProcessor';
import * as sportTypes from './utils/sportTypes';
import * as publicUserRepository from './db/publicUserRepository';


import { loadUserNotificationsSummary } from './middleware/loadUserNotificationsSummary';
import { requestLogger, suspiciousRequestLogger } from './middleware/requestLogger';
import { deviceDetection } from './middleware/deviceDetection';
import { cspMiddleware, relaxedCspMiddleware } from './middleware/cspMiddleware';

const log = logger.getLogger(__filename);
const app = express();

// Konfiguriere Express, um Proxy-Header zu vertrauen
// Dies ist wichtig, damit req.ip die tatsächliche Client-IP enthält
app.set('trust proxy', true);

// View Engine Setup
app.set('views', path.join(__dirname, '..', 'views'));
app.set('view engine', 'ejs');
app.use(expressEjsLayouts);
app.set('layout', 'layouts/main_layout');

// Standard Middleware
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));
app.use(express.static(path.join(__dirname, '..', 'public')));
// File Upload Middleware
app.use(fileUpload({
    createParentPath: true,
    // Wir verwenden die direkte Option statt limits
    abortOnLimit: true,
    responseOnLimit: 'Datei zu groß (max. 50MB).',
    useTempFiles: true,
    tempFileDir: '/tmp/'
}));
// Ihre static-Routen für GPX_files und photosBaseUrl
app.use('/GPX_files', express.static(config.paths.gpxBaseDir));
app.use(config.maps.photoBaseUrl, express.static(config.paths.imageBaseUploadPath));
//photoBaseUrl: env('MAPS_PHOTO_BASE_URL', '/uploads/activity_photos'),
//imageBaseUploadPath: env('IMAGE_UPLOAD_PATH', path.join(path.resolve(__dirname, '..', '..'), 'public', 'uploads', 'activity_photos')),

// Session-Konfiguration
app.use(session({
    name: config.session.name || 'connect.sid',
    secret: config.session.secret,
    // @ts-ignore
    store: new (require('express-mysql-session')(session))({
        host: config.db.host,
        port: config.db.port,
        user: config.db.user,
        password: config.db.password,
        database: config.db.database,
        clearExpired: true,
        checkExpirationInterval: 900000,
        expiration: config.session.cookie.maxAge,
        createDatabaseTable: true,
        schema: { tableName: 'sessions', columnNames: { session_id: 'session_id', expires: 'expires', data: 'data' } }
    }),
    resave: config.session.resave,
    saveUninitialized: config.session.saveUninitialized,
    cookie: config.session.cookie
}));


// === Passport Middleware Initialisierung ===
initializePassport(passport); // Rufen Sie Ihre Konfigurationsfunktion auf
app.use(passport.initialize());
app.use(passport.session()); // Für persistente Login-Sessions

// Flash-Middleware für Benachrichtigungen
app.use(flash());

// Content Security Policy Middleware (für Google APIs)
app.use(cspMiddleware);

// IP-basiertes Logging (nach Passport, damit Benutzerinformationen verfügbar sind)
app.use(requestLogger);
app.use(suspiciousRequestLogger);

// Device Detection Middleware (vor den globalen Locals)
app.use(deviceDetection);

// Globale Middleware für res.locals (currentUser, config etc.)
app.use((req: Request, res: Response, next: NextFunction) => {
    const fnLogPrefix = '[GlobalLocalsMiddleware]';
    log.debug(`${fnLogPrefix} Path: ${req.path}`);
    log.debug(`${fnLogPrefix} req.session.user (vorhanden?): ${!!req.session.user}`);
    if (req.session.user) {
        log.debug(`${fnLogPrefix} req.session.user content:`, req.session.user);
    }
    log.debug(`${fnLogPrefix} req.user (von Passport, vorhanden?): ${!!req.user}`);
    if (req.user) {
        log.debug(`${fnLogPrefix} req.user content:`, req.user);
        res.locals.currentUser = req.user;
        res.locals.isAdmin = (req.user as any).role === 'admin';
        log.debug(`${fnLogPrefix} res.locals.currentUser GESETZT auf:`, res.locals.currentUser);
    } else {
        res.locals.currentUser = null;
        res.locals.isAdmin = false;
        log.debug(`${fnLogPrefix} req.user NICHT vorhanden, res.locals.currentUser auf null gesetzt.`);
    }
    res.locals.config = config;
    res.locals.pageTitle = config.app.name || 'Master-Map';

    // Flash-Nachrichten in res.locals verfügbar machen
    // Diese werden in den Layouts angezeigt
    res.locals.successMessage = req.flash('success');
    res.locals.errorMessage = req.flash('error');
    res.locals.infoMessage = req.flash('info');

    res.locals.currentYear = new Date().getFullYear();
    next();
});

// Middleware für den Benachrichtigungs-Counter
app.use(loadUserNotificationsSummary);

// --- Routen-Definitionen ---
app.use('/auth', authRoutes);
app.use('/admin', adminProtectedRoutes);
app.use('/user', userRoutes);
app.use('/show', viewRoutes); // Für öffentliche Karten und ggf. Stats (Stats-Seite wurde entfernt)
app.use('/api/geojson', geojsonRoutes);
app.use('/api', apiRoutes); // Für andere API-Endpunkte
app.use(stravaWebhookRoutes); // NEU: Webhook Routen einbinden (z.B. auf Root-Ebene oder unter /strava)
app.use('/user/garmin', garminRoutes); // Garmin-Routen



// Root-Route für die Willkommensseite
app.get('/', async (req: Request, res: Response) => {
    const fnLogPrefix = '[Root Route]';

    // Prüfe, ob der Benutzer authentifiziert ist (Passport.js Methode)
    if (req.isAuthenticated && req.isAuthenticated()) {
        // Benutzer ist über Passport authentifiziert
        if (req.user && (req.user as any).role === 'admin') {
            log.info(`${fnLogPrefix} Authenticated admin user ${(req.user as any).username} redirected to admin dashboard`);
            return res.redirect('/admin/dashboard');
        } else if (req.user) {
            log.info(`${fnLogPrefix} Authenticated user ${(req.user as any).username} redirected to user dashboard`);
            return res.redirect('/user/dashboard');
        }
    }

    // Fallback: Prüfe session.user (für den Fall, dass Passport nicht verwendet wird)
    if (req.session && req.session.user) {
        if (req.session.user.role === 'admin') {
            log.info(`${fnLogPrefix} Session admin user ${req.session.user.username} redirected to admin dashboard`);
            return res.redirect('/admin/dashboard');
        } else {
            log.info(`${fnLogPrefix} Session user ${req.session.user.username} redirected to user dashboard`);
            return res.redirect('/user/dashboard');
        }
    }

    try {
        // Hole zufällige Benutzer mit öffentlichen Karten
        const publicMapUsers = await publicUserRepository.getRandomPublicMapUsers(5);

        // Kein authentifizierter Benutzer, zeige die Willkommensseite
        log.debug(`${fnLogPrefix} No authenticated user, showing welcome page with ${publicMapUsers.length} public maps`);
        res.render('welcome', {
            pageTitle: `Willkommen bei ${config.app.name || 'Master-Map'}`,
            layout: 'layouts/simple_layout', // Ein anderes Layout für die Willkommensseite, ohne User-Header etc.
            publicMapUsers: publicMapUsers // Übergebe die öffentlichen Karten an die View
        });
    } catch (error) {
        log.error(`${fnLogPrefix} Error fetching public map users: ${error}`);
        // Fallback ohne öffentliche Karten
        res.render('welcome', {
            pageTitle: `Willkommen bei ${config.app.name || 'Master-Map'}`,
            layout: 'layouts/simple_layout'
        });
    }
});

// --- Fehlerbehandlung ---
// 404 Handler (Fallback für nicht gefundene Routen)
app.use((req: Request, res: Response, next: NextFunction) => {
    log.warn(`[404 Handler] Route nicht gefunden: ${req.method} ${req.originalUrl}`);
    res.status(404).render('error', { // Ein allgemeines error.ejs Template
        pageTitle: 'Seite nicht gefunden (404)',
        message: 'Die angeforderte Seite konnte nicht gefunden werden.',
        statusCode: 404,
        layout: 'layouts/simple_layout' // Auch hier ein einfaches Layout
    });
});

// Globaler Fehlerbehandlungs-Middleware (muss als Letztes definiert werden)
app.use((err: any, req: Request, res: Response, next: NextFunction) => {
    log.error(`[GlobalErrorHandler] Unerwarteter Fehler aufgetreten: ${err.message}`, {
        stack: err.stack,
        url: req.originalUrl,
        method: req.method,
        ip: req.ip
    });
    const statusCode = err.status || 500;
    res.status(statusCode).render('error', {
        pageTitle: `Fehler ${statusCode}`,
        message: err.expose ? err.message : 'Ein interner Serverfehler ist aufgetreten.',
        statusCode: statusCode,
        errorDetail: process.env.NODE_ENV === 'development' ? err.stack : undefined,
        layout: 'layouts/simple_layout'
    });
});


// Initialisiere den Strava API Queue Prozessor
stravaApiQueueProcessor.initQueueProcessor();
log.info('Strava API Queue Prozessor initialisiert');

// Initialisiere den Sport-Typen-Cache
sportTypes.initializeSyncCache()
    .then(() => {
        log.info('Sport-Typen-Cache initialisiert');
    })
    .catch(error => {
        log.error('Fehler beim Initialisieren des Sport-Typen-Caches:', error);
    });

// Server starten
const PORT = config.server.port;
app.listen(PORT, () => {
    log.info(`Server läuft auf http://localhost:${PORT}`);
});