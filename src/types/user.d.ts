// src/types/user.d.ts
export type UserRole = 'admin' | 'user';

export interface StravaTokenData {
  accessToken: string | null;
  refreshToken: string | null;
  expiresAt: number | null; // Unix timestamp (Sekunden)
  scope?: string | null;
  athleteId?: string | null;
}

export interface GoogleTokenData {
  accessToken: string | null;
  refreshToken: string | null;
  expiresAt: number | null; // Unix timestamp (Millisekunden)
  scope?: string | null;
}

export interface GoogleDriveTokenData {
  accessToken: string | null;
  refreshToken: string | null;
  expiresAt: number | null; // Unix timestamp (Millisekunden)
  scope?: string | null;
}

export interface GooglePhotosPickerTokenData {
  accessToken: string | null;
  refreshToken: string | null;
  expiresAt: number | null; // Unix timestamp (Millisekunden)
  scope?: string | null;
}


export interface User {
  id: number;
  username: string;
  role: UserRole;
  email?: string | null; // NEU
  created_at?: Date;
  map_visibility?: 'private' | 'public';
  default_map_center_lat?: number | null;
  default_map_center_lng?: number | null;
  default_map_zoom?: number | null;

  // Benachrichtigungseinstellungen
  notify_on_friend_request_email?: boolean; // NEU
  notify_on_activity_shared_email?: boolean; // NEU

  // Strava Tokens
  strava_access_token?: string | null;
  strava_refresh_token?: string | null;
  strava_expires_at?: number | null;
  strava_scope?: string | null;
  strava_athlete_id?: string | null;

  // Google Photos Tokens
  google_access_token?: string | null;
  google_refresh_token?: string | null;
  google_expires_at?: number | null;
  google_scope?: string | null;

  // Google Drive Tokens
  google_drive_access_token?: string | null;
  google_drive_refresh_token?: string | null;
  google_drive_expires_at?: number | null;
  google_drive_scope?: string | null;
  google_drive_folder_id?: string | null;
  // Google Photos Picker API Tokens
  google_photos_picker_access_token?: string | null;
  google_photos_picker_refresh_token?: string | null;
  google_photos_picker_expires_at?: number | null;
  google_photos_picker_scope?: string | null;

  // Komoot-Verbindung
  komoot_connected?: boolean;

  // Garmin-Verbindung
  garmin_connected?: boolean;

}


// Für DB-Abfragen, die den Hash enthalten
export interface UserForDb { // Wird für DB-Abfragen verwendet, die nicht alle Felder brauchen
    id: number;
    username: string;
    password_hash: string;
    role: UserRole;
    email?: string | null; // NEU
    created_at?: Date; // Von DB als Date oder String, im Code als Date
    map_visibility: 'private' | 'public'; // Ist schon in der DB-Tabelle
    default_map_center_lat?: number | null;
    default_map_center_lng?: number | null;
    default_map_zoom?: number | null;
    notify_on_friend_request_email: boolean; // NEU (aus DB, hat Default)
    notify_on_activity_shared_email: boolean; // NEU (aus DB, hat Default)
    // KEINE Tokens hier, die sind im User-Interface und ggf. spezifischen Token-Funktionen
}