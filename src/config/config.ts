// src/config/config.ts
import dotenv from 'dotenv';
import path from 'path';
import packageJson from '../../package.json'; // Stellt sicher, dass der Pfad zu deiner package.json korrekt ist

dotenv.config(); // Lädt .env Datei

// Helper-Funktionen mit Typisierung
const env = (key: string, defaultValue?: string): string => {
    const value = process.env[key];
    if (value === undefined) {
        if (defaultValue === undefined) {
            // console.warn(`Umgebungsvariable ${key} nicht gesetzt und kein Standardwert vorhanden!`);
            // Für kritische Werte wie DB-Passwörter könntest du hier einen Fehler werfen.
            return ''; // Fallback für nicht-kritische Werte
        }
        return defaultValue;
    }
    return value;
};

const envInt = (key: string, defaultValue: number): number => {
    const value = process.env[key];
    const parsed = parseInt(value || '', 10);
    return isNaN(parsed) ? defaultValue : parsed;
};

const envBool = (key: string, defaultValue: boolean): boolean => {
    const value = process.env[key];
    if (value === undefined || value === null) return defaultValue;
    return value.toLowerCase() === 'true' || value === '1';
};

const envFloat = (key: string, defaultValue: number): number => {
    const value = process.env[key];
    const parsed = parseFloat(value || '');
    return isNaN(parsed) ? defaultValue : parseFloat(defaultValue.toString()); // Fallback auf Default, wenn Parsing fehlschlägt
};



// === INTERFACE DEFINITIONS (basierend auf Ihrer Struktur) ===
export interface StravaWebhookConfig { // NEU
    verifyToken: string;
    callbackBasePath?: string; // z.B. '/strava/webhook'
}


export interface EmailConfig {
    service?: string; // z.B. 'gmail' (optional, wenn host/port direkt gesetzt werden)
    host: string;    // SMTP-Host (für Gmail: 'smtp.gmail.com')
    port: number;    // SMTP-Port (für Gmail mit SSL: 465, für TLS: 587)
    secure: boolean; // true für SSL (Port 465), false für TLS (Port 587, aber secureConnection wird dann true)
    auth: {
        user: string;    // Ihre Gmail-Adresse
        pass: string;    // Ihr Gmail App-Passwort (NICHT Ihr normales Passwort)
    };
    fromAddress: string; // Standard "From"-Adresse, z.B. "Master-Map Benachrichtigungen <<EMAIL>>"
    // Für OAuth2 (später):
    // authType?: 'OAuth2' | 'Login';
    // clientId?: string;
    // clientSecret?: string;
    // refreshToken?: string;
    // accessToken?: string;
}

export interface DbConfig { // Ihre DatabaseConfig umbenannt zu DbConfig für Konsistenz
    host: string;
    port: number; // Hinzugefügt, falls benötigt, sonst entfernen
    user: string;
    password?: string;
    database: string; // Umbenannt von name zu database für Konsistenz
    connectionLimit: number;
}

export interface ServerConfig {
    port: number;
    nodeEnv: string; // Aus Ihrer //_
    sessionSecret: string; // Aus Ihrer //_
    sessionMaxAge: number; // Aus Ihrer //_
    baseUrl: string;
}

export interface SessionConfig { // Diese Struktur ist Standard und gut
    name?: string; // Optional: Name des Session-Cookies
    secret: string;
    resave: boolean;
    saveUninitialized: boolean;
    cookie: {
        secure: boolean;
        httpOnly: boolean;
        maxAge: number;
        sameSite: 'lax' | 'strict' | 'none' | boolean;
    };
}

export interface AppDetailsConfig {
    name: string;
    version: string;
}

export interface StravaConfig {
    clientId: number; // Geändert zu number, da Strava IDs Zahlen sind
    clientSecret: string;
    redirectUri: string;
    authUrl: string; // Aus Ihrer //_
    tokenUrl: string;
    apiBaseUrl: string;
    activitiesPerPage: number;
    defaultSyncNumPerPage: number; // Aus Ihrer //_
    photoSizeForDownload: number;
    streamTypesToRequest: string;
}

export interface GooglePhotosConfig {
    clientId: string; // Optional, wenn nicht immer gesetzt
    clientSecret: string;
    redirectUri: string;
    authUrl: string; // Aus Ihrer //_
    tokenUrl: string; // Aus Ihrer //_
    scopes: string[];
    albumPageSize?: number; // Optional gemacht, falls nicht immer benötigt
}

export interface GoogleDriveConfig {
    clientId: string;
    clientSecret: string;
    redirectUri: string;
    authUrl: string;
    tokenUrl: string;
    scopes: string[];
    folderPageSize?: number;
    webhookEnabled: boolean;
    webhookUrl: string;
    webhookSecret: string;
}

export interface PathsConfig {
    baseDir: string; // Wird oft als Projekt-Root verwendet
    projectRoot: string; // Aus Ihrer //_
    gpxBaseDir: string;
    plannedGpxSubDir: string; // Aus Ihrer //_
    photosBaseDir: string; // Hauptverzeichnis für verarbeitete Fotos
    imageBaseUploadPath: string; // Für Roh-Uploads
    tempDir: string; // Aus Ihrer //_
}

export interface LoggingConfig {
    level: 'debug' | 'info' | 'warn' | 'error';
    logToConsole: boolean; // Hinzugefügt für Konsistenz
    logToFile: boolean;   // Hinzugefügt für Konsistenz
    logFilePath: string;  // Hinzugefügt für Konsistenz
    pm2LogPath: string; // Aus Ihrer //_
    appErrorLogPath: string; // Aus Ihrer //_
    appOutLogPath: string; // Aus Ihrer //_
    ipLogPath: string; // Pfad für IP-basiertes Logging
    maxLogLinesAdmin: number; // Aus Ihrer //_
    serviceLogsDir: string; // Verzeichnis für dienstspezifische Logs
    serviceLogPaths: { [serviceName: string]: string }; // Pfade für dienstspezifische Logs
}

export interface ActivityProcessingDetailsConfig { // Umbenannt von Ihrem "processing" für Klarheit
    pauseSpeedThresholdMs: number;
    minPauseDurationSec: number;
    displayTrackSmoothingWindow: number;
    gradientThresholdFlatPositive: number;
    gradientThresholdFlatNegative: number;
    minSectionLengthMeters: number;
    mergeShortFlatBetweenDownhillsMeters: number;
    finalFilterMinLengthMeters: number;
    finalFilterMinAvgGradient: number;
    extendSegmentStartMeters: number;
    extendSegmentEndMeters: number;
    fussTypes: string[]; // Von mir hinzugefügt, war in Ihrem activities.processing
    radTypes: string[];  // Von mir hinzugefügt, war in Ihrem activities.processing
}

export interface SpeedThresholds {
    max_speed_mps: number;
    average_speed_mps: number;
}

export interface ActivitiesConfig { // Ihre detaillierte ActivitiesConfig
    ignoreTypes: string[];
    adminBrowserPageLimit: number;
    photoMigrationBatchSize: number;
    tokenValidBufferSeconds: number; // War bei mir, gut hier zu haben
    photoConsistencyBatchSize: number;
    processing: ActivityProcessingDetailsConfig; // Verwendet das umbenannte Interface
    speedThresholds: {
        default: SpeedThresholds;
        Ride?: SpeedThresholds;
        EBikeRide?: SpeedThresholds;
        Run?: SpeedThresholds;
        Walk?: SpeedThresholds;
        Hike?: SpeedThresholds;
        AlpineSki?: SpeedThresholds;
        [key: string]: SpeedThresholds | undefined;
    };
    // Felder aus meiner ActivityProcessingConfig, die hierher passen könnten:
    minMovingTimeForGpx?: number; // Ist in Ihrer ActivityProcessingDetailsConfig
}


export interface StravaApiLimitsConfig {
    readShortTermLimit: number;
    readShortTermWindowSeconds: number;
    readDailyLimit: number;
    readDailyWindowSeconds: number;
}

export interface MapLayerOption {
    maxZoom?: number;
    maxNativeZoom?: number;
    attribution?: string;
}
export interface MapLayerConfig {
    name: string;
    url: string;
    options?: MapLayerOption;
    active?: boolean;
}

export interface PiControlViewConfig {
    desiredTerrainExaggeration: number;
    infoUpdateIntervalMs: number;
    defaultSpeedFactor: number;
    maxSpeedFactor: number;
    minSpeedFactor: number;
    trackCasingColor: string;
    trackCasingWidth: number;
    trackMainLineColor: string;
    trackMainLineWidth: number;
    trackCasingOpacity: number;
    lookAheadDistanceMeters: number;
    targetSpeedMetersPerSecond: number;
    pauseProximityThresholdMeters: number;
    autoPauseDurationMs: number;
    pauseConsolidationDistanceMeters: number;
    piControllerPosKp: number;
    piControllerPosKi: number;
    piControllerPosIntegralLimit: number;
    piControllerBearingKp: number;
    piControllerBearingKi: number;
    piControllerBearingIntegralLimit: number;
    speedSliderMin: number;
    speedSliderMax: number;
    speedSliderMinSpeedMps: number;
    speedSliderMaxSpeedMps: number;
}

export interface StyleColorsConfig {
    activityTypes: { [key: string]: string; Default: string; };
    plannedRoute: string;
    piTrack: { casing: string; mainLine: string; };
    debugTracks: { originalRaw: string; processedDisplay: string; downhillSegment: string; };
    elevationAnnotations: { enterDownhillLine: string; exitDownhillLine: string; downhillSegmentBoxBg: string; downhillSegmentBoxBorder: string; };
    anchorMarkers: { [key: string]: string; };
}

export interface MapsConfig {
    mapTilerApiKey: string; // Aus Ihrer Struktur
    thunderforestApiKey: string; // Von mir hinzugefügt, wichtig für Thunderforest
    baseLayers: MapLayerConfig[]; // Verwendet Ihr MapLayerConfig
    defaultMapOptions: { // Kombiniert
        zoom: number;
        center: [number, number];
        attributionControl?: boolean;
        tapTolerance?: number;
    };
    styleColors: StyleColorsConfig; // Verwendet Ihr StyleColorsConfig
    geojsonStyleDefault: { weight: number; opacity: number; }; // Verwendet Ihre Definition
    plannedRouteStyle: { weight: number; opacity: number; dashArray: string; }; // Verwendet Ihre Definition
    photoBaseUrl: string; // Aus Ihrer //_
    piControlView: PiControlViewConfig; // Verwendet Ihr PiControlViewConfig
}

export interface ImageSizeDetailConfig {
    width?: number;
    quality: number;
    suffix: string;
}
export interface ImagesConfig {
    processPhotos: boolean; // Von mir hinzugefügt
    photoDownloadSize: string; // Von mir hinzugefügt (z.B. 'd' für Google)
    photoDisplaySizes: { small: number; medium: number; }; // Von mir hinzugefügt (vereinfacht)
    photoQuality: number; // Von mir hinzugefügt (JPEG Qualität)
    // Ihre detailliertere Struktur:
    sizes: {
        small: ImageSizeDetailConfig;
        medium: ImageSizeDetailConfig;
        original: ImageSizeDetailConfig;
    };
    defaultFormat: string;
    maxUploadFileSizeMb: number;
}

// Komoot-Konfiguration
export interface KomootConfig {
    enabled: boolean;
    komootGpxPath: string;
    gpxUploadDir: string;
}

// Garmin-Konfiguration
export interface GarminConfig {
    enabled: boolean;
    garminBridgePath: string;
    gpxUploadDir: string;
}

// === MAIN AppConfig INTERFACE ===
export interface AppConfig {
    env: string;
    server: ServerConfig;
    db: DbConfig;
    email: EmailConfig;
    session: SessionConfig;
    app: AppDetailsConfig;
    strava: StravaConfig;
    stravaWebhook: StravaWebhookConfig;
    googlePhotos: GooglePhotosConfig;
    googleDrive: GoogleDriveConfig;
    paths: PathsConfig;
    logging: LoggingConfig;
    activities: ActivitiesConfig; // Verwendet Ihre detaillierte ActivitiesConfig
    stravaApiLimits: StravaApiLimitsConfig;
    maps: MapsConfig;
    images: ImagesConfig;
    gpxAuthorName?: string;
    komoot: KomootConfig; // Komoot-Konfiguration
    garmin: GarminConfig; // Garmin-Konfiguration
}

// === CONFIGURATION OBJECT INITIALIZATION ===
const appNameFromEnv = env('APP_NAME', 'Master-Map');

const appConfig: AppConfig = {
    env: env('NODE_ENV', 'development'),
    server: {
        port: envInt('PORT', 3000),
        nodeEnv: env('NODE_ENV', 'development'),
        sessionSecret: env('SESSION_SECRET', 'bitte-unbedingt-aendern-123!'),
        sessionMaxAge: envInt('SESSION_MAX_AGE_MS', 24 * 60 * 60 * 1000),
        baseUrl: env('BASE_URL', `http://localhost:${envInt('PORT', 3000)}`),
    },
    db: { // Initialisiert mit Ihren Feldnamen (database statt name)
        host: env('DB_HOST', 'localhost'),
        port: envInt('DB_PORT', 3306), // Hinzugefügt
        user: env('DB_USER', 'strava_user'), // Aus Ihrer Config
        password: env('DB_PASSWORD', env('MYSQL_PW')), // Nimmt MYSQL_PW, wenn DB_PASSWORD nicht gesetzt
        database: env('DB_NAME', 'strava_activities'), // Aus Ihrer Config
        connectionLimit: envInt('DB_CONNECTION_LIMIT', 10),
    },
    email: {
        service: env('EMAIL_SERVICE', 'gmail'), // Standard auf Gmail, wenn Sie Gmails SMTP direkt verwenden wollen
        host: env('EMAIL_HOST', 'smtp.gmail.com'),
        port: envInt('EMAIL_PORT', 465), // 465 für SSL, 587 für TLS
        secure: envBool('EMAIL_SECURE', true), // true, wenn Port 465
        auth: {
            user: env('EMAIL_USER', '<EMAIL>'), // Ihre Gmail-Adresse
            pass: env('EMAIL_PASS', ''),                  // Ihr Gmail App-Passwort (aus .env laden!)
        },
        fromAddress: env('EMAIL_FROM_ADDRESS', `"${appNameFromEnv}" <<EMAIL>>`),
    },
    session: {
        // Name des Session-Cookies. Wenn nicht gesetzt, verwendet express-session 'connect.sid'.
        // Es ist gut, einen spezifischen Namen zu definieren.
        name: env('SESSION_COOKIE_NAME', 'map.session.sid'), // Beispiel für einen spezifischen Namen
        secret: env('SESSION_SECRET', 'IhrSehrGeheimesSessionSecretHierAendern!UndLangMachen!'),
        resave: false, // Nicht bei jeder Anfrage speichern, nur wenn sich die Session ändert
        saveUninitialized: false, // Keine Session für unmodifizierte, neue Sessions speichern
        cookie: {
            secure: env('NODE_ENV', 'development') === 'production', // Cookie nur über HTTPS senden (in Produktion)
            httpOnly: true, // Verhindert Zugriff auf Cookie über clientseitiges JavaScript
            maxAge: envInt('SESSION_MAX_AGE_MS', 24 * 60 * 60 * 1000), // 24 Stunden Gültigkeit
            sameSite: 'lax', // Schutz gegen CSRF-Angriffe. 'lax' ist ein guter Standard.
        },
    },
    app: {
        name: appNameFromEnv,
        version: packageJson.version || '1.0.0',
    },
    strava: {
        clientId: envInt('STRAVA_CLIENT_ID', 0), // Strava Client ID ist eine Zahl
        clientSecret: env('STRAVA_CLIENT_SECRET', ''),
        redirectUri: env('STRAVA_REDIRECT_URI', `http://localhost:${envInt('PORT', 3000)}/auth/strava/callback`),
        authUrl: env('STRAVA_AUTH_URL', 'https://www.strava.com/oauth/authorize'),
        tokenUrl: env('STRAVA_TOKEN_URL', 'https://www.strava.com/oauth/token'),
        apiBaseUrl: env('STRAVA_API_BASE_URL', 'https://www.strava.com/api/v3'),
        activitiesPerPage: envInt('STRAVA_ACTIVITIES_PER_PAGE', 50),
        defaultSyncNumPerPage: envInt('DEFAULT_SYNC_NUM_PER_PAGE', 30),
        photoSizeForDownload: envInt('STRAVA_PHOTO_SIZE_DOWNLOAD', 2048), // Ihre höhere Auflösung
        streamTypesToRequest: env('STRAVA_STREAM_TYPES', 'time,distance,latlng,altitude,velocity_smooth,heartrate,cadence,watts,temp,grade_smooth'),
    },
    stravaWebhook: { // NEU
        verifyToken: env('STRAVA_WEBHOOK_VERIFY_TOKEN', 'DEIN_GEHEIMER_VERIFY_TOKEN_HIER_AENDERN'),
        callbackBasePath: '/strava/webhook', // Basis-Pfad für den Webhook-Endpunkt
    },
    googlePhotos: {
        clientId: env('GOOGLE_CLIENT_ID', ''),
        clientSecret: env('GOOGLE_CLIENT_SECRET', ''),
        redirectUri: env('GOOGLE_REDIRECT_URI', `http://localhost:${envInt('PORT', 3000)}/auth/google/callback`),
        authUrl: env('GOOGLE_AUTH_URL', 'https://accounts.google.com/o/oauth2/v2/auth'),
        tokenUrl: env('GOOGLE_TOKEN_URL', 'https://oauth2.googleapis.com/token'),
        // WICHTIG: photoslibrary.readonly wurde am 1. April 2025 von Google entfernt!
        // Für das Durchsuchen von Benutzerfotos muss die neue Google Photos Picker API verwendet werden
        scopes: ['https://www.googleapis.com/auth/photoslibrary.readonly.appcreateddata'],
        albumPageSize: envInt('GOOGLE_ALBUM_PAGE_SIZE', 50),
    },
    googleDrive: {
        clientId: env('GOOGLE_CLIENT_ID', ''),
        clientSecret: env('GOOGLE_CLIENT_SECRET', ''),
        redirectUri: env('GOOGLE_REDIRECT_URI', `http://localhost:${envInt('PORT', 3000)}/auth/google/callback`),
        authUrl: env('GOOGLE_AUTH_URL', 'https://accounts.google.com/o/oauth2/v2/auth'),
        tokenUrl: env('GOOGLE_TOKEN_URL', 'https://oauth2.googleapis.com/token'),
        scopes: ['https://www.googleapis.com/auth/drive.readonly'],
        folderPageSize: envInt('GOOGLE_DRIVE_FOLDER_PAGE_SIZE', 50),
        webhookEnabled: envBool('GOOGLE_DRIVE_WEBHOOK_ENABLED', false),
        webhookUrl: env('GOOGLE_DRIVE_WEBHOOK_URL', `${env('BASE_URL', `http://localhost:${envInt('PORT', 3000)}`)}/api/google-drive/webhook`),
        webhookSecret: env('GOOGLE_DRIVE_WEBHOOK_SECRET', 'change-this-webhook-secret'),
    },
    paths: {
        baseDir: path.resolve(__dirname, '..', '..'), // Annahme: config.ts ist in src/config
        projectRoot: path.resolve(__dirname, '..', '..'), // Ähnlich baseDir
        gpxBaseDir: env('GPX_FILES_DIR', path.join(path.resolve(__dirname, '..', '..'), 'GPX_files')),
        plannedGpxSubDir: env('PLANNED_GPX_SUBDIR', 'planned'),
        photosBaseDir: env('PHOTOS_BASE_DIR', path.join(path.resolve(__dirname, '..', '..'), 'public', 'activity_photos_processed')),
        imageBaseUploadPath: env('IMAGE_UPLOAD_PATH', path.join(path.resolve(__dirname, '..', '..'), 'public', 'uploads', 'activity_photos')),
        tempDir: env('TEMP_DIR', path.join(path.resolve(__dirname, '..', '..'), 'tmp')),
    },
    logging: {
        level: env('LOG_LEVEL', 'info') as 'debug' | 'info' | 'warn' | 'error',
        logToConsole: envBool('LOG_TO_CONSOLE', true),
        logToFile: envBool('LOG_TO_FILE', false),
        logFilePath: env('LOG_FILE_PATH', path.join(path.resolve(__dirname, '..', '..'), 'logs', 'app.log')),
        pm2LogPath: env('PM2_LOG_PATH', path.join(path.resolve(__dirname, '..', '..'), 'logs', 'pm2.log')),
        appErrorLogPath: env('APP_ERROR_LOG_PATH', path.join(path.resolve(__dirname, '..', '..'), 'logs', 'app-error.log')),
        appOutLogPath: env('APP_OUT_LOG_PATH', path.join(path.resolve(__dirname, '..', '..'), 'logs', 'app-out.log')),
        ipLogPath: env('IP_LOG_PATH', path.join(path.resolve(__dirname, '..', '..'), 'logs', 'ip-access.log')),
        maxLogLinesAdmin: envInt('MAX_LOG_LINES_ADMIN', 200),
        serviceLogsDir: env('SERVICE_LOGS_DIR', path.join(path.resolve(__dirname, '..', '..'), 'logs', 'services')),
        serviceLogPaths: {
            'stravaApiQueueProcessor': env('STRAVA_API_QUEUE_PROCESSOR_LOG_PATH',
                path.join(path.resolve(__dirname, '..', '..'), 'logs', 'services', 'strava-api-queue-processor.log')),
            // Hier können weitere dienstspezifische Log-Pfade hinzugefügt werden
        },
    },
    activities: { // Ihre detaillierte activities config
        ignoreTypes: (env('IGNORE_ACTIVITY_TYPES', 'VirtualRide,Workout') || '').split(',').map(type => type.trim().toLowerCase()).filter(type => type.length > 0),
        adminBrowserPageLimit: envInt('ADMIN_ACTIVITIES_PAGE_LIMIT', 25),
        photoMigrationBatchSize: envInt('PHOTO_MIGRATION_BATCH_SIZE', 20),
        tokenValidBufferSeconds: envInt('TOKEN_VALID_BUFFER_SECONDS', 300),
        photoConsistencyBatchSize: envInt('PHOTO_CONSISTENCY_BATCH_SIZE', 50),
        processing: {
            pauseSpeedThresholdMs: envFloat('PROC_PAUSE_SPEED_THRESHOLD_MS', 0.5),
            minPauseDurationSec: envInt('PROC_MIN_PAUSE_DURATION_SEC', 30),
            displayTrackSmoothingWindow: envInt('PROC_DISPLAY_SMOOTH_WINDOW', 5),
            gradientThresholdFlatPositive: envFloat('PROC_GRAD_FLAT_POS', 1.0),
            gradientThresholdFlatNegative: envFloat('PROC_GRAD_FLAT_NEG', -1.0),
            minSectionLengthMeters: envInt('PROC_MIN_SECTION_LENGTH_M', 20),
            mergeShortFlatBetweenDownhillsMeters: envInt('PROC_MERGE_FLAT_DOWNHILL_M', 25),
            finalFilterMinLengthMeters: envInt('PROC_FINAL_FILTER_MIN_LENGTH_M', 100),
            finalFilterMinAvgGradient: envFloat('PROC_FINAL_FILTER_MIN_GRADIENT', -20.0),
            extendSegmentStartMeters: envInt('PROC_EXTEND_SEGMENT_START_M', 200),
            extendSegmentEndMeters: envInt('PROC_EXTEND_SEGMENT_END_M', 200),
            fussTypes: env('ACTIVITY_TYPES_FUSS', 'Run,Walk,Hike,TrailRun,Snowshoe').split(','),
            radTypes: env('ACTIVITY_TYPES_RAD', 'Ride,MountainBikeRide,GravelRide,EBikeRide,VirtualRide').split(','),
        },
        speedThresholds: {
            default: { max_speed_mps: envFloat('DQ_SPEED_DEFAULT_MAX_MPS', 50), average_speed_mps: envFloat('DQ_SPEED_DEFAULT_AVG_MPS', 30) },
            Ride: { max_speed_mps: envFloat('DQ_SPEED_RIDE_MAX_MPS', 45), average_speed_mps: envFloat('DQ_SPEED_RIDE_AVG_MPS', 20) },
            EBikeRide: { max_speed_mps: envFloat('DQ_SPEED_EBIKE_MAX_MPS', 40), average_speed_mps: envFloat('DQ_SPEED_EBIKE_AVG_MPS', 18) },
            Run: { max_speed_mps: envFloat('DQ_SPEED_RUN_MAX_MPS', 12), average_speed_mps: envFloat('DQ_SPEED_RUN_AVG_MPS', 7) },
            Walk: { max_speed_mps: envFloat('DQ_SPEED_WALK_MAX_MPS', 8), average_speed_mps: envFloat('DQ_SPEED_WALK_AVG_MPS', 4) },
            Hike: { max_speed_mps: envFloat('DQ_SPEED_HIKE_MAX_MPS', 8), average_speed_mps: envFloat('DQ_SPEED_HIKE_AVG_MPS', 4) },
            AlpineSki: { max_speed_mps: envFloat('DQ_SPEED_SKIALPINE_MAX_MPS', 55), average_speed_mps: envFloat('DQ_SPEED_SKIALPINE_AVG_MPS', 25) }
        },
    },
    stravaApiLimits: {
        readShortTermLimit: envInt('STRAVA_API_SHORT_TERM_LIMIT', 200), // Ihre höheren Limits
        readShortTermWindowSeconds: envInt('STRAVA_API_SHORT_TERM_WINDOW_SECONDS', 15 * 60),
        readDailyLimit: envInt('STRAVA_API_DAILY_LIMIT', 2000), // Ihre höheren Limits
        readDailyWindowSeconds: envInt('STRAVA_API_DAILY_WINDOW_SECONDS', 24 * 60 * 60),
    },
    maps: { // Ihre detaillierte maps config
        mapTilerApiKey: env('MAPTILER_API_KEY', ''),
        thunderforestApiKey: env('THUNDERFOREST_API_KEY', ''),
        baseLayers: [
            { name: "Streets", url: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', options: { maxZoom: 24, maxNativeZoom: 19, attribution: '&copy; OpenStreetMap' }, active: true },
            { name: "Terrain", url: `https://{s}.tile.thunderforest.com/landscape/{z}/{x}/{y}.png?apikey=${env('THUNDERFOREST_API_KEY')}`, options: { maxZoom: 24, maxNativeZoom: 22, attribution: '&copy; Thunderforest' } },
            { name: "Satellite", url: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', options: { maxZoom: 24, maxNativeZoom: 18, attribution: '&copy; Esri' } },
            { name: "Topo", url: 'https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png', options: { maxZoom: 24, maxNativeZoom: 17, attribution: '&copy; OpenTopoMap' } },
            { name: "Cycle", url: `https://{s}.tile.thunderforest.com/cycle/{z}/{x}/{y}.png?apikey=${env('THUNDERFOREST_API_KEY')}`, options: { maxZoom: 24, maxNativeZoom: 22, attribution: '&copy; Thunderforest' } }
        ],
        defaultMapOptions: {
            zoom: envInt('MAP_DEFAULT_ZOOM', 12),
            center: [envFloat('MAP_DEFAULT_CENTER_LAT', 48.1114), envFloat('MAP_DEFAULT_CENTER_LON', 8.5058)],
            attributionControl: false,
            tapTolerance: 40
        },
        styleColors: {
            activityTypes: {
                Hike: "#ff0000", Walk: "#0000ff", Ride: "#ffa500", Run: "#ff00ff",
                EBikeRide: "#E69100", MountainBikeRide: "#cc8400", VirtualRide: "#6a0dad", Swim: "#00ffff",
                AlpineSki: "#00ced1", Snowshoe: "#fff8dc", Kayaking: "#4682b4", Rowing: "#4682b4",
                StandUpPaddling: "#4682b4", Windsurf: "#1e90ff", Workout: "#32cd32", WeightTraining: "#228b22",
                IceSkate: "#add8e6", InlineSkate: "#ff69b4", Default: "#808080"
            },
            plannedRoute: '#3388ff',
            piTrack: { casing: env('PI_TRACK_CASING_COLOR', '#000000'), mainLine: env('PI_TRACK_MAIN_LINE_COLOR', '#FF0000') },
            debugTracks: { originalRaw: '#ff0000', processedDisplay: '#0000ff', downhillSegment: '#008000' },
            elevationAnnotations: { enterDownhillLine: 'rgba(255, 20, 147, 0.8)', exitDownhillLine: 'rgba(30, 144, 255, 0.8)', downhillSegmentBoxBg: 'rgba(0, 128, 0, 0.15)', downhillSegmentBoxBorder: 'rgba(0, 100, 0, 0.3)' },
            anchorMarkers: { start: 'lime', enterDownhill: '#FF1493', exitDownhill: '#1E90FF', distance: 'blue', angle: 'orange', distanceAndAngle: 'purple', end: 'red', unknown: 'grey' }
        },
        geojsonStyleDefault: { weight: envInt('GEOJSON_DEFAULT_WEIGHT', 3), opacity: envFloat('GEOJSON_DEFAULT_OPACITY', 0.7) },
        plannedRouteStyle: { weight: envInt('PLANNED_ROUTE_WEIGHT', 4), opacity: envFloat('PLANNED_ROUTE_OPACITY', 0.8), dashArray: env('PLANNED_ROUTE_DASHARRAY', '8, 8') },
        photoBaseUrl: env('MAPS_PHOTO_BASE_URL', '/uploads/activity_photos'),
        piControlView: {
            desiredTerrainExaggeration: envFloat('MAP_TERRAIN_EXAGGERATION', 1.5),
            infoUpdateIntervalMs: envInt('PI_INFO_UPDATE_INTERVAL_MS', 250),
            defaultSpeedFactor: envFloat('PI_DEFAULT_SPEED_FACTOR', 300),
            maxSpeedFactor: envFloat('PI_MAX_SPEED_FACTOR', 1000),
            minSpeedFactor: envFloat('PI_MIN_SPEED_FACTOR', 50),
            trackCasingColor: env('PI_TRACK_CASING_COLOR', '#000000'),
            trackCasingWidth: envInt('PI_TRACK_CASING_WIDTH', 7),
            trackMainLineColor: env('PI_TRACK_MAIN_LINE_COLOR', '#FF0000'),
            trackMainLineWidth: envInt('PI_TRACK_MAIN_LINE_WIDTH', 3),
            trackCasingOpacity: envFloat('PI_TRACK_CASING_OPACITY', 0.8),
            lookAheadDistanceMeters: envFloat('PI_LOOK_AHEAD_METERS', 75),
            targetSpeedMetersPerSecond: envFloat('PI_TARGET_SPEED_MPS', 50),
            pauseProximityThresholdMeters: envFloat('PI_PAUSE_PROXIMITY_METERS', 10),
            autoPauseDurationMs: envInt('PI_AUTO_PAUSE_DURATION_MS', 3000),
            pauseConsolidationDistanceMeters: envFloat('PI_PAUSE_CONSOLIDATION_METERS', 25),
            piControllerPosKp: envFloat('PI_POS_KP', 5),
            piControllerPosKi: envFloat('PI_POS_KI', 0.05),
            piControllerPosIntegralLimit: envFloat('PI_POS_INTEGRAL_LIMIT', 0.01),
            piControllerBearingKp: envFloat('PI_BEARING_KP', 0.5),
            piControllerBearingKi: envFloat('PI_BEARING_KI', 0.0),
            piControllerBearingIntegralLimit: envFloat('PI_BEARING_INTEGRAL_LIMIT', 90),
            speedSliderMin: envInt('SPEED_SLIDER_MIN', 50),
            speedSliderMax: envInt('SPEED_SLIDER_MAX', 1000),
            speedSliderMinSpeedMps: envFloat('SPEED_SLIDER_MIN_SPEED_MPS', 5),
            speedSliderMaxSpeedMps: envFloat('SPEED_SLIDER_MAX_SPEED_MPS', 100)
        }
    },
    images: { // Ihre detaillierte images config
        processPhotos: envBool('PROCESS_PHOTOS', true), // Von mir
        photoDownloadSize: env('PHOTO_DOWNLOAD_SIZE', 'd'), // Von mir (für Google)
        photoDisplaySizes: { // Von mir (vereinfacht)
            small: envInt('PHOTO_DISPLAY_SIZE_SMALL_PX', 300),
            medium: envInt('PHOTO_DISPLAY_SIZE_MEDIUM_PX', 800)
        },
        photoQuality: envInt('PHOTO_QUALITY_JPEG', 80), // Von mir
        // Ihre spezifischen Image-Größen-Details:
        sizes: {
            small: { width: envInt('IMAGE_SIZE_SMALL_WIDTH', 320), quality: envInt('IMAGE_SIZE_SMALL_QUALITY', 80), suffix: env('IMAGE_SIZE_SMALL_SUFFIX', '_s') },
            medium: { width: envInt('IMAGE_SIZE_MEDIUM_WIDTH', 1024), quality: envInt('IMAGE_SIZE_MEDIUM_QUALITY', 85), suffix: env('IMAGE_SIZE_MEDIUM_SUFFIX', '_m') },
            original: { quality: envInt('IMAGE_SIZE_ORIGINAL_QUALITY', 90), suffix: env('IMAGE_SIZE_ORIGINAL_SUFFIX', '_o') } // Original hat typ. keine Breite
        },
        defaultFormat: env('IMAGE_DEFAULT_FORMAT', 'jpeg'),
        maxUploadFileSizeMb: envInt('IMAGE_MAX_UPLOAD_MB', 50)
    },
    gpxAuthorName: env('GPX_AUTHOR_NAME', appNameFromEnv),
    komoot: {
        enabled: envBool('KOMOOT_ENABLED', true),
        komootGpxPath: env('KOMOOT_GPX_PATH', '/home/<USER>/.local/bin/komootgpx'),
        gpxUploadDir: env('KOMOOT_GPX_UPLOAD_DIR', path.join(path.resolve(__dirname, '..', '..'), 'GPX_files')),
    },
    garmin: {
        enabled: envBool('GARMIN_ENABLED', true),
        garminBridgePath: env('GARMIN_BRIDGE_PATH', '/home/<USER>/python_envs/garmin/garmin_wrapper.sh'),
        gpxUploadDir: env('GARMIN_GPX_UPLOAD_DIR', path.join(path.resolve(__dirname, '..', '..'), 'GPX_files')),
    },
};

export default appConfig;
