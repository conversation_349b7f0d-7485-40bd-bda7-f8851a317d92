// src/routes/userRoutes.ts
import express from 'express';
import * as userController from '../controllers/userController';
import * as userConnectionController from '../controllers/userConnectionController';
import * as notificationController from '../controllers/notificationController'; // NEU
import * as sportGroupController from '../controllers/sportGroupController'; // NEU: Sportart-Obergruppen
import * as mapStyleController from '../controllers/mapStyleController'; // NEU: Karten-Layer-Stile
import * as userGpxController from '../controllers/userGpxController'; // NEU: GPX-Upload für Benutzer
import * as urlGpxImportController from '../controllers/urlGpxImportController';
import * as tripController from '../controllers/tripController'; // NEU: GPX-Import von URLs
import * as poiImportController from '../controllers/poiImportController'; // NEU: POI-Import von URLs
import * as googleDriveController from '../controllers/googleDriveController'; // Google Drive Integration
import * as googlePhotoPickerController from '../controllers/googlePhotoPickerController'; // Google Photos Picker API
import komootRoutes from './komootRoutes'; // Komoot-Routen
import garminRoutes from './garminRoutes'; // Garmin-Routen

// Die Sync-Logik könnte auch in einem eigenen syncController sein,
// aber für den Moment nehmen wir an, sie ist im userController oder wird von dort gerufen.
// import * as syncController from '../controllers/syncController'; // Falls ausgelagert
import { requireUserLogin } from '../middleware/requireUserLogin';

const router = express.Router();

// Alle Routen in dieser Datei erfordern einen eingeloggten Benutzer.
// Die Middleware wird einmal am Anfang für alle nachfolgenden Routen in dieser Datei angewendet.
router.use(requireUserLogin);

// === Dashboard und allgemeine User-Seiten ===
router.get('/dashboard', userController.showUserDashboard);
router.get('/settings', userController.showUserSettingsPage);
router.post('/settings', userController.handleUserSettingsUpdate);

// === User-spezifische Karte ===
// Stellt die HTML-Seite für die Karte des Benutzers bereit
router.get('/:username/map', userController.showUserMap); // Annahme: showUserMap existiert in userController

// Freundesprofil anzeigen
router.get('/profile/:username', userController.showFriendProfile);

// === User-spezifische Statistiken ===
// Stellt die HTML-Seite für die Statistiken des Benutzers bereit
router.get('/:username/stats', userController.showUserStatsPage);

// === API Endpunkte für User-spezifische Statistiken ===
// Diese Routen werden vom clientseitigen JavaScript der User-Statistikseite aufgerufen.
// Das :username in der Route dient zur Identifizierung, aber die Berechtigungsprüfung
// im Controller stellt sicher, dass der eingeloggte User nur auf seine eigenen Daten
// oder als Admin auf die Daten des :username zugreifen kann.

// API: Monatsstatistiken für einen Benutzer
router.get('/api/:username/stats/monthly', userController.handleUserMonthlyStatsApi);

// API: Jährliche Zusammenfassung für einen Benutzer
router.get('/api/:username/stats/yearly-summary', userController.handleUserYearlySummaryApi);

// API: Tägliche Fortschrittsdaten für Jahresvergleich eines Benutzers
router.get('/api/:username/stats/daily-progression', userController.handleUserDailyProgressionApi);


// === Synchronisations-Routen ===
// Stößt die Synchronisation der neuesten Strava-Aktivitäten für den eingeloggten Benutzer an
// Die Logik dafür befindet sich in `userController.handleStravaSyncLatest` (oder `syncController`)
router.post('/sync/strava/latest', userController.handleStravaSyncLatest);


// === Ausrüstungsverwaltung ===
// Seite anzeigen (Liste und Hinzufügen-Formular)
router.get('/equipment', userController.showEquipmentPage);
// Neuen Ausrüstungsgegenstand hinzufügen
router.post('/equipment/add', userController.handleAddEquipment);
// Seite zum Bearbeiten eines Ausrüstungsgegenstandes anzeigen
router.get('/equipment/edit/:id(\\d+)', userController.showEditEquipmentPage);
// Ausrüstungsgegenstand aktualisieren
router.post('/equipment/update/:id(\\d+)', userController.handleUpdateEquipment);
// Ausrüstungsgegenstand löschen
router.post('/equipment/delete/:id(\\d+)', userController.handleDeleteEquipment);
// SportType mit Equipment verknüpfen
router.post('/equipment/link-sport-type', userController.handleLinkSportTypeToEquipment);
// Verknüpfung zwischen SportType und Equipment entfernen
router.post('/equipment/unlink-sport-type', userController.handleUnlinkSportTypeFromEquipment);
// Equipment zu allen Aktivitäten basierend auf SportType hinzufügen
router.post('/equipment/add-to-all-activities', userController.handleAddEquipmentToAllActivities);

// User-spezifische Aktivitäts-Detailansicht
router.get('/activity/:activityDbId(\\d+)', userController.showUserActivityDetailPage);

// Route zum Speichern der Ausrüstungszuordnung (POST)
router.post('/activity/:activityDbId(\\d+)/equipment', userController.handleLinkEquipmentToActivity);

// Seite zum Bearbeiten einer Aktivität (inkl. Ausrüstung und Google Photos Link)
router.get('/activity/:activityDbId(\\d+)/edit', userController.showEditActivityPage);

// Route zum Aktualisieren des Aktivitätstitels
router.post('/activity/:activityDbId(\\d+)/update-title', userController.handleUpdateActivityTitle);

// Seite "Meine Aktivitäten"
router.get('/activities', userController.showMyActivitiesPage);
// Aktivität löschen (nur eigene)
router.post('/activities/delete/:activityDbId(\\d+)', userController.handleDeleteMyActivity);

// Seite "Meine geplanten Aktivitäten"
router.get('/activities/planned', userController.showMyPlannedActivitiesPage);
// Detailansicht einer geplanten Aktivität
router.get('/activity/planned/:plannedRouteId(\\d+)', userController.showPlannedRouteDetailPage);
// Bearbeitungsseite für geplante Aktivität
router.get('/activity/planned/:plannedRouteId(\\d+)/edit', userController.showEditPlannedRoutePage);
// Update einer geplanten Aktivität
router.post('/activity/planned/:plannedRouteId(\\d+)/edit', userController.handleUpdatePlannedRoute);
// Löschen von Bildern und URLs
router.post('/activity/planned/:plannedRouteId(\\d+)/delete-image/:imageId(\\d+)', userController.deletePlannedRouteImage);
router.post('/activity/planned/:plannedRouteId(\\d+)/delete-url/:urlId(\\d+)', userController.deletePlannedRouteUrl);
// Weiterleitung von der alten URL zur neuen URL (für Kompatibilität)
router.get('/planned-route/:plannedRouteId(\\d+)', (req, res) => {
    res.redirect(`/user/activity/planned/${req.params.plannedRouteId}`);
});

// === POI-Routen ===
// Seite "Meine POIs"
router.get('/pois', userController.showMyPOIsPage);
// Neuen POI hinzufügen
router.get('/poi/add', userController.showAddPOIPage);
router.post('/poi/add', userController.handleAddPOI);
// Detailansicht eines POI
router.get('/poi/:poiId(\\d+)', userController.showPOIDetailPage);
// Bearbeitungsseite für POI
router.get('/poi/:poiId(\\d+)/edit', userController.showEditPOIPage);
// Update eines POI
router.post('/poi/:poiId(\\d+)/edit', userController.handleUpdatePOI);
// Löschen von POI-Bildern und URLs
router.post('/poi/:poiId(\\d+)/delete-image/:imageId(\\d+)', userController.deletePOIImage);
router.post('/poi/:poiId(\\d+)/delete-url/:urlId(\\d+)', userController.deletePOIUrl);
// POI löschen
router.post('/poi/:poiId(\\d+)/delete', userController.handleDeletePOI);

// === POI-Import-Routen ===
// POI-Import von URLs (ähnlich wie URL-GPX-Import)
router.get('/poi-import', poiImportController.showPoiImportPage);
router.post('/poi-import/analyze', poiImportController.analyzePoiUrl);
router.post('/poi-import/import', poiImportController.importPoiFromUrl);
// Direkte POI-Erstellung (für Kontextmenü)
router.post('/poi-import/create-direct', poiImportController.createPoiDirect);
// Geocaching-GPX-Import
router.post('/poi-import/geocaching-gpx', poiImportController.importGeocachingGpx);
// Geplante Aktivität löschen
router.post('/activities/planned/delete/:plannedRouteId(\\d+)', userController.handleDeletePlannedRoute);


router.get('/friends', userConnectionController.showFriendsPage);
router.post('/friends/request', userConnectionController.sendFriendRequest); // Sendet eine Anfrage
router.post('/friends/accept/:connectionId(\\d+)', userConnectionController.acceptFriendRequestController); // Nimmt eine Anfrage an
router.post('/friends/action/:connectionId(\\d+)', userConnectionController.declineOrBlockFriendRequestController); // Lehnt ab, blockiert oder entfreundet

// === Benachrichtigungs-API für eingeloggte User ===
router.get('/api/notifications', notificationController.getMyNotifications); // Holt Benachrichtigungen
router.post('/api/notifications/:notificationId(\\d+)/read', notificationController.markAsRead); // Einzelne als gelesen
router.post('/api/notifications/read-all', notificationController.markAllAsRead); // Alle als gelesen
router.delete('/api/notifications/:notificationId(\\d+)', notificationController.deleteNotificationController); // Einzelne löschen

router.get('/notifications', notificationController.showNotificationsPage); // Seite für alle Benachrichtigungen

// NEU: API-Endpunkt zum Suchen von Google Photos für eine Aktivität
router.get('/api/activity/:activityDbId(\\d+)/google-photos', userController.findGooglePhotosForActivityApi);

// NEU: API-Endpunkt zum Verknüpfen ausgewählter Google Photos mit einer Aktivität
router.post('/api/activity/:activityDbId(\\d+)/link-google-photos', userController.linkGooglePhotosToActivityApi);

// POST oder DELETE, je nachdem, was Sie bevorzugen
router.post('/api/activity-photo/:photoDbId(\\d+)/unlink', userController.handleUnlinkPhotoFromActivityApi);

// === Google Photos Picker API (korrekte Implementierung) ===
// Konfiguration für Google Photos Picker API abrufen
router.get('/api/google-photos-picker/config', googlePhotoPickerController.getPickerConfig);
// Neue Picker Session erstellen
router.post('/api/google-photos-picker/session', googlePhotoPickerController.createPickerSession);
// Fotos aus Picker Session abrufen
router.get('/api/google-photos-picker/session/:sessionId/photos', googlePhotoPickerController.getPickerPhotos);
// Status der Google Photos Integration abrufen
router.get('/api/google-photos-picker/status', googlePhotoPickerController.getIntegrationStatus);

// Füge den neuen Handler am Ende der Datei oder an passender Stelle hinzu:
router.post('/activity/:activityDbId(\\d+)/resync', requireUserLogin, userController.handleManualResyncActivity);

// API-Endpunkt zum Abrufen der Freundesliste für das Teilen
router.get('/api/friends', requireUserLogin, userController.getFriendsListApi);

// Endpunkt zum Teilen einer Aktivität mit Freunden
router.post('/activity/:activityDbId(\\d+)/share', requireUserLogin, userController.shareActivityWithFriends);

// Endpunkt zum Teilen einer geplanten Route mit Freunden
router.post('/activity/planned/:plannedRouteId(\\d+)/share', requireUserLogin, userController.sharePlannedRouteWithFriends);

// Bulk-Aktionen für geplante Routen
router.post('/activities/planned/bulk-delete', requireUserLogin, userController.bulkDeletePlannedRoutes);
router.post('/activities/planned/bulk-share', requireUserLogin, userController.bulkSharePlannedRoutes);

// Endpunkt zum Teilen eines POI mit Freunden
router.post('/poi/:poiId(\\d+)/share', requireUserLogin, userController.sharePOIWithFriends);

// Platzhalter für spätere Synchronisationsrouten:
// router.post('/sync/strava/all', userController.handleStravaSyncAll);
// router.post('/sync/google/photos', userController.handleGooglePhotosSync);

// === Sportart-Obergruppen ===
// Seite anzeigen (Liste und Hinzufügen-Formular)
router.get('/sport-groups', sportGroupController.showSportGroupsPage);
// Neue Sportart-Obergruppe hinzufügen
router.post('/sport-groups/add', sportGroupController.handleCreateSportGroup);
// Seite zum Bearbeiten einer Sportart-Obergruppe anzeigen
router.get('/sport-groups/edit/:id(\\d+)', sportGroupController.showEditSportGroupPage);
// Sportart-Obergruppe aktualisieren
router.post('/sport-groups/update/:id(\\d+)', sportGroupController.handleUpdateSportGroup);
// Sportart-Obergruppe löschen
router.post('/sport-groups/delete/:id(\\d+)', sportGroupController.handleDeleteSportGroup);
// API: Alle Sportart-Obergruppen eines Benutzers abrufen
router.get('/api/sport-groups', sportGroupController.getSportGroupsApi);
// API: Alle Sportarten für eine Gruppe abrufen
router.get('/api/sport-groups/:id(\\d+)/sport-types', sportGroupController.getSportTypesForGroupApi);

// === Karten-Layer-Stile ===
// Seite anzeigen (Liste und Bearbeiten-Formular)
router.get('/map-styles', mapStyleController.showMapStylesPage);
// Stil speichern
router.post('/map-styles/save', mapStyleController.handleSaveMapStyle);
// Stil löschen
router.post('/map-styles/delete/:sportType', mapStyleController.handleDeleteMapStyle);
// API: Alle Stile eines Benutzers abrufen

// === Komoot-Integration ===
// Komoot-Routen einbinden
router.use('/komoot', komootRoutes);

// === Garmin-Integration ===
// Garmin-Routen einbinden
router.use('/garmin', garminRoutes);
router.get('/api/map-styles', mapStyleController.getUserMapStyles);

// === GPX-Verwaltung für Benutzer ===
// Seite zur Verwaltung von GPX-Dateien anzeigen
router.get('/gpx-management', userGpxController.renderGpxManagementPage);
// GPX-Datei hochladen und verarbeiten
router.post('/gpx-upload', userGpxController.handleGpxUpload);
// Alte Route für Kompatibilität beibehalten
router.get('/gpx-upload', (_req, res) => res.redirect('/user/gpx-management'));

// === Google Drive Integration ===
// Google Drive Dateien anzeigen und importieren
router.get('/google-drive/files', googleDriveController.showGoogleDriveFiles);
router.get('/google-drive/preview/:fileId', googleDriveController.showGoogleDriveFilePreview);
router.post('/google-drive/import/:fileId', googleDriveController.importGoogleDriveFile);

// === URL GPX Import ===
// Seite zum Importieren von GPX-Dateien von URLs anzeigen
router.get('/url-gpx-import', urlGpxImportController.showUrlGpxImportPage);
// URL analysieren und GPX-Links extrahieren
router.post('/url-gpx-import/analyze', urlGpxImportController.analyzeUrl);
// Ausgewählte GPX-Dateien importieren
router.post('/url-gpx-import/import', urlGpxImportController.importFromUrls);

// === Reisen (Trips) ===
// Übersicht aller Reisen
router.get('/trip', requireUserLogin, tripController.showTripsOverview);
// Neue Reise erstellen (Formular anzeigen)
router.get('/trip/new', requireUserLogin, tripController.showCreateTripForm);
// Neue Reise erstellen (POST)
router.post('/trip', requireUserLogin, tripController.createTrip);
// Reise-Details anzeigen
router.get('/trip/:id(\\d+)', requireUserLogin, tripController.showTripDetail);
// Reise bearbeiten (Formular anzeigen)
router.get('/trip/:id(\\d+)/edit', requireUserLogin, tripController.showEditTripForm);
// Reise bearbeiten (POST)
router.post('/trip/:id(\\d+)/edit', requireUserLogin, tripController.updateTrip);
// Reise löschen
router.post('/trip/:id(\\d+)/delete', requireUserLogin, tripController.deleteTrip);
// Aktivität zu Reise hinzufügen
router.post('/trip/:id(\\d+)/add-activity', requireUserLogin, tripController.addActivityToTrip);
// Aktivität von Reise entfernen
router.post('/trip/:id(\\d+)/remove-activity', requireUserLogin, tripController.removeActivityFromTrip);
// Geplante Route zu Reise hinzufügen
router.post('/trip/:id(\\d+)/add-planned-route', requireUserLogin, tripController.addPlannedRouteToTrip);
// Geplante Route von Reise entfernen
router.post('/trip/:id(\\d+)/remove-planned-route', requireUserLogin, tripController.removePlannedRouteFromTrip);
// POI zu Reise hinzufügen
router.post('/trip/:id(\\d+)/add-poi', requireUserLogin, tripController.addPoiToTrip);
// POI von Reise entfernen
router.post('/trip/:id(\\d+)/remove-poi', requireUserLogin, tripController.removePoiFromTrip);

// Aktivitäten-Vorschläge für Reise anzeigen
router.get('/trip/:id(\\d+)/suggest-activities', requireUserLogin, tripController.showActivitySuggestions);

// === Trip API-Endpunkte ===
// API: Alle Reisen eines Benutzers abrufen
router.get('/api/trips', requireUserLogin, tripController.getTripsApi);
// API: Weitere Bilder einer Reise laden
router.get('/api/trip/:tripId(\\d+)/images', requireUserLogin, tripController.getTripImagesApi);

export default router;
