// src/routes/authRoutes.ts
import express, { Router } from 'express';
import * as authController from '../controllers/authController';
import { disconnectGoogleDrive } from '../controllers/googleDriveAuthController'; // Nur disconnect benötigt
import * as googlePhotosPickerAuthController from '../controllers/googlePhotosPickerAuthController';
import { requireUserLogin } from '../middleware/requireUserLogin';
import logger from '../utils/logger';

const router: Router = express.Router();
const log = logger.getLogger(__filename);

log.info('Authentication routes initializing...');

// Login-Seite anzeigen
router.get('/login', authController.showLoginPage);

// Login-Anfrage verarbeiten (verwendet Passport 'local' Strategie)
router.post('/login', authController.handleLogin);

// Logout-Anfrage verarbeiten
router.get('/logout', authController.logout); // Korrekter Funktionsname

// Strava Authentifizierung
router.get('/strava/login', requireUserLogin, authController.requestStravaAuthorization);
router.get('/strava/callback', requireUserLogin, authController.handleStravaCallback); // requireUserLogin hier, da der User Tokens für seinen Account bekommt

// Google Photos Authentifizierung
router.get('/google/login', requireUserLogin, authController.requestGooglePhotosAuthorization);
router.get('/google/callback', requireUserLogin, authController.handleGooglePhotosCallback); // requireUserLogin hier
router.get('/google/disconnect', requireUserLogin, authController.disconnectGooglePhotos);

// Google Drive Authentifizierung
router.get('/google-drive/login', requireUserLogin, authController.requestGoogleDriveAuthorization);
router.get('/google-drive/disconnect', requireUserLogin, disconnectGoogleDrive);

// === Google Photos Picker OAuth ===
router.get('/google-photos-picker', requireUserLogin, googlePhotosPickerAuthController.startGooglePhotosPickerAuth);
router.get('/google-photos-picker/callback', googlePhotosPickerAuthController.handleGooglePhotosPickerCallback);
router.post('/google-photos-picker/disconnect', requireUserLogin, googlePhotosPickerAuthController.disconnectGooglePhotosPicker);
router.get('/google-photos-picker/status', requireUserLogin, googlePhotosPickerAuthController.getGooglePhotosPickerStatus);

// Beispiel für eine Registrierungsroute (falls Sie sie später hinzufügen)
// router.get('/register', authController.showRegisterPage);
// router.post('/register', authController.handleRegister);

export default router;
