"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getIntegrationStatus = exports.getPickerPhotos = exports.createPickerSession = exports.getPickerConfig = void 0;
const config_1 = __importDefault(require("../config/config"));
const logger_1 = __importDefault(require("../utils/logger"));
const log = logger_1.default.getLogger(__filename);
/**
 * Liefert die Konfiguration für die Google Photos Picker API
 */
const getPickerConfig = (req, res) => {
    const fnLogPrefix = `[GooglePickerConfig User:${req.user?.id}]`;
    try {
        if (!config_1.default.googlePhotos.enablePickerApi) {
            log.warn(`${fnLogPrefix} Google Photos Picker API ist deaktiviert`);
            res.status(503).json({
                error: 'Google Photos Picker API ist nicht verfügbar',
                enabled: false
            });
            return;
        }
        if (!config_1.default.googlePhotos.clientId) {
            log.error(`${fnLogPrefix} Google Photos Picker API nicht konfiguriert - Client ID fehlt`);
            res.status(500).json({
                error: 'Google Photos Picker API ist nicht konfiguriert - Client ID fehlt',
                enabled: false
            });
            return;
        }
        log.info(`${fnLogPrefix} Picker API Konfiguration bereitgestellt`);
        res.json({
            enabled: true,
            clientId: config_1.default.googlePhotos.clientId,
            // Picker API verwendet die gleiche Client ID wie OAuth - kein separater API Key nötig
        });
    }
    catch (error) {
        log.error(`${fnLogPrefix} Fehler beim Bereitstellen der Picker-Konfiguration:`, error);
        res.status(500).json({
            error: 'Interner Serverfehler',
            enabled: false
        });
    }
};
exports.getPickerConfig = getPickerConfig;
/**
 * Erstellt eine neue Google Photos Picker Session (korrekte API)
 */
const createPickerSession = async (req, res) => {
    const fnLogPrefix = `[GooglePickerSession User:${req.user?.id}]`;
    try {
        if (!req.user) {
            res.status(401).json({ error: 'Nicht authentifiziert' });
            return;
        }
        if (!config_1.default.googlePhotos.enablePickerApi) {
            res.status(503).json({
                error: 'Google Photos Picker API ist deaktiviert',
                enabled: false
            });
            return;
        }
        // Hole den Access Token aus der Datenbank (Google Photos Picker Integration)
        const userRepository = await Promise.resolve().then(() => __importStar(require('../db/userRepository')));
        const userTokens = await userRepository.getUserGooglePhotosPickerTokens(req.user.id);
        if (!userTokens || !userTokens.accessToken) {
            res.status(401).json({
                error: 'Keine Google Photos Picker Verbindung gefunden. Bitte verbinde Google Photos Picker in den Einstellungen.',
                needsReauth: true
            });
            return;
        }
        const accessToken = userTokens.accessToken;
        log.info(`${fnLogPrefix} Erstelle neue Picker Session`);
        // Erstelle Session mit der korrekten Google Photos Picker API
        const response = await fetch(`${config_1.default.googlePhotos.pickerApiUrl}/sessions`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${accessToken}`
            }
        });
        if (!response.ok) {
            const errorText = await response.text();
            log.error(`${fnLogPrefix} Fehler beim Erstellen der Picker Session: ${response.status} ${errorText}`);
            if (response.status === 401 || response.status === 403) {
                res.status(401).json({
                    error: 'Google Photos Berechtigung fehlt oder abgelaufen. Bitte erneut verbinden.',
                    needsReauth: true
                });
            }
            else {
                res.status(500).json({
                    error: `Google Photos API Fehler: ${response.status}`,
                    details: errorText
                });
            }
            return;
        }
        const sessionData = await response.json();
        log.info(`${fnLogPrefix} Picker Session erstellt: ${sessionData.id}`);
        res.json({
            success: true,
            session: sessionData,
            pickerUrl: sessionData.pickerUri
        });
    }
    catch (error) {
        log.error(`${fnLogPrefix} Fehler beim Erstellen der Picker Session:`, error);
        res.status(500).json({
            error: 'Fehler beim Erstellen der Picker Session',
            details: error.message
        });
    }
};
exports.createPickerSession = createPickerSession;
/**
 * Holt die ausgewählten Fotos aus einer Picker Session
 */
const getPickerPhotos = async (req, res) => {
    const fnLogPrefix = `[GooglePickerPhotos User:${req.user?.id}]`;
    const { sessionId } = req.params;
    try {
        if (!req.user) {
            res.status(401).json({ error: 'Nicht authentifiziert' });
            return;
        }
        // Hole den Access Token aus der Datenbank (Google Photos Picker Integration)
        const userRepository = await Promise.resolve().then(() => __importStar(require('../db/userRepository')));
        const userTokens = await userRepository.getUserGooglePhotosPickerTokens(req.user.id);
        if (!userTokens || !userTokens.accessToken) {
            res.status(401).json({
                error: 'Keine Google Photos Picker Verbindung gefunden. Bitte verbinde Google Photos Picker in den Einstellungen.',
                needsReauth: true
            });
            return;
        }
        const accessToken = userTokens.accessToken;
        log.info(`${fnLogPrefix} Hole Fotos aus Session ${sessionId}`);
        // Hole Session-Status
        const sessionResponse = await fetch(`${config_1.default.googlePhotos.pickerApiUrl}/sessions/${sessionId}`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${accessToken}`
            }
        });
        if (!sessionResponse.ok) {
            const errorText = await sessionResponse.text();
            log.error(`${fnLogPrefix} Fehler beim Abrufen der Session: ${sessionResponse.status} ${errorText}`);
            res.status(500).json({ error: 'Fehler beim Abrufen der Session' });
            return;
        }
        const sessionData = await sessionResponse.json();
        // Hole Media Items aus der Session
        const mediaResponse = await fetch(`${config_1.default.googlePhotos.pickerApiUrl}/mediaItems?sessionId=${sessionId}&pageSize=50`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${accessToken}`
            }
        });
        if (!mediaResponse.ok) {
            const errorText = await mediaResponse.text();
            log.error(`${fnLogPrefix} Fehler beim Abrufen der Media Items: ${mediaResponse.status} ${errorText}`);
            res.status(500).json({ error: 'Fehler beim Abrufen der Fotos' });
            return;
        }
        const mediaData = await mediaResponse.json();
        log.info(`${fnLogPrefix} ${mediaData.mediaItems?.length || 0} Fotos aus Session abgerufen`);
        res.json({
            success: true,
            session: sessionData,
            photos: mediaData.mediaItems || []
        });
    }
    catch (error) {
        log.error(`${fnLogPrefix} Fehler beim Abrufen der Picker Fotos:`, error);
        res.status(500).json({
            error: 'Fehler beim Abrufen der Fotos',
            details: error.message
        });
    }
};
exports.getPickerPhotos = getPickerPhotos;
/**
 * Liefert den Status der Google Photos Integration
 */
const getIntegrationStatus = (req, res) => {
    const fnLogPrefix = `[GooglePickerStatus User:${req.user?.id}]`;
    try {
        const status = {
            pickerApiEnabled: config_1.default.googlePhotos.enablePickerApi || false,
            pickerApiConfigured: !!(config_1.default.googlePhotos.clientId), // Nur Client ID nötig
            libraryApiDeprecated: true, // Seit April 2025
            recommendedApproach: 'Google Photos Picker API',
            migrationDate: '2025-04-01',
            features: {
                photoSelection: config_1.default.googlePhotos.enablePickerApi || false,
                dateRangeFilter: false, // Picker API unterstützt keine Datumsfilterung
                bulkDownload: false,
                albumAccess: config_1.default.googlePhotos.enablePickerApi || false
            }
        };
        log.info(`${fnLogPrefix} Integration Status abgerufen`);
        res.json(status);
    }
    catch (error) {
        log.error(`${fnLogPrefix} Fehler beim Abrufen des Integration Status:`, error);
        res.status(500).json({ error: 'Interner Serverfehler' });
    }
};
exports.getIntegrationStatus = getIntegrationStatus;
//# sourceMappingURL=googlePhotoPickerController.js.map