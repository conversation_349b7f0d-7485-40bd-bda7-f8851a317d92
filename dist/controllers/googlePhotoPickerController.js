"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getIntegrationStatus = exports.processSelectedPhotos = exports.getPickerConfig = void 0;
const config_1 = __importDefault(require("../config/config"));
const logger_1 = __importDefault(require("../utils/logger"));
const log = logger_1.default.getLogger(__filename);
/**
 * Liefert die Konfiguration für die Google Photos Picker API
 */
const getPickerConfig = (req, res) => {
    const fnLogPrefix = `[GooglePickerConfig User:${req.user?.id}]`;
    try {
        if (!config_1.default.googlePhotos.enablePickerApi) {
            log.warn(`${fnLogPrefix} Google Photos Picker API ist deaktiviert`);
            res.status(503).json({
                error: 'Google Photos Picker API ist nicht verfügbar',
                enabled: false
            });
            return;
        }
        if (!config_1.default.googlePhotos.clientId) {
            log.error(`${fnLogPrefix} Google Photos Picker API nicht konfiguriert - Client ID fehlt`);
            res.status(500).json({
                error: 'Google Photos Picker API ist nicht konfiguriert - Client ID fehlt',
                enabled: false
            });
            return;
        }
        log.info(`${fnLogPrefix} Picker API Konfiguration bereitgestellt`);
        res.json({
            enabled: true,
            clientId: config_1.default.googlePhotos.clientId,
            // Picker API verwendet die gleiche Client ID wie OAuth - kein separater API Key nötig
        });
    }
    catch (error) {
        log.error(`${fnLogPrefix} Fehler beim Bereitstellen der Picker-Konfiguration:`, error);
        res.status(500).json({
            error: 'Interner Serverfehler',
            enabled: false
        });
    }
};
exports.getPickerConfig = getPickerConfig;
/**
 * Verarbeitet ausgewählte Fotos vom Google Photos Picker
 */
const processSelectedPhotos = async (req, res) => {
    const fnLogPrefix = `[GooglePickerProcess User:${req.user?.id}]`;
    const { activityId, photos } = req.body;
    try {
        if (!req.user) {
            res.status(401).json({ error: 'Nicht authentifiziert' });
            return;
        }
        if (!activityId || !photos || !Array.isArray(photos)) {
            res.status(400).json({ error: 'Ungültige Anfrage: activityId und photos sind erforderlich' });
            return;
        }
        log.info(`${fnLogPrefix} Verarbeite ${photos.length} ausgewählte Fotos für Aktivität ${activityId}`);
        // Hier würdest du die Fotos verarbeiten:
        // 1. Validiere, dass die Aktivität dem Benutzer gehört
        // 2. Lade die Fotos herunter oder speichere die URLs
        // 3. Verknüpfe sie mit der Aktivität in der Datenbank
        const processedPhotos = [];
        for (const photo of photos) {
            try {
                // Validiere Foto-Daten
                if (!photo.id || !photo.url) {
                    log.warn(`${fnLogPrefix} Ungültiges Foto übersprungen:`, photo);
                    continue;
                }
                // Hier würdest du das Foto verarbeiten
                const processedPhoto = {
                    id: photo.id,
                    name: photo.name || 'Unbenanntes Foto',
                    originalUrl: photo.url,
                    thumbnailUrl: photo.thumbnailUrl,
                    mimeType: photo.mimeType,
                    sizeBytes: photo.sizeBytes,
                    addedAt: new Date().toISOString()
                };
                processedPhotos.push(processedPhoto);
                log.debug(`${fnLogPrefix} Foto verarbeitet: ${photo.name} (${photo.id})`);
            }
            catch (photoError) {
                log.error(`${fnLogPrefix} Fehler beim Verarbeiten von Foto ${photo.id}:`, photoError);
            }
        }
        log.info(`${fnLogPrefix} ${processedPhotos.length} von ${photos.length} Fotos erfolgreich verarbeitet`);
        res.json({
            success: true,
            processedCount: processedPhotos.length,
            totalCount: photos.length,
            photos: processedPhotos
        });
    }
    catch (error) {
        log.error(`${fnLogPrefix} Fehler beim Verarbeiten der ausgewählten Fotos:`, error);
        res.status(500).json({
            error: 'Fehler beim Verarbeiten der Fotos',
            details: error.message
        });
    }
};
exports.processSelectedPhotos = processSelectedPhotos;
/**
 * Liefert den Status der Google Photos Integration
 */
const getIntegrationStatus = (req, res) => {
    const fnLogPrefix = `[GooglePickerStatus User:${req.user?.id}]`;
    try {
        const status = {
            pickerApiEnabled: config_1.default.googlePhotos.enablePickerApi || false,
            pickerApiConfigured: !!(config_1.default.googlePhotos.clientId), // Nur Client ID nötig
            libraryApiDeprecated: true, // Seit April 2025
            recommendedApproach: 'Google Photos Picker API',
            migrationDate: '2025-04-01',
            features: {
                photoSelection: config_1.default.googlePhotos.enablePickerApi || false,
                dateRangeFilter: false, // Picker API unterstützt keine Datumsfilterung
                bulkDownload: false,
                albumAccess: config_1.default.googlePhotos.enablePickerApi || false
            }
        };
        log.info(`${fnLogPrefix} Integration Status abgerufen`);
        res.json(status);
    }
    catch (error) {
        log.error(`${fnLogPrefix} Fehler beim Abrufen des Integration Status:`, error);
        res.status(500).json({ error: 'Interner Serverfehler' });
    }
};
exports.getIntegrationStatus = getIntegrationStatus;
//# sourceMappingURL=googlePhotoPickerController.js.map