"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.refreshUserGoogleAccessTokenUtility = exports.refreshUserStravaToken = exports.disconnectGooglePhotos = exports.handleGooglePhotosCallback = exports.requestGoogleDriveAuthorization = exports.requestGooglePhotosAuthorization = exports.handleStravaCallback = exports.requestStravaAuthorization = exports.logout = exports.handleLogin = exports.showLoginPage = void 0;
const config_1 = __importDefault(require("../config/config"));
const logger_1 = __importDefault(require("../utils/logger"));
const passport_1 = __importDefault(require("passport")); // Passport für die Login-Route importieren
const googleapis_1 = require("googleapis"); // Auth für OAuth2Client importieren
const userRepository = __importStar(require("../db/userRepository"));
const sportGroupService = __importStar(require("../services/sportGroupService")); // NEU: Sportart-Obergruppen
const log = logger_1.default.getLogger(__filename);
// --- Login-Seiten Handler ---
// Diese Funktion ist dafür zuständig, die Login-Seite anzuzeigen.
// Sie wird von der Route GET /auth/login aufgerufen.
// --- Login-Seiten Handler ---
const showLoginPage = (req, res) => {
    const fnLogPrefix = '[Ctrl ShowLoginPage]';
    // req.user ist hier noch nicht zuverlässig gesetzt, bevor der Login-Prozess durchlaufen ist.
    // req.session.user ist hier relevant, FALLS Sie es nach dem Login manuell setzen würden,
    // was aber durch Passport und req.logIn() + serializeUser/deserializeUser gehandhabt wird.
    // Für "ist bereits eingeloggt" ist req.isAuthenticated() oder req.user die bessere Prüfung.
    if (req.isAuthenticated && req.isAuthenticated()) { // Von Passport
        log.info(`${fnLogPrefix} User ${req.user?.username} ist bereits eingeloggt. Redirect zum Dashboard.`);
        if (req.user?.role === 'admin') {
            return res.redirect('/admin/dashboard');
        }
        return res.redirect('/user/dashboard');
    }
    log.info(`${fnLogPrefix} Rendering login page.`);
    res.render('login', {
        pageTitle: 'Anmelden',
        layout: 'layouts/simple_layout',
        pageSpecificClass: 'login-page-body'
    });
};
exports.showLoginPage = showLoginPage;
// --- Login Verarbeitungs-Handler ---
const handleLogin = (req, res, next) => {
    const fnLogPrefix = '[Ctrl HandleLogin]';
    log.debug(`${fnLogPrefix} Login attempt for user: ${req.body.username}`);
    passport_1.default.authenticate('local', (err, user, info) => {
        if (err) {
            log.error(`${fnLogPrefix} Passport authentication error:`, err);
            return next(err);
        }
        if (!user) {
            log.warn(`${fnLogPrefix} Login failed for ${req.body.username}: ${info ? info.message : 'Unbekannter Fehler'}`);
            req.flash('error', info ? info.message : 'Login fehlgeschlagen');
            return res.redirect('/auth/login');
        }
        req.logIn(user, (errLogin) => {
            if (errLogin) {
                log.error(`${fnLogPrefix} Error during req.logIn for ${user.username}:`, errLogin);
                return next(errLogin);
            }
            req.session.save(async (errSave) => {
                if (errSave) {
                    log.error(`${fnLogPrefix} Error saving session after login for ${user.username}:`, errSave);
                    return next(errSave);
                }
                log.info(`${fnLogPrefix} User ${user.username} successfully logged in and session saved.`);
                // Initialisiere Sportart-Obergruppen für den Benutzer, falls noch nicht vorhanden
                try {
                    await sportGroupService.initializeDefaultSportGroupsForUser(user.id);
                    log.info(`${fnLogPrefix} Sport groups initialized for user ${user.username}`);
                }
                catch (sportGroupError) {
                    log.error(`${fnLogPrefix} Error initializing sport groups for user ${user.username}:`, sportGroupError);
                    // Wir brechen den Login-Prozess nicht ab, wenn die Initialisierung fehlschlägt
                }
                if (user.role === 'admin') {
                    return res.redirect('/admin/dashboard');
                }
                return res.redirect('/user/dashboard');
            });
        });
    })(req, res, next);
};
exports.handleLogin = handleLogin;
// --- Logout Handler ---
// Diese Funktion ist für den Logout zuständig und wird von der Route GET /auth/logout aufgerufen.
const logout = (req, res, next) => {
    const usernameForLog = req.user?.username || req.session?.user?.username || 'Unbekannter Benutzer';
    const fnLogPrefix = `[Ctrl Logout User:${usernameForLog}]`;
    // Setze Flash-Nachricht VOR dem Zerstören der Session
    req.flash('success', 'Erfolgreich ausgeloggt.');
    req.logout((errLogout) => {
        if (errLogout) {
            log.error(`${fnLogPrefix} Fehler beim Passport Logout:`, errLogout);
        }
        if (req.session) {
            req.session.destroy((errSession) => {
                if (errSession) {
                    log.error(`${fnLogPrefix} Fehler beim Zerstören der Session:`, errSession);
                    // Keine Flash-Nachricht mehr hier, da Session bereits zerstört
                    return res.redirect('/auth/login');
                }
                log.info(`${fnLogPrefix} User erfolgreich ausgeloggt. Session zerstört.`);
                const sessionCookieName = config_1.default.session.name || 'connect.sid';
                res.clearCookie(sessionCookieName, { path: '/' });
                res.redirect('/auth/login');
            });
        }
        else {
            log.warn(`${fnLogPrefix} Keine Session zum Zerstören vorhanden beim Logout.`);
            const sessionCookieName = config_1.default.session.name || 'connect.sid';
            res.clearCookie(sessionCookieName, { path: '/' });
            res.redirect('/auth/login');
        }
    });
};
exports.logout = logout;
// --- Strava und Google Authentifizierungs-Handler ---
const requestStravaAuthorization = (req, res) => {
    const user = req.user; // KORREKT: req.user verwenden, das von Passport gesetzt wird
    if (!user) {
        log.warn(`[StravaAuthRequest] Kein req.user gefunden. User nicht authentifiziert für Strava Verbindung.`);
        req.flash('error', 'Bitte zuerst einloggen, um Strava zu verbinden.');
        return res.redirect('/auth/login');
    }
    const clientId = config_1.default.strava.clientId;
    const redirectUri = config_1.default.strava.redirectUri;
    if (!clientId || !config_1.default.strava.clientSecret) {
        log.error("[StravaAuthRequest] Strava Client ID oder Client Secret nicht in der Konfiguration gefunden!");
        req.flash('error', 'Strava-Konfiguration unvollständig. Bitte Admin kontaktieren.');
        return res.redirect('/user/settings');
    }
    const authURL = new URL(config_1.default.strava.authUrl);
    authURL.searchParams.append('client_id', clientId.toString());
    authURL.searchParams.append('redirect_uri', redirectUri);
    authURL.searchParams.append('response_type', 'code');
    authURL.searchParams.append('approval_prompt', 'force');
    authURL.searchParams.append('scope', 'activity:read_all,profile:read_all');
    authURL.searchParams.append('state', `user:${user.id};service:strava`);
    log.info(`[StravaAuthRequest User:${user.id}] Redirecting to Strava: ${authURL.toString()}`);
    res.redirect(authURL.toString());
};
exports.requestStravaAuthorization = requestStravaAuthorization;
const handleStravaCallback = async (req, res, next) => {
    const code = req.query.code;
    const stateReceived = req.query.state;
    const fnLogPrefix = `[StravaCallback]`;
    // Wichtig: User-ID aus dem State oder req.user holen
    const userId = req.user?.id || (stateReceived?.split(';')[0]?.split(':')[1] ? parseInt(stateReceived.split(';')[0].split(':')[1], 10) : null);
    if (!userId) {
        log.error(`${fnLogPrefix} Konnte User-ID nicht aus State oder Session ermitteln.`);
        req.flash('error', 'Fehler bei der Strava-Authentifizierung: Benutzerkontext fehlt.');
        return res.redirect('/user/settings');
    }
    if (code && stateReceived) {
        log.info(`${fnLogPrefix} Strava Callback erfolgreich, Code erhalten. User-State: ${stateReceived}, User-ID: ${userId}`);
        try {
            // Hier Logik zum Token-Austausch und Speichern
            // Beispiel: await stravaService.exchangeCodeForTokensAndSave(code, userId);
            log.info(`${fnLogPrefix} Tokens für User ${userId} würden jetzt ausgetauscht und gespeichert.`);
            req.flash('success', 'Strava erfolgreich verbunden (Implementierung ausstehend).');
            res.redirect('/user/settings');
        }
        catch (error) {
            log.error(`${fnLogPrefix} Fehler beim Verarbeiten des Strava Callbacks für User ${userId}:`, error);
            req.flash('error', error.message || 'Fehler beim Verarbeiten der Strava-Antwort.');
            res.redirect('/user/settings');
        }
    }
    else {
        log.error(`${fnLogPrefix} Strava Callback Fehler oder Code/State fehlt.`);
        req.flash('error', 'Fehler bei der Strava-Authentifizierung.');
        res.redirect('/user/settings');
    }
};
exports.handleStravaCallback = handleStravaCallback;
const requestGooglePhotosAuthorization = (req, res) => {
    requestGoogleAuthorization(req, res, 'photos');
};
exports.requestGooglePhotosAuthorization = requestGooglePhotosAuthorization;
const requestGoogleDriveAuthorization = (req, res) => {
    requestGoogleAuthorization(req, res, 'drive');
};
exports.requestGoogleDriveAuthorization = requestGoogleDriveAuthorization;
/**
 * Gemeinsame Funktion für Google OAuth-Autorisierung (Photos und Drive)
 */
const requestGoogleAuthorization = (req, res, service) => {
    const user = req.user;
    if (!user) {
        log.warn(`[GoogleAuthRequest] Kein req.user gefunden.`);
        req.flash('error', `Bitte zuerst einloggen, um Google ${service === 'photos' ? 'Photos' : 'Drive'} zu verbinden.`);
        return res.redirect('/auth/login');
    }
    if (!config_1.default.googlePhotos.clientId || !config_1.default.googlePhotos.clientSecret) {
        log.error("[GoogleAuthRequest] Google Client ID oder Secret nicht konfiguriert!");
        req.flash('error', `Google ${service === 'photos' ? 'Photos' : 'Drive'}-Konfiguration unvollständig.`);
        return res.redirect('/user/settings');
    }
    const oauth2Client = createGoogleOAuth2Client();
    // Kombiniere Scopes für beide Services
    const combinedScopes = [
        ...config_1.default.googlePhotos.scopes,
        ...config_1.default.googleDrive.scopes
    ];
    const authURL = oauth2Client.generateAuthUrl({
        access_type: 'offline',
        prompt: 'consent', // Wichtig, um immer einen Refresh-Token zu bekommen
        scope: combinedScopes,
        state: `user:${user.id};service:${service}` // User-ID und Service im State mitsenden
    });
    log.info(`[GoogleAuthRequest User:${user.id}] Redirecting to Google for ${service}: ${authURL}`);
    res.redirect(authURL);
};
const handleGooglePhotosCallback = async (req, res, next) => {
    await handleGoogleCallback(req, res, next);
};
exports.handleGooglePhotosCallback = handleGooglePhotosCallback;
/**
 * Trennt die Verbindung zu Google Photos
 */
const disconnectGooglePhotos = async (req, res) => {
    const userId = req.user?.id;
    const fnLogPrefix = `[GooglePhotosDisconnect User:${userId}]`;
    if (!userId) {
        log.error(`${fnLogPrefix} Kein User in der Session gefunden.`);
        req.flash('error', 'Bitte zuerst einloggen.');
        return res.redirect('/auth/login');
    }
    try {
        const success = await userRepository.clearUserGoogleTokens(userId);
        if (success) {
            log.info(`${fnLogPrefix} Google Photos Verbindung erfolgreich getrennt.`);
            req.flash('success', 'Google Photos Verbindung erfolgreich getrennt.');
        }
        else {
            log.warn(`${fnLogPrefix} Google Photos Verbindung konnte nicht getrennt werden.`);
            req.flash('warning', 'Google Photos Verbindung konnte nicht getrennt werden.');
        }
    }
    catch (error) {
        log.error(`${fnLogPrefix} Fehler beim Trennen der Google Photos Verbindung:`, error.message);
        req.flash('error', `Fehler beim Trennen der Google Photos Verbindung: ${error.message}`);
    }
    res.redirect('/user/settings');
};
exports.disconnectGooglePhotos = disconnectGooglePhotos;
/**
 * Gemeinsamer Google OAuth Callback-Handler für Photos und Drive
 */
const handleGoogleCallback = async (req, res, next) => {
    const code = req.query.code;
    const stateReceived = req.query.state;
    const fnLogPrefix = `[GoogleCallback]`;
    // User-ID und Service aus dem State extrahieren
    let userIdFromState = null;
    let serviceFromState = null;
    if (stateReceived) {
        const parts = stateReceived.split(';');
        const userPart = parts.find(p => p.startsWith('user:'));
        const servicePart = parts.find(p => p.startsWith('service:'));
        if (userPart) {
            userIdFromState = parseInt(userPart.split(':')[1], 10);
        }
        if (servicePart) {
            serviceFromState = servicePart.split(':')[1];
        }
    }
    const userId = req.user?.id || userIdFromState;
    if (!userId) {
        log.error(`${fnLogPrefix} Konnte User-ID nicht aus State oder Session ermitteln.`);
        req.flash('error', 'Fehler bei der Google Photos-Authentifizierung: Benutzerkontext fehlt.');
        return res.redirect('/user/settings');
    }
    if (code) {
        const serviceName = serviceFromState === 'drive' ? 'Google Drive' :
            serviceFromState === 'photos-picker' ? 'Google Photos Picker' :
                'Google Photos';
        log.info(`${fnLogPrefix} ${serviceName} Callback erfolgreich, Code erhalten für User-ID: ${userId}. Code: ${code.substring(0, 15)}...`);
        const oauth2Client = createGoogleOAuth2Client();
        try {
            const { tokens } = await oauth2Client.getToken(code);
            log.info(`${fnLogPrefix} Tokens von Google erhalten für User ${userId}. AccessToken vorhanden: ${!!tokens.access_token}, RefreshToken vorhanden: ${!!tokens.refresh_token}`);
            oauth2Client.setCredentials(tokens); // Wichtig für weitere API-Aufrufe, falls direkt hier nötig
            // Analysiere die erhaltenen Scopes um zu bestimmen, welche Services aktiviert wurden
            const grantedScopes = tokens.scope ? tokens.scope.split(' ') : [];
            const hasPhotosScope = grantedScopes.some(scope => scope.includes('photoslibrary'));
            const hasDriveScope = grantedScopes.some(scope => scope.includes('drive'));
            log.info(`${fnLogPrefix} Erhaltene Scopes: ${tokens.scope}. Photos: ${hasPhotosScope}, Drive: ${hasDriveScope}`);
            const successMessages = [];
            const errorMessages = [];
            // Speichere Google Photos Tokens, wenn Photos-Scope vorhanden ist
            if (hasPhotosScope) {
                const tokenDataToSave = {
                    accessToken: tokens.access_token || null,
                    refreshToken: tokens.refresh_token || null,
                    expiresAt: tokens.expiry_date || null,
                    scope: tokens.scope || null
                };
                const success = await userRepository.updateUserGoogleTokens(userId, tokenDataToSave);
                if (success) {
                    log.info(`${fnLogPrefix} Google Photos Tokens erfolgreich für User ${userId} in DB gespeichert.`);
                    successMessages.push('Google Photos');
                }
                else {
                    log.error(`${fnLogPrefix} Fehler beim Speichern der Google Photos Tokens in DB für User ${userId}.`);
                    errorMessages.push('Google Photos');
                }
            }
            // Speichere Google Drive Tokens, wenn Drive-Scope vorhanden ist
            if (hasDriveScope) {
                const tokenDataToSave = {
                    accessToken: tokens.access_token || null,
                    refreshToken: tokens.refresh_token || null,
                    expiresAt: tokens.expiry_date || null,
                    scope: tokens.scope || null
                };
                const success = await userRepository.updateUserGoogleDriveTokens(userId, tokenDataToSave);
                if (success) {
                    log.info(`${fnLogPrefix} Google Drive Tokens erfolgreich für User ${userId} in DB gespeichert.`);
                    successMessages.push('Google Drive');
                }
                else {
                    log.error(`${fnLogPrefix} Fehler beim Speichern der Google Drive Tokens in DB für User ${userId}.`);
                    errorMessages.push('Google Drive');
                }
            }
            // Speichere Google Photos Picker Tokens, wenn Photos-Picker-Service angefragt wurde
            if (serviceFromState === 'photos-picker') {
                const tokenDataToSave = {
                    accessToken: tokens.access_token || null,
                    refreshToken: tokens.refresh_token || null,
                    expiresAt: tokens.expiry_date || null,
                    scope: tokens.scope || null
                };
                const success = await userRepository.updateUserGooglePhotosPickerTokens(userId, tokenDataToSave);
                if (success) {
                    log.info(`${fnLogPrefix} Google Photos Picker Tokens erfolgreich für User ${userId} in DB gespeichert.`);
                    successMessages.push('Google Photos Picker');
                }
                else {
                    log.error(`${fnLogPrefix} Fehler beim Speichern der Google Photos Picker Tokens in DB für User ${userId}.`);
                    errorMessages.push('Google Photos Picker');
                }
            }
            // Erstelle entsprechende Flash-Nachrichten
            if (successMessages.length > 0) {
                const message = successMessages.length === 1
                    ? `${successMessages[0]} erfolgreich verbunden!`
                    : `${successMessages.join(' und ')} erfolgreich verbunden!`;
                req.flash('success', message);
            }
            if (errorMessages.length > 0) {
                const message = errorMessages.length === 1
                    ? `Fehler beim Speichern der ${errorMessages[0]} Tokens.`
                    : `Fehler beim Speichern der ${errorMessages.join(' und ')} Tokens.`;
                req.flash('error', message);
            }
            // Fallback, falls keine Scopes erkannt wurden
            if (!hasPhotosScope && !hasDriveScope) {
                log.warn(`${fnLogPrefix} Keine bekannten Scopes in der Token-Antwort gefunden: ${tokens.scope}`);
                req.flash('warning', 'Google-Verbindung hergestellt, aber keine bekannten Services erkannt.');
            }
            res.redirect('/user/settings');
        }
        catch (error) {
            log.error(`${fnLogPrefix} Fehler beim Austauschen des Google Codes gegen Tokens für User ${userId}:`, error.response?.data || error.message || error);
            req.flash('error', error.message || `Fehler beim Verarbeiten der ${serviceName}-Antwort.`);
            res.redirect('/user/settings');
        }
    }
    else {
        const errorFromQuery = req.query.error;
        const serviceName = serviceFromState === 'drive' ? 'Google Drive' : 'Google Photos';
        log.error(`${fnLogPrefix} ${serviceName} Callback Fehler oder Code fehlt. Fehler von Google: ${errorFromQuery}`);
        req.flash('error', `Fehler bei der ${serviceName}-Authentifizierung: ${errorFromQuery || 'Kein Code erhalten.'}`);
        res.redirect('/user/settings');
    }
};
// --- Google Photos Authentifizierungs-Handler ---
function createGoogleOAuth2Client() {
    return new googleapis_1.google.auth.OAuth2(config_1.default.googlePhotos.clientId, config_1.default.googlePhotos.clientSecret, config_1.default.googlePhotos.redirectUri);
}
// --- Token Refresh Logik (Beispiel für Strava, angepasst für User) ---
// Diese Funktion könnte auch in einem stravaService.ts liegen
const refreshUserStravaToken = async (userId) => {
    const fnLogPrefix = `[StravaTokenRefresh User:${userId}]`;
    log.info(`${fnLogPrefix} Attempting to refresh Strava access token...`);
    const userTokens = await userRepository.getUserStravaTokens(userId);
    if (!userTokens || !userTokens.strava_refresh_token) {
        log.error(`${fnLogPrefix} No Strava refresh token found in DB for user.`);
        throw new Error("Kein Strava Refresh Token für den Benutzer verfügbar.");
    }
    const clientId = config_1.default.strava.clientId;
    const clientSecret = config_1.default.strava.clientSecret;
    const tokenUrl = config_1.default.strava.tokenUrl;
    if (!clientId || !clientSecret) {
        log.error(`${fnLogPrefix} Strava Client ID or Secret not configured!`);
        throw new Error("Serverkonfigurationsfehler: Strava Client ID oder Secret fehlen.");
    }
    try {
        const response = await fetch(tokenUrl, {
            method: 'POST',
            headers: { "Content-Type": "application/json; charset=UTF-8" },
            body: JSON.stringify({
                client_id: clientId,
                client_secret: clientSecret,
                refresh_token: userTokens.strava_refresh_token,
                grant_type: 'refresh_token'
            })
        });
        const stravaData = await response.json();
        if (!response.ok) {
            log.error(`${fnLogPrefix} Strava API error during token refresh:`, response.status, stravaData);
            if (response.status === 400 && stravaData.message?.includes("Invalid Refresh Token")) {
                // Refresh Token ist ungültig, lösche ihn aus der DB, damit User neu autorisieren muss
                await userRepository.updateUserStravaTokens(userId, { refreshToken: null, accessToken: null, expiresAt: null });
                log.warn(`${fnLogPrefix} Invalid Strava refresh token for user ${userId}. Tokens cleared from DB.`);
            }
            const apiErrorMessage = stravaData.message || (stravaData.errors && stravaData.errors[0]?.message) || 'Unbekannter Strava API Fehler beim Refresh';
            throw new Error(`Strava API Fehler (${response.status}): ${apiErrorMessage}`);
        }
        log.info(`${fnLogPrefix} Strava token refresh successful.`);
        const updatedTokenData = {
            accessToken: stravaData.access_token,
            refreshToken: stravaData.refresh_token || userTokens.strava_refresh_token, // Strava sendet manchmal neuen RT
            expiresAt: stravaData.expires_at,
            scope: userTokens.strava_scope, // Scope bleibt meist gleich
            athleteId: userTokens.strava_athlete_id
        };
        await userRepository.updateUserStravaTokens(userId, updatedTokenData);
        log.info(`${fnLogPrefix} Updated Strava tokens saved for user ${userId}.`);
        return updatedTokenData.accessToken;
    }
    catch (error) {
        log.error(`${fnLogPrefix} Critical error refreshing Strava token: ${error.message}`);
        throw error; // Fehler weiterwerfen
    }
};
exports.refreshUserStravaToken = refreshUserStravaToken;
// refreshGoogleAccessToken (ähnlich wie oben, aber für Google)
// Diese Funktion wird typischerweise vom googlePhotosService aufgerufen, wenn ein Token abgelaufen ist.
// Sie ist komplexer, da sie oft direkt mit dem google.auth.OAuth2Client arbeitet.
// Fürs Erste als Platzhalter, die Logik im googlePhotosService.ts ist hier relevanter.
const refreshUserGoogleAccessTokenUtility = async (userId) => {
    const fnLogPrefix = `[GoogleTokenRefreshUtil User:${userId}]`;
    log.info(`${fnLogPrefix} Placeholder for refreshing Google access token for a user.`);
    // TODO: Implementiere Logik ähnlich zu refreshUserStravaToken, aber mit dem googleOAuth2Client.
    // 1. User's google_refresh_token aus DB holen.
    // 2. oauth2Client.setCredentials({ refresh_token: userRefreshToken });
    // 3. const { credentials } = await oauth2Client.refreshAccessToken();
    // 4. Neue credentials.access_token und credentials.expiry_date in DB für User speichern.
    // 5. Neuen access_token zurückgeben.
    // Diese Logik ist schon teilweise in googlePhotosService.getAuthenticatedClient,
    // es müsste nur User-spezifisch gemacht werden.
    return null;
};
exports.refreshUserGoogleAccessTokenUtility = refreshUserGoogleAccessTokenUtility;
//# sourceMappingURL=authController.js.map