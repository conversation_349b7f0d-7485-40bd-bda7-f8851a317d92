"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getGooglePhotosPickerStatus = exports.disconnectGooglePhotosPicker = exports.handleGooglePhotosPickerCallback = exports.startGooglePhotosPickerAuth = void 0;
const config_1 = __importDefault(require("../config/config"));
const logger_1 = __importDefault(require("../utils/logger"));
const userRepository = __importStar(require("../db/userRepository"));
const log = logger_1.default.getLogger(__filename);
/**
 * Startet den Google Photos Picker OAuth-Flow
 */
const startGooglePhotosPickerAuth = (req, res) => {
    const fnLogPrefix = `[GooglePhotosPickerAuth Start User:${req.user?.id}]`;
    try {
        if (!req.user) {
            res.status(401).json({ error: 'Nicht authentifiziert' });
            return;
        }
        // Erstelle OAuth-URL für Google Photos Picker
        const authUrl = new URL(config_1.default.googlePhotos.authUrl);
        authUrl.searchParams.set('client_id', config_1.default.googlePhotos.clientId);
        authUrl.searchParams.set('redirect_uri', config_1.default.googlePhotos.redirectUri);
        authUrl.searchParams.set('response_type', 'code');
        authUrl.searchParams.set('scope', 'https://www.googleapis.com/auth/photospicker.mediaitems.readonly');
        authUrl.searchParams.set('access_type', 'offline');
        authUrl.searchParams.set('prompt', 'consent');
        authUrl.searchParams.set('state', `user:${req.user.id};service:photos-picker`);
        log.info(`${fnLogPrefix} Redirecting to Google Photos Picker OAuth: ${authUrl.toString()}`);
        res.redirect(authUrl.toString());
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error starting Google Photos Picker auth:`, error);
        res.status(500).json({ error: 'Fehler beim Starten der Authentifizierung' });
    }
};
exports.startGooglePhotosPickerAuth = startGooglePhotosPickerAuth;
/**
 * Behandelt den OAuth-Callback von Google Photos Picker
 */
const handleGooglePhotosPickerCallback = async (req, res) => {
    const fnLogPrefix = `[GooglePhotosPickerAuth Callback]`;
    const { code, state, error } = req.query;
    try {
        if (error) {
            log.error(`${fnLogPrefix} OAuth error from Google:`, error);
            res.redirect('/user/settings?error=google_photos_picker_auth_failed');
            return;
        }
        if (!code || !state) {
            log.error(`${fnLogPrefix} Missing code or state parameter`);
            res.redirect('/user/settings?error=google_photos_picker_missing_params');
            return;
        }
        // Extrahiere User ID aus state
        const stateMatch = state.match(/^user_(\d+)$/);
        if (!stateMatch) {
            log.error(`${fnLogPrefix} Invalid state parameter: ${state}`);
            res.redirect('/user/settings?error=google_photos_picker_invalid_state');
            return;
        }
        const userId = parseInt(stateMatch[1], 10);
        log.info(`${fnLogPrefix} Processing callback for user ${userId}`);
        // Tausche Authorization Code gegen Access Token
        const tokenResponse = await fetch(config_1.default.googlePhotos.tokenUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                client_id: config_1.default.googlePhotos.clientId,
                client_secret: config_1.default.googlePhotos.clientSecret,
                code: code,
                grant_type: 'authorization_code',
                redirect_uri: config_1.default.googlePhotos.redirectUri,
            }),
        });
        if (!tokenResponse.ok) {
            const errorText = await tokenResponse.text();
            log.error(`${fnLogPrefix} Token exchange failed: ${tokenResponse.status} ${errorText}`);
            res.redirect('/user/settings?error=google_photos_picker_token_failed');
            return;
        }
        const tokenData = await tokenResponse.json();
        log.info(`${fnLogPrefix} Token exchange successful for user ${userId}`);
        // Speichere Tokens in der Datenbank
        const expiresAt = tokenData.expires_in ? Date.now() + (tokenData.expires_in * 1000) : null;
        await userRepository.updateUserGooglePhotosPickerTokens(userId, {
            accessToken: tokenData.access_token,
            refreshToken: tokenData.refresh_token || null,
            expiresAt: expiresAt,
            scope: tokenData.scope || 'https://www.googleapis.com/auth/photospicker.mediaitems.readonly'
        });
        log.info(`${fnLogPrefix} Google Photos Picker tokens saved for user ${userId}`);
        res.redirect('/user/settings?success=google_photos_picker_connected');
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error handling Google Photos Picker callback:`, error);
        res.redirect('/user/settings?error=google_photos_picker_callback_error');
    }
};
exports.handleGooglePhotosPickerCallback = handleGooglePhotosPickerCallback;
/**
 * Trennt die Google Photos Picker Verbindung
 */
const disconnectGooglePhotosPicker = async (req, res) => {
    const fnLogPrefix = `[GooglePhotosPickerAuth Disconnect User:${req.user?.id}]`;
    try {
        if (!req.user) {
            res.status(401).json({ error: 'Nicht authentifiziert' });
            return;
        }
        // Lösche Tokens aus der Datenbank
        await userRepository.clearUserGooglePhotosPickerTokens(req.user.id);
        log.info(`${fnLogPrefix} Google Photos Picker disconnected for user ${req.user.id}`);
        res.redirect('/user/settings?success=google_photos_picker_disconnected');
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error disconnecting Google Photos Picker:`, error);
        res.redirect('/user/settings?error=google_photos_picker_disconnect_failed');
    }
};
exports.disconnectGooglePhotosPicker = disconnectGooglePhotosPicker;
/**
 * Prüft den Status der Google Photos Picker Verbindung
 */
const getGooglePhotosPickerStatus = async (req, res) => {
    const fnLogPrefix = `[GooglePhotosPickerAuth Status User:${req.user?.id}]`;
    try {
        if (!req.user) {
            res.status(401).json({ error: 'Nicht authentifiziert' });
            return;
        }
        const tokens = await userRepository.getUserGooglePhotosPickerTokens(req.user.id);
        const isConnected = !!(tokens && tokens.accessToken);
        log.debug(`${fnLogPrefix} Google Photos Picker status: ${isConnected ? 'connected' : 'not connected'}`);
        res.json({
            connected: isConnected,
            scope: tokens?.scope || null,
            expiresAt: tokens?.expiresAt || null
        });
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error getting Google Photos Picker status:`, error);
        res.status(500).json({ error: 'Fehler beim Abrufen des Status' });
    }
};
exports.getGooglePhotosPickerStatus = getGooglePhotosPickerStatus;
//# sourceMappingURL=googlePhotosPickerAuthController.js.map