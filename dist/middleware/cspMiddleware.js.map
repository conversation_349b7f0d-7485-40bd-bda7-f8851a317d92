{"version": 3, "file": "cspMiddleware.js", "sourceRoot": "", "sources": ["../../src/middleware/cspMiddleware.ts"], "names": [], "mappings": ";;;;;;AAEA,6DAAqC;AAErC,MAAM,GAAG,GAAG,gBAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;AAEzC;;;GAGG;AACI,MAAM,aAAa,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;IACnF,MAAM,WAAW,GAAG,kBAAkB,CAAC;IAEvC,IAAI,CAAC;QACD,2DAA2D;QAC3D,MAAM,aAAa,GAAG;YAClB,mBAAmB;YACnB,oBAAoB;YAEpB,yDAAyD;YACzD,kDAAkD;gBAClD,0BAA0B;gBAC1B,0BAA0B;gBAC1B,8BAA8B;gBAC9B,0BAA0B;gBAC1B,yBAAyB;gBACzB,aAAa;YAEb,sEAAsE;YACtE,mCAAmC;gBACnC,+BAA+B;gBAC/B,0BAA0B;gBAC1B,8BAA8B;gBAC9B,8BAA8B;YAE9B,qCAAqC;YACrC,kBAAkB;gBAClB,4BAA4B;gBAC5B,0BAA0B;gBAC1B,OAAO;YAEP,uDAAuD;YACvD,iBAAiB;gBACjB,2BAA2B;gBAC3B,wBAAwB;gBACxB,uBAAuB;gBACvB,kCAAkC;gBAClC,8BAA8B;gBAC9B,aAAa;YAEb,iDAAiD;YACjD,mBAAmB;gBACnB,8BAA8B;gBAC9B,yBAAyB;gBACzB,0BAA0B;gBAC1B,2BAA2B;gBAC3B,2BAA2B;YAE3B,0DAA0D;YAC1D,qBAAqB;gBACrB,2BAA2B;gBAC3B,wBAAwB;gBACxB,8BAA8B;gBAC9B,yBAAyB;gBACzB,gCAAgC;gBAChC,sCAAsC;YAEtC,4CAA4C;YAC5C,mBAAmB;gBACnB,8BAA8B;gBAC9B,yBAAyB;gBACzB,OAAO;YAEP,qCAAqC;YACrC,mBAAmB;YAEnB,gCAAgC;YAChC,iBAAiB;YAEjB,gDAAgD;YAChD,qBAAqB;gBACrB,6BAA6B;YAE7B,2CAA2C;YAC3C,wBAAwB;YAExB,0CAA0C;YAC1C,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,CAAC,CAAC,CAAC,CAAC,2BAA2B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;SAClF,CAAC;QAEF,mBAAmB;QACnB,MAAM,SAAS,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3C,GAAG,CAAC,SAAS,CAAC,yBAAyB,EAAE,SAAS,CAAC,CAAC;QAEpD,iCAAiC;QACjC,GAAG,CAAC,SAAS,CAAC,wBAAwB,EAAE,SAAS,CAAC,CAAC;QACnD,GAAG,CAAC,SAAS,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;QACzC,GAAG,CAAC,SAAS,CAAC,kBAAkB,EAAE,eAAe,CAAC,CAAC;QACnD,GAAG,CAAC,SAAS,CAAC,iBAAiB,EAAE,iCAAiC,CAAC,CAAC;QAEpE,qCAAqC;QACrC,GAAG,CAAC,SAAS,CAAC,oBAAoB,EAC9B,aAAa;YACb,iBAAiB;YACjB,sBAAsB;YACtB,cAAc;YACd,UAAU;YACV,mBAAmB;YACnB,gBAAgB;YAChB,kBAAkB,CACrB,CAAC;QAEF,GAAG,CAAC,KAAK,CAAC,GAAG,WAAW,wBAAwB,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;IAEhE,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QAClB,GAAG,CAAC,KAAK,CAAC,GAAG,WAAW,6BAA6B,EAAE,KAAK,CAAC,CAAC;QAC9D,0EAA0E;IAC9E,CAAC;IAED,IAAI,EAAE,CAAC;AACX,CAAC,CAAC;AA9GW,QAAA,aAAa,iBA8GxB;AAEF;;GAEG;AACI,MAAM,oBAAoB,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;IAC1F,MAAM,WAAW,GAAG,0BAA0B,CAAC;IAE/C,IAAI,CAAC;QACD,iDAAiD;QACjD,MAAM,oBAAoB,GAAG;YACzB,2BAA2B;YAC3B,oEAAoE;YACpE,yCAAyC;YACzC,mCAAmC;YACnC,8BAA8B;YAC9B,2BAA2B;YAC3B,yBAAyB;YACzB,+BAA+B;YAC/B,mBAAmB;YACnB,iBAAiB;SACpB,CAAC;QAEF,MAAM,SAAS,GAAG,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAClD,GAAG,CAAC,SAAS,CAAC,yBAAyB,EAAE,SAAS,CAAC,CAAC;QAEpD,GAAG,CAAC,KAAK,CAAC,GAAG,WAAW,gCAAgC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;IAExE,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QAClB,GAAG,CAAC,KAAK,CAAC,GAAG,WAAW,qCAAqC,EAAE,KAAK,CAAC,CAAC;IAC1E,CAAC;IAED,IAAI,EAAE,CAAC;AACX,CAAC,CAAC;AA5BW,QAAA,oBAAoB,wBA4B/B"}