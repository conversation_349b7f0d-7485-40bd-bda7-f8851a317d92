"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.relaxedCspMiddleware = exports.cspMiddleware = void 0;
const logger_1 = __importDefault(require("../utils/logger"));
const log = logger_1.default.getLogger(__filename);
/**
 * Content Security Policy Middleware
 * Konfiguriert CSP-Header für sichere Nutzung von Google APIs
 */
const cspMiddleware = (req, res, next) => {
    const fnLogPrefix = `[CSP Middleware]`;
    try {
        // CSP-Direktiven für Google APIs und allgemeine Sicherheit
        const cspDirectives = [
            // Basis-Direktiven
            "default-src 'self'",
            // Script-Quellen: Erlaube Google APIs, CDNs und inline Scripts
            "script-src 'self' 'unsafe-inline' 'unsafe-eval' " +
                "https://apis.google.com " +
                "https://www.gstatic.com " +
                "https://accounts.google.com " +
                "https://ssl.gstatic.com " +
                "https://www.google.com " +
                "https://unpkg.com " +
                "https://cdn.jsdelivr.net " +
                "https://cdnjs.cloudflare.com " +
                "blob: data:",
            // Style-Quellen: Erlaube Google Fonts, CDNs und inline Styles
            "style-src 'self' 'unsafe-inline' " +
                "https://fonts.googleapis.com " +
                "https://www.gstatic.com " +
                "https://accounts.google.com " +
                "https://cdnjs.cloudflare.com " +
                "https://unpkg.com " +
                "https://cdn.jsdelivr.net",
            // Font-Quellen: Erlaube Google Fonts und Font Awesome
            "font-src 'self' " +
                "https://fonts.gstatic.com " +
                "https://www.gstatic.com " +
                "https://cdnjs.cloudflare.com " +
                "data:",
            // Bild-Quellen: Erlaube Google Services und Daten-URLs
            "img-src 'self' " +
                "https://*.googleapis.com " +
                "https://*.gstatic.com " +
                "https://*.google.com " +
                "https://*.googleusercontent.com " +
                "https://accounts.google.com " +
                "data: blob:",
            // Frame-Quellen: Erlaube Google OAuth und Picker
            "frame-src 'self' " +
                "https://accounts.google.com " +
                "https://www.google.com " +
                "https://docs.google.com " +
                "https://drive.google.com " +
                "https://photos.google.com",
            // Connect-Quellen: Erlaube API-Aufrufe zu Google Services
            "connect-src 'self' " +
                "https://*.googleapis.com " +
                "https://*.gstatic.com " +
                "https://accounts.google.com " +
                "https://www.google.com " +
                "https://oauth2.googleapis.com " +
                "https://photoslibrary.googleapis.com",
            // Child-Quellen: Für Web Workers und Frames
            "child-src 'self' " +
                "https://accounts.google.com " +
                "https://www.google.com " +
                "blob:",
            // Object-Quellen: Beschränke Plugins
            "object-src 'none'",
            // Base-URI: Beschränke base-Tag
            "base-uri 'self'",
            // Form-Action: Erlaube eigene Domain und Google
            "form-action 'self' " +
                "https://accounts.google.com",
            // Frame-Ancestors: Verhindere Clickjacking
            "frame-ancestors 'none'",
            // Upgrade insecure requests in production
            ...(process.env.NODE_ENV === 'production' ? ["upgrade-insecure-requests"] : [])
        ];
        // Setze CSP-Header
        const cspHeader = cspDirectives.join('; ');
        res.setHeader('Content-Security-Policy', cspHeader);
        // Zusätzliche Sicherheits-Header
        res.setHeader('X-Content-Type-Options', 'nosniff');
        res.setHeader('X-Frame-Options', 'DENY');
        res.setHeader('X-XSS-Protection', '1; mode=block');
        res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
        // Permissions Policy für Google APIs
        res.setHeader('Permissions-Policy', 'camera=(), ' +
            'microphone=(), ' +
            'geolocation=(self), ' +
            'payment=(), ' +
            'usb=(), ' +
            'magnetometer=(), ' +
            'gyroscope=(), ' +
            'accelerometer=()');
        log.debug(`${fnLogPrefix} CSP headers set for ${req.path}`);
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error setting CSP headers:`, error);
        // Fahre trotz Fehler fort - CSP ist nicht kritisch für die Funktionalität
    }
    next();
};
exports.cspMiddleware = cspMiddleware;
/**
 * Relaxed CSP für Google APIs (für spezielle Routen)
 */
const relaxedCspMiddleware = (req, res, next) => {
    const fnLogPrefix = `[Relaxed CSP Middleware]`;
    try {
        // Sehr permissive CSP für Google API Integration
        const relaxedCspDirectives = [
            "default-src 'self' https:",
            "script-src 'self' 'unsafe-inline' 'unsafe-eval' https: blob: data:",
            "style-src 'self' 'unsafe-inline' https:",
            "img-src 'self' https: data: blob:",
            "font-src 'self' https: data:",
            "connect-src 'self' https:",
            "frame-src 'self' https:",
            "child-src 'self' https: blob:",
            "object-src 'none'",
            "base-uri 'self'"
        ];
        const cspHeader = relaxedCspDirectives.join('; ');
        res.setHeader('Content-Security-Policy', cspHeader);
        log.debug(`${fnLogPrefix} Relaxed CSP headers set for ${req.path}`);
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error setting relaxed CSP headers:`, error);
    }
    next();
};
exports.relaxedCspMiddleware = relaxedCspMiddleware;
//# sourceMappingURL=cspMiddleware.js.map