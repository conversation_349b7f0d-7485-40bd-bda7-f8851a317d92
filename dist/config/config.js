"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
// src/config/config.ts
const dotenv_1 = __importDefault(require("dotenv"));
const path_1 = __importDefault(require("path"));
const package_json_1 = __importDefault(require("../../package.json")); // Stellt sicher, dass der Pfad zu deiner package.json korrekt ist
dotenv_1.default.config(); // Lädt .env Datei
// Helper-Funktionen mit Typisierung
const env = (key, defaultValue) => {
    const value = process.env[key];
    if (value === undefined) {
        if (defaultValue === undefined) {
            // console.warn(`Umgebungsvariable ${key} nicht gesetzt und kein Standardwert vorhanden!`);
            // Für kritische Werte wie DB-Passwörter könntest du hier einen Fehler werfen.
            return ''; // Fallback für nicht-kritische Werte
        }
        return defaultValue;
    }
    return value;
};
const envInt = (key, defaultValue) => {
    const value = process.env[key];
    const parsed = parseInt(value || '', 10);
    return isNaN(parsed) ? defaultValue : parsed;
};
const envBool = (key, defaultValue) => {
    const value = process.env[key];
    if (value === undefined || value === null)
        return defaultValue;
    return value.toLowerCase() === 'true' || value === '1';
};
const envFloat = (key, defaultValue) => {
    const value = process.env[key];
    const parsed = parseFloat(value || '');
    return isNaN(parsed) ? defaultValue : parseFloat(defaultValue.toString()); // Fallback auf Default, wenn Parsing fehlschlägt
};
// === CONFIGURATION OBJECT INITIALIZATION ===
const appNameFromEnv = env('APP_NAME', 'Master-Map');
const appConfig = {
    env: env('NODE_ENV', 'development'),
    server: {
        port: envInt('PORT', 3000),
        nodeEnv: env('NODE_ENV', 'development'),
        sessionSecret: env('SESSION_SECRET', 'bitte-unbedingt-aendern-123!'),
        sessionMaxAge: envInt('SESSION_MAX_AGE_MS', 24 * 60 * 60 * 1000),
        baseUrl: env('BASE_URL', `http://localhost:${envInt('PORT', 3000)}`),
    },
    db: {
        host: env('DB_HOST', 'localhost'),
        port: envInt('DB_PORT', 3306), // Hinzugefügt
        user: env('DB_USER', 'strava_user'), // Aus Ihrer Config
        password: env('DB_PASSWORD', env('MYSQL_PW')), // Nimmt MYSQL_PW, wenn DB_PASSWORD nicht gesetzt
        database: env('DB_NAME', 'strava_activities'), // Aus Ihrer Config
        connectionLimit: envInt('DB_CONNECTION_LIMIT', 10),
    },
    email: {
        service: env('EMAIL_SERVICE', 'gmail'), // Standard auf Gmail, wenn Sie Gmails SMTP direkt verwenden wollen
        host: env('EMAIL_HOST', 'smtp.gmail.com'),
        port: envInt('EMAIL_PORT', 465), // 465 für SSL, 587 für TLS
        secure: envBool('EMAIL_SECURE', true), // true, wenn Port 465
        auth: {
            user: env('EMAIL_USER', '<EMAIL>'), // Ihre Gmail-Adresse
            pass: env('EMAIL_PASS', ''), // Ihr Gmail App-Passwort (aus .env laden!)
        },
        fromAddress: env('EMAIL_FROM_ADDRESS', `"${appNameFromEnv}" <<EMAIL>>`),
    },
    session: {
        // Name des Session-Cookies. Wenn nicht gesetzt, verwendet express-session 'connect.sid'.
        // Es ist gut, einen spezifischen Namen zu definieren.
        name: env('SESSION_COOKIE_NAME', 'map.session.sid'), // Beispiel für einen spezifischen Namen
        secret: env('SESSION_SECRET', 'IhrSehrGeheimesSessionSecretHierAendern!UndLangMachen!'),
        resave: false, // Nicht bei jeder Anfrage speichern, nur wenn sich die Session ändert
        saveUninitialized: false, // Keine Session für unmodifizierte, neue Sessions speichern
        cookie: {
            secure: env('NODE_ENV', 'development') === 'production', // Cookie nur über HTTPS senden (in Produktion)
            httpOnly: true, // Verhindert Zugriff auf Cookie über clientseitiges JavaScript
            maxAge: envInt('SESSION_MAX_AGE_MS', 24 * 60 * 60 * 1000), // 24 Stunden Gültigkeit
            sameSite: 'lax', // Schutz gegen CSRF-Angriffe. 'lax' ist ein guter Standard.
        },
    },
    app: {
        name: appNameFromEnv,
        version: package_json_1.default.version || '1.0.0',
    },
    strava: {
        clientId: envInt('STRAVA_CLIENT_ID', 0), // Strava Client ID ist eine Zahl
        clientSecret: env('STRAVA_CLIENT_SECRET', ''),
        redirectUri: env('STRAVA_REDIRECT_URI', `http://localhost:${envInt('PORT', 3000)}/auth/strava/callback`),
        authUrl: env('STRAVA_AUTH_URL', 'https://www.strava.com/oauth/authorize'),
        tokenUrl: env('STRAVA_TOKEN_URL', 'https://www.strava.com/oauth/token'),
        apiBaseUrl: env('STRAVA_API_BASE_URL', 'https://www.strava.com/api/v3'),
        activitiesPerPage: envInt('STRAVA_ACTIVITIES_PER_PAGE', 50),
        defaultSyncNumPerPage: envInt('DEFAULT_SYNC_NUM_PER_PAGE', 30),
        photoSizeForDownload: envInt('STRAVA_PHOTO_SIZE_DOWNLOAD', 2048), // Ihre höhere Auflösung
        streamTypesToRequest: env('STRAVA_STREAM_TYPES', 'time,distance,latlng,altitude,velocity_smooth,heartrate,cadence,watts,temp,grade_smooth'),
    },
    stravaWebhook: {
        verifyToken: env('STRAVA_WEBHOOK_VERIFY_TOKEN', 'DEIN_GEHEIMER_VERIFY_TOKEN_HIER_AENDERN'),
        callbackBasePath: '/strava/webhook', // Basis-Pfad für den Webhook-Endpunkt
    },
    googlePhotos: {
        clientId: env('GOOGLE_CLIENT_ID', ''),
        clientSecret: env('GOOGLE_CLIENT_SECRET', ''),
        redirectUri: env('GOOGLE_REDIRECT_URI', `http://localhost:${envInt('PORT', 3000)}/auth/google/callback`),
        authUrl: env('GOOGLE_AUTH_URL', 'https://accounts.google.com/o/oauth2/v2/auth'),
        tokenUrl: env('GOOGLE_TOKEN_URL', 'https://oauth2.googleapis.com/token'),
        // WICHTIG: photoslibrary.readonly wurde am 1. April 2025 von Google entfernt!
        // Für das Durchsuchen von Benutzerfotos muss die neue Google Photos Picker API verwendet werden
        scopes: ['https://www.googleapis.com/auth/photoslibrary.readonly.appcreateddata'],
        albumPageSize: envInt('GOOGLE_ALBUM_PAGE_SIZE', 50),
    },
    googleDrive: {
        clientId: env('GOOGLE_CLIENT_ID', ''),
        clientSecret: env('GOOGLE_CLIENT_SECRET', ''),
        redirectUri: env('GOOGLE_REDIRECT_URI', `http://localhost:${envInt('PORT', 3000)}/auth/google/callback`),
        authUrl: env('GOOGLE_AUTH_URL', 'https://accounts.google.com/o/oauth2/v2/auth'),
        tokenUrl: env('GOOGLE_TOKEN_URL', 'https://oauth2.googleapis.com/token'),
        scopes: ['https://www.googleapis.com/auth/drive.readonly'],
        folderPageSize: envInt('GOOGLE_DRIVE_FOLDER_PAGE_SIZE', 50),
        webhookEnabled: envBool('GOOGLE_DRIVE_WEBHOOK_ENABLED', false),
        webhookUrl: env('GOOGLE_DRIVE_WEBHOOK_URL', `${env('BASE_URL', `http://localhost:${envInt('PORT', 3000)}`)}/api/google-drive/webhook`),
        webhookSecret: env('GOOGLE_DRIVE_WEBHOOK_SECRET', 'change-this-webhook-secret'),
    },
    paths: {
        baseDir: path_1.default.resolve(__dirname, '..', '..'), // Annahme: config.ts ist in src/config
        projectRoot: path_1.default.resolve(__dirname, '..', '..'), // Ähnlich baseDir
        gpxBaseDir: env('GPX_FILES_DIR', path_1.default.join(path_1.default.resolve(__dirname, '..', '..'), 'GPX_files')),
        plannedGpxSubDir: env('PLANNED_GPX_SUBDIR', 'planned'),
        photosBaseDir: env('PHOTOS_BASE_DIR', path_1.default.join(path_1.default.resolve(__dirname, '..', '..'), 'public', 'activity_photos_processed')),
        imageBaseUploadPath: env('IMAGE_UPLOAD_PATH', path_1.default.join(path_1.default.resolve(__dirname, '..', '..'), 'public', 'uploads', 'activity_photos')),
        tempDir: env('TEMP_DIR', path_1.default.join(path_1.default.resolve(__dirname, '..', '..'), 'tmp')),
    },
    logging: {
        level: env('LOG_LEVEL', 'info'),
        logToConsole: envBool('LOG_TO_CONSOLE', true),
        logToFile: envBool('LOG_TO_FILE', false),
        logFilePath: env('LOG_FILE_PATH', path_1.default.join(path_1.default.resolve(__dirname, '..', '..'), 'logs', 'app.log')),
        pm2LogPath: env('PM2_LOG_PATH', path_1.default.join(path_1.default.resolve(__dirname, '..', '..'), 'logs', 'pm2.log')),
        appErrorLogPath: env('APP_ERROR_LOG_PATH', path_1.default.join(path_1.default.resolve(__dirname, '..', '..'), 'logs', 'app-error.log')),
        appOutLogPath: env('APP_OUT_LOG_PATH', path_1.default.join(path_1.default.resolve(__dirname, '..', '..'), 'logs', 'app-out.log')),
        ipLogPath: env('IP_LOG_PATH', path_1.default.join(path_1.default.resolve(__dirname, '..', '..'), 'logs', 'ip-access.log')),
        maxLogLinesAdmin: envInt('MAX_LOG_LINES_ADMIN', 200),
        serviceLogsDir: env('SERVICE_LOGS_DIR', path_1.default.join(path_1.default.resolve(__dirname, '..', '..'), 'logs', 'services')),
        serviceLogPaths: {
            'stravaApiQueueProcessor': env('STRAVA_API_QUEUE_PROCESSOR_LOG_PATH', path_1.default.join(path_1.default.resolve(__dirname, '..', '..'), 'logs', 'services', 'strava-api-queue-processor.log')),
            // Hier können weitere dienstspezifische Log-Pfade hinzugefügt werden
        },
    },
    activities: {
        ignoreTypes: (env('IGNORE_ACTIVITY_TYPES', 'VirtualRide,Workout') || '').split(',').map(type => type.trim().toLowerCase()).filter(type => type.length > 0),
        adminBrowserPageLimit: envInt('ADMIN_ACTIVITIES_PAGE_LIMIT', 25),
        photoMigrationBatchSize: envInt('PHOTO_MIGRATION_BATCH_SIZE', 20),
        tokenValidBufferSeconds: envInt('TOKEN_VALID_BUFFER_SECONDS', 300),
        photoConsistencyBatchSize: envInt('PHOTO_CONSISTENCY_BATCH_SIZE', 50),
        processing: {
            pauseSpeedThresholdMs: envFloat('PROC_PAUSE_SPEED_THRESHOLD_MS', 0.5),
            minPauseDurationSec: envInt('PROC_MIN_PAUSE_DURATION_SEC', 30),
            displayTrackSmoothingWindow: envInt('PROC_DISPLAY_SMOOTH_WINDOW', 5),
            gradientThresholdFlatPositive: envFloat('PROC_GRAD_FLAT_POS', 1.0),
            gradientThresholdFlatNegative: envFloat('PROC_GRAD_FLAT_NEG', -1.0),
            minSectionLengthMeters: envInt('PROC_MIN_SECTION_LENGTH_M', 20),
            mergeShortFlatBetweenDownhillsMeters: envInt('PROC_MERGE_FLAT_DOWNHILL_M', 25),
            finalFilterMinLengthMeters: envInt('PROC_FINAL_FILTER_MIN_LENGTH_M', 100),
            finalFilterMinAvgGradient: envFloat('PROC_FINAL_FILTER_MIN_GRADIENT', -20.0),
            extendSegmentStartMeters: envInt('PROC_EXTEND_SEGMENT_START_M', 200),
            extendSegmentEndMeters: envInt('PROC_EXTEND_SEGMENT_END_M', 200),
            fussTypes: env('ACTIVITY_TYPES_FUSS', 'Run,Walk,Hike,TrailRun,Snowshoe').split(','),
            radTypes: env('ACTIVITY_TYPES_RAD', 'Ride,MountainBikeRide,GravelRide,EBikeRide,VirtualRide').split(','),
        },
        speedThresholds: {
            default: { max_speed_mps: envFloat('DQ_SPEED_DEFAULT_MAX_MPS', 50), average_speed_mps: envFloat('DQ_SPEED_DEFAULT_AVG_MPS', 30) },
            Ride: { max_speed_mps: envFloat('DQ_SPEED_RIDE_MAX_MPS', 45), average_speed_mps: envFloat('DQ_SPEED_RIDE_AVG_MPS', 20) },
            EBikeRide: { max_speed_mps: envFloat('DQ_SPEED_EBIKE_MAX_MPS', 40), average_speed_mps: envFloat('DQ_SPEED_EBIKE_AVG_MPS', 18) },
            Run: { max_speed_mps: envFloat('DQ_SPEED_RUN_MAX_MPS', 12), average_speed_mps: envFloat('DQ_SPEED_RUN_AVG_MPS', 7) },
            Walk: { max_speed_mps: envFloat('DQ_SPEED_WALK_MAX_MPS', 8), average_speed_mps: envFloat('DQ_SPEED_WALK_AVG_MPS', 4) },
            Hike: { max_speed_mps: envFloat('DQ_SPEED_HIKE_MAX_MPS', 8), average_speed_mps: envFloat('DQ_SPEED_HIKE_AVG_MPS', 4) },
            AlpineSki: { max_speed_mps: envFloat('DQ_SPEED_SKIALPINE_MAX_MPS', 55), average_speed_mps: envFloat('DQ_SPEED_SKIALPINE_AVG_MPS', 25) }
        },
    },
    stravaApiLimits: {
        readShortTermLimit: envInt('STRAVA_API_SHORT_TERM_LIMIT', 200), // Ihre höheren Limits
        readShortTermWindowSeconds: envInt('STRAVA_API_SHORT_TERM_WINDOW_SECONDS', 15 * 60),
        readDailyLimit: envInt('STRAVA_API_DAILY_LIMIT', 2000), // Ihre höheren Limits
        readDailyWindowSeconds: envInt('STRAVA_API_DAILY_WINDOW_SECONDS', 24 * 60 * 60),
    },
    maps: {
        mapTilerApiKey: env('MAPTILER_API_KEY', ''),
        thunderforestApiKey: env('THUNDERFOREST_API_KEY', ''),
        baseLayers: [
            { name: "Streets", url: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', options: { maxZoom: 24, maxNativeZoom: 19, attribution: '&copy; OpenStreetMap' }, active: true },
            { name: "Terrain", url: `https://{s}.tile.thunderforest.com/landscape/{z}/{x}/{y}.png?apikey=${env('THUNDERFOREST_API_KEY')}`, options: { maxZoom: 24, maxNativeZoom: 22, attribution: '&copy; Thunderforest' } },
            { name: "Satellite", url: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', options: { maxZoom: 24, maxNativeZoom: 18, attribution: '&copy; Esri' } },
            { name: "Topo", url: 'https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png', options: { maxZoom: 24, maxNativeZoom: 17, attribution: '&copy; OpenTopoMap' } },
            { name: "Cycle", url: `https://{s}.tile.thunderforest.com/cycle/{z}/{x}/{y}.png?apikey=${env('THUNDERFOREST_API_KEY')}`, options: { maxZoom: 24, maxNativeZoom: 22, attribution: '&copy; Thunderforest' } }
        ],
        defaultMapOptions: {
            zoom: envInt('MAP_DEFAULT_ZOOM', 12),
            center: [envFloat('MAP_DEFAULT_CENTER_LAT', 48.1114), envFloat('MAP_DEFAULT_CENTER_LON', 8.5058)],
            attributionControl: false,
            tapTolerance: 40
        },
        styleColors: {
            activityTypes: {
                Hike: "#ff0000", Walk: "#0000ff", Ride: "#ffa500", Run: "#ff00ff",
                EBikeRide: "#E69100", MountainBikeRide: "#cc8400", VirtualRide: "#6a0dad", Swim: "#00ffff",
                AlpineSki: "#00ced1", Snowshoe: "#fff8dc", Kayaking: "#4682b4", Rowing: "#4682b4",
                StandUpPaddling: "#4682b4", Windsurf: "#1e90ff", Workout: "#32cd32", WeightTraining: "#228b22",
                IceSkate: "#add8e6", InlineSkate: "#ff69b4", Default: "#808080"
            },
            plannedRoute: '#3388ff',
            piTrack: { casing: env('PI_TRACK_CASING_COLOR', '#000000'), mainLine: env('PI_TRACK_MAIN_LINE_COLOR', '#FF0000') },
            debugTracks: { originalRaw: '#ff0000', processedDisplay: '#0000ff', downhillSegment: '#008000' },
            elevationAnnotations: { enterDownhillLine: 'rgba(255, 20, 147, 0.8)', exitDownhillLine: 'rgba(30, 144, 255, 0.8)', downhillSegmentBoxBg: 'rgba(0, 128, 0, 0.15)', downhillSegmentBoxBorder: 'rgba(0, 100, 0, 0.3)' },
            anchorMarkers: { start: 'lime', enterDownhill: '#FF1493', exitDownhill: '#1E90FF', distance: 'blue', angle: 'orange', distanceAndAngle: 'purple', end: 'red', unknown: 'grey' }
        },
        geojsonStyleDefault: { weight: envInt('GEOJSON_DEFAULT_WEIGHT', 3), opacity: envFloat('GEOJSON_DEFAULT_OPACITY', 0.7) },
        plannedRouteStyle: { weight: envInt('PLANNED_ROUTE_WEIGHT', 4), opacity: envFloat('PLANNED_ROUTE_OPACITY', 0.8), dashArray: env('PLANNED_ROUTE_DASHARRAY', '8, 8') },
        photoBaseUrl: env('MAPS_PHOTO_BASE_URL', '/uploads/activity_photos'),
        piControlView: {
            desiredTerrainExaggeration: envFloat('MAP_TERRAIN_EXAGGERATION', 1.5),
            infoUpdateIntervalMs: envInt('PI_INFO_UPDATE_INTERVAL_MS', 250),
            defaultSpeedFactor: envFloat('PI_DEFAULT_SPEED_FACTOR', 300),
            maxSpeedFactor: envFloat('PI_MAX_SPEED_FACTOR', 1000),
            minSpeedFactor: envFloat('PI_MIN_SPEED_FACTOR', 50),
            trackCasingColor: env('PI_TRACK_CASING_COLOR', '#000000'),
            trackCasingWidth: envInt('PI_TRACK_CASING_WIDTH', 7),
            trackMainLineColor: env('PI_TRACK_MAIN_LINE_COLOR', '#FF0000'),
            trackMainLineWidth: envInt('PI_TRACK_MAIN_LINE_WIDTH', 3),
            trackCasingOpacity: envFloat('PI_TRACK_CASING_OPACITY', 0.8),
            lookAheadDistanceMeters: envFloat('PI_LOOK_AHEAD_METERS', 75),
            targetSpeedMetersPerSecond: envFloat('PI_TARGET_SPEED_MPS', 50),
            pauseProximityThresholdMeters: envFloat('PI_PAUSE_PROXIMITY_METERS', 10),
            autoPauseDurationMs: envInt('PI_AUTO_PAUSE_DURATION_MS', 3000),
            pauseConsolidationDistanceMeters: envFloat('PI_PAUSE_CONSOLIDATION_METERS', 25),
            piControllerPosKp: envFloat('PI_POS_KP', 5),
            piControllerPosKi: envFloat('PI_POS_KI', 0.05),
            piControllerPosIntegralLimit: envFloat('PI_POS_INTEGRAL_LIMIT', 0.01),
            piControllerBearingKp: envFloat('PI_BEARING_KP', 0.5),
            piControllerBearingKi: envFloat('PI_BEARING_KI', 0.0),
            piControllerBearingIntegralLimit: envFloat('PI_BEARING_INTEGRAL_LIMIT', 90),
            speedSliderMin: envInt('SPEED_SLIDER_MIN', 50),
            speedSliderMax: envInt('SPEED_SLIDER_MAX', 1000),
            speedSliderMinSpeedMps: envFloat('SPEED_SLIDER_MIN_SPEED_MPS', 5),
            speedSliderMaxSpeedMps: envFloat('SPEED_SLIDER_MAX_SPEED_MPS', 100)
        }
    },
    images: {
        processPhotos: envBool('PROCESS_PHOTOS', true), // Von mir
        photoDownloadSize: env('PHOTO_DOWNLOAD_SIZE', 'd'), // Von mir (für Google)
        photoDisplaySizes: {
            small: envInt('PHOTO_DISPLAY_SIZE_SMALL_PX', 300),
            medium: envInt('PHOTO_DISPLAY_SIZE_MEDIUM_PX', 800)
        },
        photoQuality: envInt('PHOTO_QUALITY_JPEG', 80), // Von mir
        // Ihre spezifischen Image-Größen-Details:
        sizes: {
            small: { width: envInt('IMAGE_SIZE_SMALL_WIDTH', 320), quality: envInt('IMAGE_SIZE_SMALL_QUALITY', 80), suffix: env('IMAGE_SIZE_SMALL_SUFFIX', '_s') },
            medium: { width: envInt('IMAGE_SIZE_MEDIUM_WIDTH', 1024), quality: envInt('IMAGE_SIZE_MEDIUM_QUALITY', 85), suffix: env('IMAGE_SIZE_MEDIUM_SUFFIX', '_m') },
            original: { quality: envInt('IMAGE_SIZE_ORIGINAL_QUALITY', 90), suffix: env('IMAGE_SIZE_ORIGINAL_SUFFIX', '_o') } // Original hat typ. keine Breite
        },
        defaultFormat: env('IMAGE_DEFAULT_FORMAT', 'jpeg'),
        maxUploadFileSizeMb: envInt('IMAGE_MAX_UPLOAD_MB', 50)
    },
    gpxAuthorName: env('GPX_AUTHOR_NAME', appNameFromEnv),
    komoot: {
        enabled: envBool('KOMOOT_ENABLED', true),
        komootGpxPath: env('KOMOOT_GPX_PATH', '/home/<USER>/.local/bin/komootgpx'),
        gpxUploadDir: env('KOMOOT_GPX_UPLOAD_DIR', path_1.default.join(path_1.default.resolve(__dirname, '..', '..'), 'GPX_files')),
    },
    garmin: {
        enabled: envBool('GARMIN_ENABLED', true),
        garminBridgePath: env('GARMIN_BRIDGE_PATH', '/home/<USER>/python_envs/garmin/garmin_wrapper.sh'),
        gpxUploadDir: env('GARMIN_GPX_UPLOAD_DIR', path_1.default.join(path_1.default.resolve(__dirname, '..', '..'), 'GPX_files')),
    },
};
exports.default = appConfig;
//# sourceMappingURL=config.js.map