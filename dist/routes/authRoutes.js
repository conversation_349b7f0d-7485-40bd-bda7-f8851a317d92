"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
// src/routes/authRoutes.ts
const express_1 = __importDefault(require("express"));
const authController = __importStar(require("../controllers/authController"));
const googleDriveAuthController_1 = require("../controllers/googleDriveAuthController"); // Nur disconnect benötigt
const googlePhotosPickerAuthController = __importStar(require("../controllers/googlePhotosPickerAuthController"));
const requireUserLogin_1 = require("../middleware/requireUserLogin");
const logger_1 = __importDefault(require("../utils/logger"));
const router = express_1.default.Router();
const log = logger_1.default.getLogger(__filename);
log.info('Authentication routes initializing...');
// Login-Seite anzeigen
router.get('/login', authController.showLoginPage);
// Login-Anfrage verarbeiten (verwendet Passport 'local' Strategie)
router.post('/login', authController.handleLogin);
// Logout-Anfrage verarbeiten
router.get('/logout', authController.logout); // Korrekter Funktionsname
// Strava Authentifizierung
router.get('/strava/login', requireUserLogin_1.requireUserLogin, authController.requestStravaAuthorization);
router.get('/strava/callback', requireUserLogin_1.requireUserLogin, authController.handleStravaCallback); // requireUserLogin hier, da der User Tokens für seinen Account bekommt
// Google Photos Authentifizierung
router.get('/google/login', requireUserLogin_1.requireUserLogin, authController.requestGooglePhotosAuthorization);
router.get('/google/callback', requireUserLogin_1.requireUserLogin, authController.handleGooglePhotosCallback); // requireUserLogin hier
router.get('/google/disconnect', requireUserLogin_1.requireUserLogin, authController.disconnectGooglePhotos);
// Google Drive Authentifizierung
router.get('/google-drive/login', requireUserLogin_1.requireUserLogin, authController.requestGoogleDriveAuthorization);
router.get('/google-drive/disconnect', requireUserLogin_1.requireUserLogin, googleDriveAuthController_1.disconnectGoogleDrive);
// === Google Photos Picker OAuth ===
router.get('/google-photos-picker', requireUserLogin_1.requireUserLogin, googlePhotosPickerAuthController.startGooglePhotosPickerAuth);
// Callback wird über /auth/google/callback mit State-Parameter behandelt
router.post('/google-photos-picker/disconnect', requireUserLogin_1.requireUserLogin, googlePhotosPickerAuthController.disconnectGooglePhotosPicker);
router.get('/google-photos-picker/status', requireUserLogin_1.requireUserLogin, googlePhotosPickerAuthController.getGooglePhotosPickerStatus);
// Beispiel für eine Registrierungsroute (falls Sie sie später hinzufügen)
// router.get('/register', authController.showRegisterPage);
// router.post('/register', authController.handleRegister);
exports.default = router;
//# sourceMappingURL=authRoutes.js.map