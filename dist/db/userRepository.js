"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getFullUserDetailsByUsername = getFullUserDetailsByUsername;
exports.updateUserProfile = updateUserProfile;
exports.findUserByIdForSession = findUserByIdForSession;
exports.getFullUserDetailsById = getFullUserDetailsById;
exports.updateUserNotificationSettings = updateUserNotificationSettings;
exports.updateUserMapDefaults = updateUserMapDefaults;
exports.findUserByUsername = findUserByUsername;
exports.getUserByStravaAthleteId = getUserByStravaAthleteId;
exports.updateUserMapVisibility = updateUserMapVisibility;
exports.createUser = createUser;
exports.findAllUsers = findAllUsers;
exports.deleteUser = deleteUser;
exports.updateUserStravaTokens = updateUserStravaTokens;
exports.getUserStravaTokens = getUserStravaTokens;
exports.updateUserGoogleTokens = updateUserGoogleTokens;
exports.getUserGoogleTokens = getUserGoogleTokens;
exports.clearUserGoogleTokens = clearUserGoogleTokens;
exports.updateUserGoogleDriveTokens = updateUserGoogleDriveTokens;
exports.getUserGoogleDriveTokens = getUserGoogleDriveTokens;
exports.clearUserGoogleDriveTokens = clearUserGoogleDriveTokens;
exports.updateUserGooglePhotosPickerTokens = updateUserGooglePhotosPickerTokens;
exports.getUserGooglePhotosPickerTokens = getUserGooglePhotosPickerTokens;
exports.clearUserGooglePhotosPickerTokens = clearUserGooglePhotosPickerTokens;
exports.updateUserGoogleDriveFolder = updateUserGoogleDriveFolder;
exports.executeQuery = executeQuery;
// src/db/userRepository.ts
const connection_1 = __importDefault(require("./connection"));
const logger_1 = __importDefault(require("../utils/logger"));
const bcrypt_1 = __importDefault(require("bcrypt"));
const log = logger_1.default.getLogger(__filename);
const saltRounds = 12;
/**
 * Findet einen Benutzer anhand seines Benutzernamens und gibt alle Felder zurück (inkl. Tokens und Einstellungen).
 * @param {string} username - Der zu suchende Benutzername.
 * @returns {Promise<User|null>} Ein Promise, das das vollständige Benutzerobjekt
 * oder null zurückgibt, wenn kein Benutzer gefunden wurde.
 */
async function getFullUserDetailsByUsername(username) {
    const fnLogPrefix = `[UserRepo GetFullDetails:${username}]`;
    // Wichtig: Selektieren Sie hier ALLE Spalten, die im User-Typ definiert sind
    // und für die Einstellungsseite benötigt werden.
    const sql = `
        SELECT id, username, role, email, created_at,
               strava_access_token, strava_refresh_token, strava_expires_at, strava_scope, strava_athlete_id,
               google_access_token, google_refresh_token, google_expires_at, google_scope,
               google_drive_access_token, google_drive_refresh_token, google_drive_expires_at, google_drive_scope, google_drive_folder_id,
               komoot_connected, garmin_connected,
               map_visibility, default_map_center_lat, default_map_center_lng, default_map_zoom,
               notify_on_friend_request_email, notify_on_activity_shared_email
        FROM users
        WHERE username = ?
        LIMIT 1`;
    try {
        const [rows] = await connection_1.default.query(sql, [username]);
        if (rows.length > 0) {
            const dbUser = rows[0];
            log.debug(`${fnLogPrefix} Full user details found.`);
            return {
                id: dbUser.id,
                username: dbUser.username,
                role: dbUser.role,
                email: dbUser.email, // NEU
                created_at: dbUser.created_at ? new Date(dbUser.created_at) : undefined,
                // ... (Token-Felder)
                map_visibility: dbUser.map_visibility,
                default_map_center_lat: dbUser.default_map_center_lat !== null ? parseFloat(dbUser.default_map_center_lat) : null,
                default_map_center_lng: dbUser.default_map_center_lng !== null ? parseFloat(dbUser.default_map_center_lng) : null,
                default_map_zoom: dbUser.default_map_zoom !== null ? parseInt(dbUser.default_map_zoom, 10) : null,
                notify_on_friend_request_email: !!dbUser.notify_on_friend_request_email, // NEU (konvertiere zu boolean)
                notify_on_activity_shared_email: !!dbUser.notify_on_activity_shared_email, // NEU
                strava_access_token: dbUser.strava_access_token, /* ... etc ... */
                strava_refresh_token: dbUser.strava_refresh_token,
                strava_expires_at: dbUser.strava_expires_at,
                strava_scope: dbUser.strava_scope,
                strava_athlete_id: dbUser.strava_athlete_id,
                google_access_token: dbUser.google_access_token,
                google_refresh_token: dbUser.google_refresh_token,
                google_expires_at: dbUser.google_expires_at,
                google_scope: dbUser.google_scope,
                google_drive_access_token: dbUser.google_drive_access_token,
                google_drive_refresh_token: dbUser.google_drive_refresh_token,
                google_drive_expires_at: dbUser.google_drive_expires_at,
                google_drive_scope: dbUser.google_drive_scope,
                google_drive_folder_id: dbUser.google_drive_folder_id,
                komoot_connected: !!dbUser.komoot_connected, // NEU (konvertiere zu boolean)
                garmin_connected: !!dbUser.garmin_connected, // NEU (konvertiere zu boolean)
            };
        }
        else {
            log.debug(`${fnLogPrefix} User not found.`);
            return null;
        }
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error fetching full user details:`, error);
        throw new Error('Database error while fetching full user details.');
    }
}
async function updateUserProfile(userId, data) {
    const fnLogPrefix = `[UserRepo UpdateProfile User:${userId}]`;
    if (data.email === undefined)
        return false; // Nichts zu aktualisieren
    // Validierung der E-Mail-Adresse serverseitig ist wichtig (z.B. mit einer Bibliothek oder Regex)
    // Hier nur eine einfache Prüfung auf Format, richtige Validierung ist komplexer.
    if (data.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
        throw new Error("Ungültiges E-Mail Format.");
    }
    // Prüfen, ob die E-Mail bereits von einem anderen User verwendet wird
    if (data.email) {
        const emailCheckSql = "SELECT id FROM users WHERE email = ? AND id != ?";
        const [existingUsers] = await connection_1.default.query(emailCheckSql, [data.email, userId]);
        if (existingUsers.length > 0) {
            throw new Error("Diese E-Mail-Adresse wird bereits von einem anderen Benutzer verwendet.");
        }
    }
    const sql = "UPDATE users SET email = ? WHERE id = ?";
    try {
        const [result] = await connection_1.default.execute(sql, [data.email || null, userId]);
        log.info(`${fnLogPrefix} User email updated.`);
        return result.affectedRows > 0;
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error updating user email:`, error);
        throw error;
    }
}
// Stellen Sie sicher, dass findUserByIdForSession alle relevanten Felder für req.user lädt,
// inklusive der Token-Felder, damit die Einstellungsseite den korrekten Verbindungsstatus anzeigen kann.
async function findUserByIdForSession(id) {
    const fnLogPrefix = `[UserRepo FindByIdForSession ID:${id}]`;
    const sql = `
        SELECT
            id, username, role, email, created_at,
            map_visibility, default_map_center_lat, default_map_center_lng, default_map_zoom,
            notify_on_friend_request_email, notify_on_activity_shared_email,
            strava_access_token, strava_refresh_token, strava_expires_at, strava_scope, strava_athlete_id,
            google_access_token, google_refresh_token, google_expires_at, google_scope,
            google_drive_access_token, google_drive_refresh_token, google_drive_expires_at, google_drive_scope, google_drive_folder_id,
            komoot_connected, garmin_connected
        FROM users
        WHERE id = ?
        LIMIT 1`;
    try {
        const [rows] = await connection_1.default.query(sql, [id]);
        if (rows.length > 0) {
            const dbUser = rows[0];
            log.debug(`${fnLogPrefix} User found, mapping to Express.User object.`);
            const userForSession = {
                id: dbUser.id,
                username: dbUser.username,
                role: dbUser.role,
                email: dbUser.email,
                map_visibility: dbUser.map_visibility,
                default_map_center_lat: dbUser.default_map_center_lat !== null ? parseFloat(dbUser.default_map_center_lat) : null,
                default_map_center_lng: dbUser.default_map_center_lng !== null ? parseFloat(dbUser.default_map_center_lng) : null,
                default_map_zoom: dbUser.default_map_zoom !== null ? parseInt(dbUser.default_map_zoom, 10) : null,
                notify_on_friend_request_email: !!dbUser.notify_on_friend_request_email,
                notify_on_activity_shared_email: !!dbUser.notify_on_activity_shared_email,
                strava_access_token: dbUser.strava_access_token,
                strava_refresh_token: dbUser.strava_refresh_token,
                strava_expires_at: dbUser.strava_expires_at,
                strava_scope: dbUser.strava_scope,
                strava_athlete_id: dbUser.strava_athlete_id,
                google_access_token: dbUser.google_access_token,
                google_refresh_token: dbUser.google_refresh_token,
                google_expires_at: dbUser.google_expires_at,
                google_scope: dbUser.google_scope,
                google_drive_access_token: dbUser.google_drive_access_token,
                google_drive_refresh_token: dbUser.google_drive_refresh_token,
                google_drive_expires_at: dbUser.google_drive_expires_at,
                google_drive_scope: dbUser.google_drive_scope,
                google_drive_folder_id: dbUser.google_drive_folder_id,
                komoot_connected: !!dbUser.komoot_connected,
                garmin_connected: !!dbUser.garmin_connected,
            };
            return userForSession;
        }
        log.warn(`${fnLogPrefix} User not found.`);
        return null;
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error fetching user for session by ID:`, error);
        throw error;
    }
}
// src/db/userRepository.ts
async function getFullUserDetailsById(userId) {
    const fnLogPrefix = `[UserRepo GetFullDetailsByID:${userId}]`;
    // SQL-Query, die alle Felder für den User-Typ selektiert (inkl. email, notify_... Flags)
    const sql = `
        SELECT id, username, role, email, created_at,
               strava_access_token, strava_refresh_token, strava_expires_at, strava_scope, strava_athlete_id,
               google_access_token, google_refresh_token, google_expires_at, google_scope,
               google_drive_access_token, google_drive_refresh_token, google_drive_expires_at, google_drive_scope, google_drive_folder_id,
               komoot_connected, garmin_connected,
               map_visibility, default_map_center_lat, default_map_center_lng, default_map_zoom,
               notify_on_friend_request_email, notify_on_activity_shared_email
        FROM users
        WHERE id = ?
        LIMIT 1`;
    try {
        const [rows] = await connection_1.default.query(sql, [userId]);
        if (rows.length > 0) {
            const dbUser = rows[0];
            // Mapping zum User-Objekt (ähnlich wie in getFullUserDetailsByUsername)
            return {
                id: dbUser.id,
                username: dbUser.username,
                role: dbUser.role,
                email: dbUser.email,
                created_at: dbUser.created_at ? new Date(dbUser.created_at) : undefined,
                map_visibility: dbUser.map_visibility,
                default_map_center_lat: dbUser.default_map_center_lat !== null ? parseFloat(dbUser.default_map_center_lat) : null,
                default_map_center_lng: dbUser.default_map_center_lng !== null ? parseFloat(dbUser.default_map_center_lng) : null,
                default_map_zoom: dbUser.default_map_zoom !== null ? parseInt(dbUser.default_map_zoom, 10) : null,
                notify_on_friend_request_email: !!dbUser.notify_on_friend_request_email,
                notify_on_activity_shared_email: !!dbUser.notify_on_activity_shared_email,
                strava_access_token: dbUser.strava_access_token,
                strava_refresh_token: dbUser.strava_refresh_token,
                strava_expires_at: dbUser.strava_expires_at,
                strava_scope: dbUser.strava_scope,
                strava_athlete_id: dbUser.strava_athlete_id,
                google_access_token: dbUser.google_access_token,
                google_refresh_token: dbUser.google_refresh_token,
                google_expires_at: dbUser.google_expires_at,
                google_scope: dbUser.google_scope,
                google_drive_access_token: dbUser.google_drive_access_token,
                google_drive_refresh_token: dbUser.google_drive_refresh_token,
                google_drive_expires_at: dbUser.google_drive_expires_at,
                google_drive_scope: dbUser.google_drive_scope,
                google_drive_folder_id: dbUser.google_drive_folder_id,
                komoot_connected: !!dbUser.komoot_connected,
                garmin_connected: !!dbUser.garmin_connected,
            }; // Sicherstellen, dass alle User-Felder gemappt werden
        }
        return null;
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error fetching full user details by ID:`, error);
        throw error;
    }
}
async function updateUserNotificationSettings(userId, settings) {
    const fnLogPrefix = `[UserRepo UpdateNotifSettings User:${userId}]`;
    log.info(`${fnLogPrefix} Updating notification settings.`);
    const fieldsToUpdate = [];
    const values = [];
    if (settings.notify_on_friend_request_email !== undefined) {
        fieldsToUpdate.push("notify_on_friend_request_email = ?");
        values.push(settings.notify_on_friend_request_email);
    }
    if (settings.notify_on_activity_shared_email !== undefined) {
        fieldsToUpdate.push("notify_on_activity_shared_email = ?");
        values.push(settings.notify_on_activity_shared_email);
    }
    // Fügen Sie hier weitere Einstellungen hinzu
    if (fieldsToUpdate.length === 0) {
        log.info(`${fnLogPrefix} No notification settings to update.`);
        return false;
    }
    values.push(userId); // Für die WHERE-Klausel
    const sql = `UPDATE users SET ${fieldsToUpdate.join(', ')} WHERE id = ?`;
    try {
        const [result] = await connection_1.default.execute(sql, values);
        return result.affectedRows > 0;
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error updating notification settings:`, error);
        throw error;
    }
}
async function updateUserMapDefaults(userId, defaults) {
    const fnLogPrefix = `[UserRepo UpdateMapDefaults User:${userId}]`;
    log.info(`${fnLogPrefix} Updating map defaults.`);
    const sql = `
        UPDATE users SET
            default_map_zoom = ?,
            default_map_center_lat = ?,
            default_map_center_lng = ?
        WHERE id = ?
    `;
    try {
        const [result] = await connection_1.default.execute(sql, [
            defaults.default_map_zoom,
            defaults.default_map_center_lat,
            defaults.default_map_center_lng,
            userId
        ]);
        return result.affectedRows > 0;
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error updating map defaults:`, error);
        throw new Error('Database error while updating user map defaults.');
    }
}
/**
 * Findet einen Benutzer anhand seines Benutzernamens in der Datenbank.
 * @param {string} username - Der zu suchende Benutzername.
 * @returns {Promise<object|null>} Ein Promise, das das Benutzerobjekt (mit id, username, password_hash, role)
 * oder null zurückgibt, wenn kein Benutzer gefunden wurde.
 */
async function findUserByUsername(username) {
    const sql = 'SELECT id, username, password_hash, role, created_at FROM users WHERE username = ? LIMIT 1';
    try {
        const [rows] = await connection_1.default.query(sql, [username]); // Typisierung für RowDataPacket[] wäre besser
        if (rows.length > 0) {
            log.debug(`Benutzer gefunden: ${username}`);
            return rows[0];
        }
        else {
            log.debug(`Benutzer nicht gefunden: ${username}`);
            return null;
        }
    }
    catch (error) {
        log.error(`Fehler beim Suchen des Benutzers ${username}:`, error);
        throw new Error('Database error while finding user by username.');
    }
}
async function getUserByStravaAthleteId(stravaAthleteId) {
    const fnLogPrefix = `[UserRepo getUserByStravaAthleteId:${stravaAthleteId}]`;
    // SQL-Query, die alle Felder für den User-Typ selektiert (inkl. email, notify_... Flags)
    const sql = `
        SELECT id, username, role, email, created_at,
               strava_access_token, strava_refresh_token, strava_expires_at, strava_scope, strava_athlete_id,
               google_access_token, google_refresh_token, google_expires_at, google_scope,
               google_drive_access_token, google_drive_refresh_token, google_drive_expires_at, google_drive_scope, google_drive_folder_id,
               komoot_connected, garmin_connected,
               map_visibility, default_map_center_lat, default_map_center_lng, default_map_zoom,
               notify_on_friend_request_email, notify_on_activity_shared_email
        FROM users
        WHERE strava_athlete_id = ?
        LIMIT 1`;
    try {
        const [rows] = await connection_1.default.query(sql, [stravaAthleteId]);
        if (rows.length > 0) {
            const dbUser = rows[0];
            // Mapping zum User-Objekt (ähnlich wie in getFullUserDetailsByUsername)
            return {
                id: dbUser.id,
                username: dbUser.username,
                role: dbUser.role,
                email: dbUser.email,
                created_at: dbUser.created_at ? new Date(dbUser.created_at) : undefined,
                map_visibility: dbUser.map_visibility,
                default_map_center_lat: dbUser.default_map_center_lat !== null ? parseFloat(dbUser.default_map_center_lat) : null,
                default_map_center_lng: dbUser.default_map_center_lng !== null ? parseFloat(dbUser.default_map_center_lng) : null,
                default_map_zoom: dbUser.default_map_zoom !== null ? parseInt(dbUser.default_map_zoom, 10) : null,
                notify_on_friend_request_email: !!dbUser.notify_on_friend_request_email,
                notify_on_activity_shared_email: !!dbUser.notify_on_activity_shared_email,
                strava_access_token: dbUser.strava_access_token,
                strava_refresh_token: dbUser.strava_refresh_token,
                strava_expires_at: dbUser.strava_expires_at,
                strava_scope: dbUser.strava_scope,
                strava_athlete_id: dbUser.strava_athlete_id,
                google_access_token: dbUser.google_access_token,
                google_refresh_token: dbUser.google_refresh_token,
                google_expires_at: dbUser.google_expires_at,
                google_scope: dbUser.google_scope,
                google_drive_access_token: dbUser.google_drive_access_token,
                google_drive_refresh_token: dbUser.google_drive_refresh_token,
                google_drive_expires_at: dbUser.google_drive_expires_at,
                google_drive_scope: dbUser.google_drive_scope,
                google_drive_folder_id: dbUser.google_drive_folder_id,
                komoot_connected: !!dbUser.komoot_connected,
                garmin_connected: !!dbUser.garmin_connected,
            }; // Sicherstellen, dass alle User-Felder gemappt werden
        }
        return null;
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error fetching full user details by ID:`, error);
        throw error;
    }
}
async function updateUserMapVisibility(userId, visibility) {
    const fnLogPrefix = `[UserRepo UpdateMapVisibility User:${userId}]`;
    log.info(`${fnLogPrefix} Setting map_visibility to ${visibility}.`);
    const sql = 'UPDATE users SET map_visibility = ? WHERE id = ?';
    try {
        const [result] = await connection_1.default.execute(sql, [visibility, userId]);
        return result.affectedRows > 0;
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error updating map_visibility:`, error);
        throw new Error('Database error while updating map visibility.');
    }
}
/**
 * Erstellt einen neuen Benutzer in der Datenbank.
 * @param {string} username - Der Benutzername.
 * @param {string} password - Das Klartext-Passwort.
 * @param {'admin'|'user'} role - Die Rolle des Benutzers ('admin' oder 'user').
 * @returns {Promise<number|null>} Die ID des neu erstellten Benutzers oder null bei Fehler/Dublette.
 */
async function createUser(username, passwordPlainText, role) {
    if (!username || !passwordPlainText || !role || (role !== 'admin' && role !== 'user')) {
        log.error('createUser: Ungültige Eingabedaten.', { username, role });
        throw new Error('Ungültige Eingabedaten zum Erstellen des Benutzers.');
    }
    if (passwordPlainText.length < 8) {
        throw new Error('Passwort muss mindestens 8 Zeichen lang sein.');
    }
    try {
        const hashedPassword = await bcrypt_1.default.hash(passwordPlainText, saltRounds);
        log.info(`Hashing password for new user: ${username}`);
        const sql = 'INSERT INTO users (username, password_hash, role) VALUES (?, ?, ?)';
        log.info(`Attempting to insert new user '${username}' with role '${role}'...`);
        const [result] = await connection_1.default.query(sql, [username, hashedPassword, role]);
        if (result.affectedRows > 0 && result.insertId) {
            log.info(`User '${username}' created successfully with ID: ${result.insertId}`);
            return result.insertId;
        }
        else {
            log.warn(`User '${username}' could not be inserted, though no error was thrown.`);
            return null;
        }
    }
    catch (error) {
        if (error.code === 'ER_DUP_ENTRY') {
            log.warn(`createUser: Benutzername '${username}' existiert bereits.`);
            throw new Error(`Benutzername '${username}' existiert bereits.`);
        }
        log.error(`Fehler beim Erstellen des Benutzers '${username}':`, error);
        throw new Error('Datenbankfehler beim Erstellen des Benutzers.');
    }
}
/**
 * Ruft alle Benutzer aus der Datenbank ab (ohne Passwort-Hash).
 * @returns {Promise<Array<object>>} Ein Promise, das ein Array von Benutzerobjekten zurückgibt.
 */
async function findAllUsers() {
    const sql = 'SELECT id, username, role, created_at FROM users ORDER BY username ASC';
    try {
        const [rows] = await connection_1.default.query(sql); // Typisiere das Ergebnis von pool.query
        log.debug(`Found ${rows.length} users.`);
        return rows.map((row) => ({
            id: row.id,
            username: row.username,
            role: row.role,
            created_at: new Date(row.created_at) // In Date-Objekt umwandeln
        }));
    }
    catch (error) {
        log.error('Fehler beim Abrufen aller Benutzer:', error);
        throw new Error('Datenbankfehler beim Abrufen der Benutzerliste.');
    }
}
/**
 * Löscht einen Benutzer anhand seiner ID.
 * @param {number} id - Die ID des zu löschenden Benutzers.
 * @returns {Promise<boolean>} True bei Erfolg, false wenn kein User gelöscht wurde.
 */
async function deleteUser(id) {
    const sql = 'DELETE FROM users WHERE id = ?';
    try {
        const [result] = await connection_1.default.query(sql, [id]);
        if (result.affectedRows > 0) {
            log.info(`User with ID ${id} deleted successfully.`);
            return true;
        }
        else {
            log.warn(`Attempted to delete user with ID ${id}, but user not found.`);
            return false;
        }
    }
    catch (error) {
        log.error(`Fehler beim Löschen des Benutzers mit ID ${id}:`, error);
        throw new Error('Datenbankfehler beim Löschen des Benutzers.');
    }
}
// updateUserStravaTokens MUSS den importierten Typ verwenden
async function updateUserStravaTokens(userId, tokens) {
    const fnLogPrefix = `[UserRepo StravaTokens User:${userId}]`;
    log.info(`${fnLogPrefix} Updating Strava tokens.`);
    const sql = `
        UPDATE users SET
            strava_access_token = ?,
            strava_refresh_token = ?,
            strava_expires_at = ?,
            strava_scope = ?,
            strava_athlete_id = ?
        WHERE id = ?
    `;
    try {
        // Die Werte aus tokens können jetzt null sein und werden so an die DB übergeben
        // Deine DB-Spalten MÜSSEN NULL erlauben für diese Token-Felder
        const [result] = await connection_1.default.execute(sql, [
            tokens.accessToken, // string | null
            tokens.refreshToken, // string | null
            tokens.expiresAt, // number | null
            tokens.scope || null, // string | undefined | null -> string | null
            tokens.athleteId || null, // string | undefined | null -> string | null
            userId
        ]);
        log.info(`${fnLogPrefix} Strava tokens updated. Affected rows: ${result.affectedRows}`);
        return result.affectedRows > 0;
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error updating Strava tokens:`, error);
        throw new Error('Database error while updating user Strava tokens.');
    }
}
// NEU: Funktion zum Abrufen von User-spezifischen Strava Tokens
async function getUserStravaTokens(userId) {
    const sql = "SELECT strava_access_token, strava_refresh_token, strava_expires_at, strava_scope, strava_athlete_id FROM users WHERE id = ?";
    const [rows] = await connection_1.default.query(sql, [userId]);
    if (rows.length > 0) {
        return {
            strava_access_token: rows[0].strava_access_token,
            strava_refresh_token: rows[0].strava_refresh_token,
            strava_expires_at: rows[0].strava_expires_at,
            strava_scope: rows[0].strava_scope,
            strava_athlete_id: rows[0].strava_athlete_id,
        };
    }
    return null;
}
/**
 * Aktualisiert die Google Tokens eines Benutzers. Akzeptiert partielle Daten.
 */
async function updateUserGoogleTokens(userId, tokenData) {
    const fnLogPrefix = `[UserRepo UpdateGoogleTokens User:${userId}]`;
    log.debug(`${fnLogPrefix} Updating Google tokens with data:`, tokenData);
    const fieldsToUpdate = [];
    const values = [];
    // Nur Felder hinzufügen, die auch wirklich in tokenData vorhanden sind
    if (tokenData.accessToken !== undefined) {
        fieldsToUpdate.push("google_access_token = ?");
        values.push(tokenData.accessToken); // Kann null sein, um zu löschen
    }
    if (tokenData.refreshToken !== undefined) {
        fieldsToUpdate.push("google_refresh_token = ?");
        values.push(tokenData.refreshToken); // Kann null sein
    }
    if (tokenData.expiresAt !== undefined) { // expiresAt ist number | null
        fieldsToUpdate.push("google_expires_at = ?");
        values.push(tokenData.expiresAt);
    }
    if (tokenData.scope !== undefined) {
        fieldsToUpdate.push("google_scope = ?");
        values.push(tokenData.scope); // Kann null sein
    }
    if (fieldsToUpdate.length === 0) {
        log.info(`${fnLogPrefix} No Google token fields to update for user ${userId}.`);
        return false; // Keine Änderungen an die DB gesendet, aber kein Fehler
    }
    values.push(userId); // Für die WHERE-Klausel
    const sql = `UPDATE users SET ${fieldsToUpdate.join(', ')} WHERE id = ?`;
    log.debug(`${fnLogPrefix} SQL: ${sql}`);
    log.debug(`${fnLogPrefix} Values: ${JSON.stringify(values)}`);
    try {
        const [result] = await connection_1.default.execute(sql, values);
        log.info(`${fnLogPrefix} Google tokens updated for user ${userId}, affected rows: ${result.affectedRows}`);
        return result.affectedRows > 0;
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error updating Google tokens for user ${userId}:`, error);
        throw error; // Fehler weiterwerfen für zentrale Behandlung
    }
}
/**
 * Ruft die Google Tokens für einen Benutzer ab.
 */
async function getUserGoogleTokens(userId) {
    const sql = "SELECT google_access_token, google_refresh_token, google_expires_at, google_scope FROM users WHERE id = ?";
    const [rows] = await connection_1.default.query(sql, [userId]);
    if (rows.length > 0 && rows[0].google_access_token) { // Prüfen, ob Token vorhanden ist
        return {
            accessToken: rows[0].google_access_token,
            refreshToken: rows[0].google_refresh_token,
            expiresAt: rows[0].google_expires_at, // Ist BIGINT in DB, wird als number gelesen
            scope: rows[0].google_scope,
        };
    }
    return null;
}
/**
 * Löscht die Google Tokens eines Benutzers (setzt sie auf NULL).
 */
async function clearUserGoogleTokens(userId) {
    const fnLogPrefix = `[UserRepo ClearGoogleTokens User:${userId}]`;
    log.info(`${fnLogPrefix} Clearing Google tokens for user ${userId}.`);
    const sql = `
        UPDATE users SET
            google_access_token = NULL,
            google_refresh_token = NULL,
            google_expires_at = NULL,
            google_scope = NULL
        WHERE id = ?
    `;
    try {
        const [result] = await connection_1.default.execute(sql, [userId]);
        log.info(`${fnLogPrefix} Google tokens cleared for user ${userId}, affected rows: ${result.affectedRows}`);
        return result.affectedRows > 0;
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error clearing Google tokens for user ${userId}:`, error);
        throw error;
    }
}
/**
 * Aktualisiert die Google Drive Tokens eines Benutzers. Akzeptiert partielle Daten.
 */
async function updateUserGoogleDriveTokens(userId, tokenData) {
    const fnLogPrefix = `[UserRepo UpdateGDriveTokens User:${userId}]`;
    log.debug(`${fnLogPrefix} Updating Google Drive tokens with data:`, tokenData);
    const fieldsToUpdate = [];
    const values = [];
    // Nur Felder hinzufügen, die auch wirklich in tokenData vorhanden sind
    if (tokenData.accessToken !== undefined) {
        fieldsToUpdate.push("google_drive_access_token = ?");
        values.push(tokenData.accessToken); // Kann null sein, um zu löschen
    }
    if (tokenData.refreshToken !== undefined) {
        fieldsToUpdate.push("google_drive_refresh_token = ?");
        values.push(tokenData.refreshToken); // Kann null sein
    }
    if (tokenData.expiresAt !== undefined) { // expiresAt ist number | null
        fieldsToUpdate.push("google_drive_expires_at = ?");
        values.push(tokenData.expiresAt);
    }
    if (tokenData.scope !== undefined) {
        fieldsToUpdate.push("google_drive_scope = ?");
        values.push(tokenData.scope); // Kann null sein
    }
    if (fieldsToUpdate.length === 0) {
        log.info(`${fnLogPrefix} No Google Drive token fields to update for user ${userId}.`);
        return false; // Keine Änderungen an die DB gesendet, aber kein Fehler
    }
    values.push(userId); // Für die WHERE-Klausel
    const sql = `UPDATE users SET ${fieldsToUpdate.join(', ')} WHERE id = ?`;
    log.debug(`${fnLogPrefix} SQL: ${sql}`);
    log.debug(`${fnLogPrefix} Values: ${JSON.stringify(values)}`);
    try {
        const [result] = await connection_1.default.execute(sql, values);
        log.info(`${fnLogPrefix} Google Drive tokens updated for user ${userId}, affected rows: ${result.affectedRows}`);
        return result.affectedRows > 0;
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error updating Google Drive tokens for user ${userId}:`, error);
        throw error; // Fehler weiterwerfen für zentrale Behandlung
    }
}
/**
 * Ruft die Google Drive Tokens für einen Benutzer ab.
 */
async function getUserGoogleDriveTokens(userId) {
    const sql = "SELECT google_drive_access_token, google_drive_refresh_token, google_drive_expires_at, google_drive_scope FROM users WHERE id = ?";
    const [rows] = await connection_1.default.query(sql, [userId]);
    if (rows.length > 0 && rows[0].google_drive_access_token) { // Prüfen, ob Token vorhanden ist
        return {
            accessToken: rows[0].google_drive_access_token,
            refreshToken: rows[0].google_drive_refresh_token,
            expiresAt: rows[0].google_drive_expires_at, // Ist BIGINT in DB, wird als number gelesen
            scope: rows[0].google_drive_scope,
        };
    }
    return null;
}
/**
 * Löscht die Google Drive Tokens eines Benutzers (setzt sie auf NULL).
 */
async function clearUserGoogleDriveTokens(userId) {
    const fnLogPrefix = `[UserRepo ClearGDriveTokens User:${userId}]`;
    log.info(`${fnLogPrefix} Clearing Google Drive tokens for user ${userId}.`);
    const sql = `
        UPDATE users SET
            google_drive_access_token = NULL,
            google_drive_refresh_token = NULL,
            google_drive_expires_at = NULL,
            google_drive_scope = NULL
        WHERE id = ?
    `;
    try {
        const [result] = await connection_1.default.execute(sql, [userId]);
        log.info(`${fnLogPrefix} Google Drive tokens cleared for user ${userId}, affected rows: ${result.affectedRows}`);
        return result.affectedRows > 0;
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error clearing Google Drive tokens for user ${userId}:`, error);
        throw error;
    }
}
/**
 * Aktualisiert die Google Photos Picker Tokens eines Benutzers. Akzeptiert partielle Daten.
 */
async function updateUserGooglePhotosPickerTokens(userId, tokenData) {
    const fnLogPrefix = `[UserRepo UpdateGPhotosPickerTokens User:${userId}]`;
    log.debug(`${fnLogPrefix} Updating Google Photos Picker tokens with data:`, tokenData);
    const fieldsToUpdate = [];
    const values = [];
    // Nur Felder hinzufügen, die auch wirklich in tokenData vorhanden sind
    if (tokenData.accessToken !== undefined) {
        fieldsToUpdate.push("google_photos_picker_access_token = ?");
        values.push(tokenData.accessToken); // Kann null sein, um zu löschen
    }
    if (tokenData.refreshToken !== undefined) {
        fieldsToUpdate.push("google_photos_picker_refresh_token = ?");
        values.push(tokenData.refreshToken); // Kann null sein
    }
    if (tokenData.expiresAt !== undefined) { // expiresAt ist number | null
        fieldsToUpdate.push("google_photos_picker_expires_at = ?");
        values.push(tokenData.expiresAt);
    }
    if (tokenData.scope !== undefined) {
        fieldsToUpdate.push("google_photos_picker_scope = ?");
        values.push(tokenData.scope); // Kann null sein
    }
    if (fieldsToUpdate.length === 0) {
        log.info(`${fnLogPrefix} No Google Photos Picker token fields to update for user ${userId}.`);
        return false; // Keine Änderungen an die DB gesendet, aber kein Fehler
    }
    values.push(userId); // Für die WHERE-Klausel
    const sql = `UPDATE users SET ${fieldsToUpdate.join(', ')} WHERE id = ?`;
    log.debug(`${fnLogPrefix} SQL: ${sql}`);
    log.debug(`${fnLogPrefix} Values: ${JSON.stringify(values)}`);
    try {
        const [result] = await connection_1.default.execute(sql, values);
        log.info(`${fnLogPrefix} Google Photos Picker tokens updated for user ${userId}, affected rows: ${result.affectedRows}`);
        return result.affectedRows > 0;
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error updating Google Photos Picker tokens for user ${userId}:`, error);
        throw error; // Fehler weiterwerfen für zentrale Behandlung
    }
}
/**
 * Ruft die Google Photos Picker Tokens für einen Benutzer ab.
 */
async function getUserGooglePhotosPickerTokens(userId) {
    const sql = "SELECT google_photos_picker_access_token, google_photos_picker_refresh_token, google_photos_picker_expires_at, google_photos_picker_scope FROM users WHERE id = ?";
    const [rows] = await connection_1.default.query(sql, [userId]);
    if (rows.length > 0 && rows[0].google_photos_picker_access_token) { // Prüfen, ob Token vorhanden ist
        return {
            accessToken: rows[0].google_photos_picker_access_token,
            refreshToken: rows[0].google_photos_picker_refresh_token,
            expiresAt: rows[0].google_photos_picker_expires_at, // Ist BIGINT in DB, wird als number gelesen
            scope: rows[0].google_photos_picker_scope,
        };
    }
    return null;
}
/**
 * Löscht die Google Photos Picker Tokens eines Benutzers (setzt sie auf NULL).
 */
async function clearUserGooglePhotosPickerTokens(userId) {
    const fnLogPrefix = `[UserRepo ClearGPhotosPickerTokens User:${userId}]`;
    log.info(`${fnLogPrefix} Clearing Google Photos Picker tokens for user ${userId}.`);
    const sql = `
        UPDATE users SET
            google_photos_picker_access_token = NULL,
            google_photos_picker_refresh_token = NULL,
            google_photos_picker_expires_at = NULL,
            google_photos_picker_scope = NULL
        WHERE id = ?
    `;
    try {
        const [result] = await connection_1.default.execute(sql, [userId]);
        log.info(`${fnLogPrefix} Google Photos Picker tokens cleared for user ${userId}, affected rows: ${result.affectedRows}`);
        return result.affectedRows > 0;
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error clearing Google Photos Picker tokens for user ${userId}:`, error);
        throw error;
    }
}
/**
 * Aktualisiert den ausgewählten Google Drive Ordner eines Benutzers.
 */
async function updateUserGoogleDriveFolder(userId, folderId) {
    const fnLogPrefix = `[UserRepo UpdateGDriveFolder User:${userId}]`;
    log.info(`${fnLogPrefix} Updating Google Drive folder to ${folderId || 'NULL'} for user ${userId}.`);
    const sql = `UPDATE users SET google_drive_folder_id = ? WHERE id = ?`;
    try {
        const [result] = await connection_1.default.execute(sql, [folderId, userId]);
        log.info(`${fnLogPrefix} Google Drive folder updated for user ${userId}, affected rows: ${result.affectedRows}`);
        return result.affectedRows > 0;
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error updating Google Drive folder for user ${userId}:`, error);
        throw error;
    }
}
/**
 * Führt eine allgemeine SQL-Abfrage aus.
 * Hilfsfunktion für andere Services, die SQL-Abfragen ausführen müssen.
 */
async function executeQuery(sql, params = []) {
    try {
        const [result] = await connection_1.default.execute(sql, params);
        return result;
    }
    catch (error) {
        log.error(`Error executing query: ${sql}`, error);
        throw error;
    }
}
//# sourceMappingURL=userRepository.js.map