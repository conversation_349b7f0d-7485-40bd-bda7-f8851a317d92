"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
// src/server.ts
const express_1 = __importDefault(require("express"));
const express_session_1 = __importDefault(require("express-session"));
const path_1 = __importDefault(require("path"));
const express_ejs_layouts_1 = __importDefault(require("express-ejs-layouts"));
const passport_1 = __importDefault(require("passport"));
const connect_flash_1 = __importDefault(require("connect-flash"));
const express_fileupload_1 = __importDefault(require("express-fileupload"));
const passport_config_1 = __importDefault(require("./config/passport-config"));
const logger_1 = __importDefault(require("./utils/logger"));
const config_1 = __importDefault(require("./config/config"));
const authRoutes_1 = __importDefault(require("./routes/authRoutes"));
const adminProtectedRoutes_1 = __importDefault(require("./routes/adminProtectedRoutes"));
const userRoutes_1 = __importDefault(require("./routes/userRoutes"));
const viewRoutes_1 = __importDefault(require("./routes/viewRoutes"));
const geojsonRoutes_1 = __importDefault(require("./routes/geojsonRoutes"));
const apiRoutes_1 = __importDefault(require("./routes/apiRoutes")); // Falls vorhanden
const stravaWebhookRoutes_1 = __importDefault(require("./routes/stravaWebhookRoutes")); // NEU
const garminRoutes_1 = __importDefault(require("./routes/garminRoutes")); // Garmin-Routen
// Import für die Strava API Queue und Sport-Typen
const stravaApiQueueProcessor = __importStar(require("./services/stravaApiQueueProcessor"));
const sportTypes = __importStar(require("./utils/sportTypes"));
const publicUserRepository = __importStar(require("./db/publicUserRepository"));
const loadUserNotificationsSummary_1 = require("./middleware/loadUserNotificationsSummary");
const requestLogger_1 = require("./middleware/requestLogger");
const deviceDetection_1 = require("./middleware/deviceDetection");
const cspMiddleware_1 = require("./middleware/cspMiddleware");
const log = logger_1.default.getLogger(__filename);
const app = (0, express_1.default)();
// Konfiguriere Express, um Proxy-Header zu vertrauen
// Dies ist wichtig, damit req.ip die tatsächliche Client-IP enthält
app.set('trust proxy', true);
// View Engine Setup
app.set('views', path_1.default.join(__dirname, '..', 'views'));
app.set('view engine', 'ejs');
app.use(express_ejs_layouts_1.default);
app.set('layout', 'layouts/main_layout');
// Standard Middleware
app.use(express_1.default.json({ limit: '50mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '50mb' }));
app.use(express_1.default.static(path_1.default.join(__dirname, '..', 'public')));
// File Upload Middleware
app.use((0, express_fileupload_1.default)({
    createParentPath: true,
    // Wir verwenden die direkte Option statt limits
    abortOnLimit: true,
    responseOnLimit: 'Datei zu groß (max. 50MB).',
    useTempFiles: true,
    tempFileDir: '/tmp/'
}));
// Ihre static-Routen für GPX_files und photosBaseUrl
app.use('/GPX_files', express_1.default.static(config_1.default.paths.gpxBaseDir));
app.use(config_1.default.maps.photoBaseUrl, express_1.default.static(config_1.default.paths.imageBaseUploadPath));
//photoBaseUrl: env('MAPS_PHOTO_BASE_URL', '/uploads/activity_photos'),
//imageBaseUploadPath: env('IMAGE_UPLOAD_PATH', path.join(path.resolve(__dirname, '..', '..'), 'public', 'uploads', 'activity_photos')),
// Session-Konfiguration
app.use((0, express_session_1.default)({
    name: config_1.default.session.name || 'connect.sid',
    secret: config_1.default.session.secret,
    // @ts-ignore
    store: new (require('express-mysql-session')(express_session_1.default))({
        host: config_1.default.db.host,
        port: config_1.default.db.port,
        user: config_1.default.db.user,
        password: config_1.default.db.password,
        database: config_1.default.db.database,
        clearExpired: true,
        checkExpirationInterval: 900000,
        expiration: config_1.default.session.cookie.maxAge,
        createDatabaseTable: true,
        schema: { tableName: 'sessions', columnNames: { session_id: 'session_id', expires: 'expires', data: 'data' } }
    }),
    resave: config_1.default.session.resave,
    saveUninitialized: config_1.default.session.saveUninitialized,
    cookie: config_1.default.session.cookie
}));
// === Passport Middleware Initialisierung ===
(0, passport_config_1.default)(passport_1.default); // Rufen Sie Ihre Konfigurationsfunktion auf
app.use(passport_1.default.initialize());
app.use(passport_1.default.session()); // Für persistente Login-Sessions
// Flash-Middleware für Benachrichtigungen
app.use((0, connect_flash_1.default)());
// Content Security Policy Middleware (für Google APIs)
app.use(cspMiddleware_1.cspMiddleware);
// IP-basiertes Logging (nach Passport, damit Benutzerinformationen verfügbar sind)
app.use(requestLogger_1.requestLogger);
app.use(requestLogger_1.suspiciousRequestLogger);
// Device Detection Middleware (vor den globalen Locals)
app.use(deviceDetection_1.deviceDetection);
// Globale Middleware für res.locals (currentUser, config etc.)
app.use((req, res, next) => {
    const fnLogPrefix = '[GlobalLocalsMiddleware]';
    log.debug(`${fnLogPrefix} Path: ${req.path}`);
    log.debug(`${fnLogPrefix} req.session.user (vorhanden?): ${!!req.session.user}`);
    if (req.session.user) {
        log.debug(`${fnLogPrefix} req.session.user content:`, req.session.user);
    }
    log.debug(`${fnLogPrefix} req.user (von Passport, vorhanden?): ${!!req.user}`);
    if (req.user) {
        log.debug(`${fnLogPrefix} req.user content:`, req.user);
        res.locals.currentUser = req.user;
        res.locals.isAdmin = req.user.role === 'admin';
        log.debug(`${fnLogPrefix} res.locals.currentUser GESETZT auf:`, res.locals.currentUser);
    }
    else {
        res.locals.currentUser = null;
        res.locals.isAdmin = false;
        log.debug(`${fnLogPrefix} req.user NICHT vorhanden, res.locals.currentUser auf null gesetzt.`);
    }
    res.locals.config = config_1.default;
    res.locals.pageTitle = config_1.default.app.name || 'Master-Map';
    // Flash-Nachrichten in res.locals verfügbar machen
    // Diese werden in den Layouts angezeigt
    res.locals.successMessage = req.flash('success');
    res.locals.errorMessage = req.flash('error');
    res.locals.infoMessage = req.flash('info');
    res.locals.currentYear = new Date().getFullYear();
    next();
});
// Middleware für den Benachrichtigungs-Counter
app.use(loadUserNotificationsSummary_1.loadUserNotificationsSummary);
// --- Routen-Definitionen ---
app.use('/auth', authRoutes_1.default);
app.use('/admin', adminProtectedRoutes_1.default);
app.use('/user', userRoutes_1.default);
app.use('/show', viewRoutes_1.default); // Für öffentliche Karten und ggf. Stats (Stats-Seite wurde entfernt)
app.use('/api/geojson', geojsonRoutes_1.default);
app.use('/api', apiRoutes_1.default); // Für andere API-Endpunkte
app.use(stravaWebhookRoutes_1.default); // NEU: Webhook Routen einbinden (z.B. auf Root-Ebene oder unter /strava)
app.use('/user/garmin', garminRoutes_1.default); // Garmin-Routen
// Root-Route für die Willkommensseite
app.get('/', async (req, res) => {
    const fnLogPrefix = '[Root Route]';
    // Prüfe, ob der Benutzer authentifiziert ist (Passport.js Methode)
    if (req.isAuthenticated && req.isAuthenticated()) {
        // Benutzer ist über Passport authentifiziert
        if (req.user && req.user.role === 'admin') {
            log.info(`${fnLogPrefix} Authenticated admin user ${req.user.username} redirected to admin dashboard`);
            return res.redirect('/admin/dashboard');
        }
        else if (req.user) {
            log.info(`${fnLogPrefix} Authenticated user ${req.user.username} redirected to user dashboard`);
            return res.redirect('/user/dashboard');
        }
    }
    // Fallback: Prüfe session.user (für den Fall, dass Passport nicht verwendet wird)
    if (req.session && req.session.user) {
        if (req.session.user.role === 'admin') {
            log.info(`${fnLogPrefix} Session admin user ${req.session.user.username} redirected to admin dashboard`);
            return res.redirect('/admin/dashboard');
        }
        else {
            log.info(`${fnLogPrefix} Session user ${req.session.user.username} redirected to user dashboard`);
            return res.redirect('/user/dashboard');
        }
    }
    try {
        // Hole zufällige Benutzer mit öffentlichen Karten
        const publicMapUsers = await publicUserRepository.getRandomPublicMapUsers(5);
        // Kein authentifizierter Benutzer, zeige die Willkommensseite
        log.debug(`${fnLogPrefix} No authenticated user, showing welcome page with ${publicMapUsers.length} public maps`);
        res.render('welcome', {
            pageTitle: `Willkommen bei ${config_1.default.app.name || 'Master-Map'}`,
            layout: 'layouts/simple_layout', // Ein anderes Layout für die Willkommensseite, ohne User-Header etc.
            publicMapUsers: publicMapUsers // Übergebe die öffentlichen Karten an die View
        });
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error fetching public map users: ${error}`);
        // Fallback ohne öffentliche Karten
        res.render('welcome', {
            pageTitle: `Willkommen bei ${config_1.default.app.name || 'Master-Map'}`,
            layout: 'layouts/simple_layout'
        });
    }
});
// --- Fehlerbehandlung ---
// 404 Handler (Fallback für nicht gefundene Routen)
app.use((req, res, next) => {
    log.warn(`[404 Handler] Route nicht gefunden: ${req.method} ${req.originalUrl}`);
    res.status(404).render('error', {
        pageTitle: 'Seite nicht gefunden (404)',
        message: 'Die angeforderte Seite konnte nicht gefunden werden.',
        statusCode: 404,
        layout: 'layouts/simple_layout' // Auch hier ein einfaches Layout
    });
});
// Globaler Fehlerbehandlungs-Middleware (muss als Letztes definiert werden)
app.use((err, req, res, next) => {
    log.error(`[GlobalErrorHandler] Unerwarteter Fehler aufgetreten: ${err.message}`, {
        stack: err.stack,
        url: req.originalUrl,
        method: req.method,
        ip: req.ip
    });
    const statusCode = err.status || 500;
    res.status(statusCode).render('error', {
        pageTitle: `Fehler ${statusCode}`,
        message: err.expose ? err.message : 'Ein interner Serverfehler ist aufgetreten.',
        statusCode: statusCode,
        errorDetail: process.env.NODE_ENV === 'development' ? err.stack : undefined,
        layout: 'layouts/simple_layout'
    });
});
// Initialisiere den Strava API Queue Prozessor
stravaApiQueueProcessor.initQueueProcessor();
log.info('Strava API Queue Prozessor initialisiert');
// Initialisiere den Sport-Typen-Cache
sportTypes.initializeSyncCache()
    .then(() => {
    log.info('Sport-Typen-Cache initialisiert');
})
    .catch(error => {
    log.error('Fehler beim Initialisieren des Sport-Typen-Caches:', error);
});
// Server starten
const PORT = config_1.default.server.port;
app.listen(PORT, () => {
    log.info(`Server läuft auf http://localhost:${PORT}`);
});
//# sourceMappingURL=server.js.map