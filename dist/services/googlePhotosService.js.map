{"version": 3, "file": "googlePhotosService.js", "sourceRoot": "", "sources": ["../../src/services/googlePhotosService.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBA,oEAyFC;AA4ED,4CAoBC;AA3MD,sCAAsC;AACtC,2CAAoC;AAEpC,8DAAsC;AACtC,6DAAqC;AACrC,qEAAuD;AAIvD,MAAM,GAAG,GAAG,gBAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;AAEzC,MAAM,mBAAmB,GAAG,yCAAyC,CAAC;AAGtE;;;GAGG;AACI,KAAK,UAAU,4BAA4B,CAC9C,MAAc,EACd,sBAA4B,EAC5B,oBAA0B,EAC1B,WAAmB,EAAE,EACrB,SAAkB;IAElB,MAAM,WAAW,GAAG,oCAAoC,MAAM,GAAG,CAAC;IAElE,wFAAwF;IACxF,kFAAkF;IAClF,iFAAiF;IACjF,2DAA2D;IAC3D,GAAG,CAAC,IAAI,CAAC,GAAG,WAAW,8IAA8I,CAAC,CAAC;IAEvK,MAAM,IAAI,KAAK,CAAC,4PAA4P,CAAC,CAAC;IAE9Q,kFAAkF;IAClF,MAAM,YAAY,GAAG,MAAM,sBAAsB,CAAC,MAAM,CAAC,CAAC;IAE1D,MAAM,qBAAqB,GAAG,CAAC,IAAU,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;IAC9H,MAAM,eAAe,GAAG,qBAAqB,CAAC,sBAAsB,CAAC,CAAC;IACtE,MAAM,aAAa,GAAG,qBAAqB,CAAC,oBAAoB,CAAC,CAAC;IAElE,MAAM,iBAAiB,GAAG;QACtB,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC;QACjC,OAAO,EAAE,EAAE,UAAU,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,SAAS,EAAE,eAAe,EAAE,OAAO,EAAE,aAAa,EAAE,CAAC,EAAE,EAAE,eAAe,EAAE,EAAE,UAAU,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE;QACzI,SAAS,EAAE,SAAS,IAAI,SAAS;KACpC,CAAC;IAEF,GAAG,CAAC,IAAI,CAAC,GAAG,WAAW,iDAAiD,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;IAEjJ,IAAI,CAAC;QACD,MAAM,QAAQ,GAAG,MAAM,YAAY,CAAC,OAAO,CAAmE;YAC1G,GAAG,EAAE,GAAG,mBAAmB,oBAAoB;YAC/C,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,iBAAiB;SAC1B,CAAC,CAAC;QAEH,MAAM,gBAAgB,GAAG,QAAQ,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE,CAAC;QACxD,MAAM,UAAU,GAAgB,EAAE,CAAC;QAEnC,KAAK,MAAM,IAAI,IAAI,gBAAgB,EAAE,CAAC;YAClC,IAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,aAAa,EAAE,YAAY,EAAE,CAAC;gBACjJ,MAAM,iBAAiB,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;gBACpE,IAAI,iBAAiB,IAAI,sBAAsB,IAAI,iBAAiB,IAAI,oBAAoB,EAAE,CAAC;oBAC3F,UAAU,CAAC,IAAI,CAAC;wBACZ,EAAE,EAAE,IAAI,CAAC,EAAE;wBACX,WAAW,EAAE,IAAI,CAAC,WAAW,IAAI,IAAI;wBACrC,UAAU,EAAE,IAAI,CAAC,UAAU;wBAC3B,OAAO,EAAE,IAAI,CAAC,OAAO;wBACrB,QAAQ,EAAE,IAAI,CAAC,QAAQ;wBACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ;wBACvB,aAAa,EAAE;4BACX,YAAY,EAAE,iBAAiB;4BAC/B,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS;4BAC5F,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS;yBAClG;qBACJ,CAAC,CAAC;gBACP,CAAC;qBAAM,CAAC,CAAC,oBAAoB,CAAC,CAAC;YACnC,CAAC;QACL,CAAC;QAED,GAAG,CAAC,IAAI,CAAC,GAAG,WAAW,UAAU,gBAAgB,CAAC,MAAM,gCAAgC,UAAU,CAAC,MAAM,oBAAoB,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;QAC9J,OAAO,EAAE,UAAU,EAAE,aAAa,EAAE,QAAQ,CAAC,IAAI,CAAC,aAAa,IAAI,SAAS,EAAE,CAAC;IACnF,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QAClB,GAAG,CAAC,KAAK,CAAC,GAAG,WAAW,yCAAyC,EAAE,KAAK,CAAC,QAAQ,EAAE,IAAI,IAAI,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,CAAC;QAEnH,sDAAsD;QACtD,IAAI,KAAK,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,KAAK,iBAAiB,CAAC,EAAE,CAAC;YAC/F,GAAG,CAAC,IAAI,CAAC,GAAG,WAAW,sCAAsC,MAAM,2BAA2B,CAAC,CAAC;YAChG,MAAM,cAAc,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;YACnD,MAAM,IAAI,KAAK,CAAC,8FAA8F,CAAC,CAAC;QACrH,CAAC;QAED,4CAA4C;QAC5C,IAAI,KAAK,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,KAAK,WAAW,EAAE,CAAC;YACxF,MAAM,YAAY,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,EAAE,CAAC;YAC7D,IAAI,YAAY,CAAC,QAAQ,CAAC,oCAAoC,CAAC,EAAE,CAAC;gBAC9D,GAAG,CAAC,IAAI,CAAC,GAAG,WAAW,sCAAsC,MAAM,iCAAiC,CAAC,CAAC;gBACtG,MAAM,cAAc,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;gBACnD,MAAM,IAAI,KAAK,CAAC,sIAAsI,CAAC,CAAC;YAC5J,CAAC;YACD,MAAM,IAAI,KAAK,CAAC,qCAAqC,YAAY,EAAE,CAAC,CAAC;QACzE,CAAC;QAED,MAAM,eAAe,GAAG,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,IAAI,KAAK,CAAC,OAAO,IAAI,sCAAsC,CAAC;QACxH,MAAM,IAAI,KAAK,CAAC,qCAAqC,eAAe,EAAE,CAAC,CAAC;IAC5E,CAAC;AACL,CAAC;AAGD;;;;;GAKG;AACH,KAAK,UAAU,sBAAsB,CAAC,MAAc;IAChD,MAAM,WAAW,GAAG,yBAAyB,MAAM,GAAG,CAAC;IACvD,+DAA+D;IAC/D,MAAM,UAAU,GAA2B,MAAM,cAAc,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;IAI5F,qDAAqD;IACrD,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC;QACzC,GAAG,CAAC,KAAK,CAAC,GAAG,WAAW,wCAAwC,MAAM,YAAY,CAAC,CAAC;QACpF,MAAM,IAAI,KAAK,CAAC,+HAA+H,CAAC,CAAC;IACrJ,CAAC;IAED,MAAM,YAAY,GAAG,IAAI,mBAAM,CAAC,IAAI,CAAC,MAAM,CACvC,gBAAM,CAAC,YAAY,CAAC,QAAQ,EAC5B,gBAAM,CAAC,YAAY,CAAC,YAAY,EAChC,gBAAM,CAAC,YAAY,CAAC,WAAW,CAClC,CAAC;IAEF,YAAY,CAAC,cAAc,CAAC;QACxB,YAAY,EAAE,UAAU,CAAC,WAAW,EAAU,YAAY;QAC1D,aAAa,EAAE,UAAU,CAAC,YAAY,EAAQ,YAAY;QAC1D,WAAW,EAAE,UAAU,CAAC,SAAS,EAAa,gCAAgC;KACjF,CAAC,CAAC;IAEH,YAAY,CAAC,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE,EAAE;QAC1C,GAAG,CAAC,IAAI,CAAC,GAAG,WAAW,sCAAsC,CAAC,CAAC;QAC/D,MAAM,gBAAgB,GAA6B,EAAE,CAAC;QACtD,IAAI,SAAS,CAAC,YAAY;YAAE,gBAAgB,CAAC,WAAW,GAAG,SAAS,CAAC,YAAY,CAAC;QAClF,IAAI,SAAS,CAAC,WAAW;YAAE,gBAAgB,CAAC,SAAS,GAAG,SAAS,CAAC,WAAW,CAAC;QAC9E,IAAI,SAAS,CAAC,aAAa;YAAE,gBAAgB,CAAC,YAAY,GAAG,SAAS,CAAC,aAAa,CAAC;QACrF,IAAI,SAAS,CAAC,KAAK,IAAI,OAAO,SAAS,CAAC,KAAK,KAAK,QAAQ,EAAE,CAAC;YACxD,MAAM,aAAa,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,iCAAiC;YAC5G,MAAM,cAAc,GAAG,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAClD,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,aAAa,EAAE,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;YAC7E,gBAAgB,CAAC,KAAK,GAAG,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAClD,CAAC;QAED,IAAI,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3C,MAAM,cAAc,CAAC,sBAAsB,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;YACtE,GAAG,CAAC,IAAI,CAAC,GAAG,WAAW,qCAAqC,MAAM,eAAe,CAAC,CAAC;QACvF,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,8CAA8C;IAC9C,IAAI,UAAU,CAAC,SAAS,IAAI,UAAU,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,EAAE,CAAC,CAAC,eAAe;QACpF,IAAI,CAAC;YACD,GAAG,CAAC,IAAI,CAAC,GAAG,WAAW,sEAAsE,CAAC,CAAC;YAC/F,MAAM,EAAE,WAAW,EAAE,GAAG,MAAM,YAAY,CAAC,kBAAkB,EAAE,CAAC;YAChE,YAAY,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;QAC7C,CAAC;QAAC,OAAO,YAAiB,EAAE,CAAC;YACzB,GAAG,CAAC,KAAK,CAAC,GAAG,WAAW,iDAAiD,EAAE,YAAY,CAAC,QAAQ,EAAE,IAAI,IAAI,YAAY,CAAC,OAAO,CAAC,CAAC;YAChI,MAAM,cAAc,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;YACnD,MAAM,IAAI,KAAK,CAAC,yFAAyF,CAAC,CAAC;QAC/G,CAAC;IACL,CAAC;IACD,OAAO,YAAY,CAAC;AACxB,CAAC;AAGD;;;;GAIG;AACH,wGAAwG;AACxG,oEAAoE;AAC7D,KAAK,UAAU,gBAAgB,CAAC,MAAc,EAAE,gBAAqB,EAAE;IAC1E,MAAM,WAAW,GAAG,gCAAgC,MAAM,GAAG,CAAC;IAC9D,GAAG,CAAC,IAAI,CAAC,GAAG,WAAW,0BAA0B,EAAE,aAAa,CAAC,CAAC;IAClE,MAAM,YAAY,GAAG,MAAM,sBAAsB,CAAC,MAAM,CAAC,CAAC;IAC1D,MAAM,WAAW,GAAG,YAAY,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,oBAAoB;IAC/E,MAAM,kBAAkB,GAAQ,EAAE,QAAQ,EAAE,aAAa,CAAC,QAAQ,IAAI,GAAG,EAAE,CAAC;IAC5E,IAAI,aAAa,CAAC,OAAO;QAAE,kBAAkB,CAAC,OAAO,GAAG,aAAa,CAAC,OAAO,CAAC;IAC9E,IAAI,aAAa,CAAC,OAAO;QAAE,kBAAkB,CAAC,OAAO,GAAG,aAAa,CAAC,OAAO,CAAC;IAE9E,IAAI,CAAC;QACD,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,mBAAmB,oBAAoB,EAAE;YACrE,MAAM,EAAE,MAAM;YACd,OAAO,EAAE,EAAE,eAAe,EAAE,UAAU,WAAW,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;YACzF,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC;SAC3C,CAAC,CAAC;QACH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC,CAAC,8BAA8B;YAAC,MAAM,IAAI,KAAK,CAAC,WAAW,CAAC,CAAC;QAAC,CAAC;QAClF,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;QACnC,GAAG,CAAC,IAAI,CAAC,GAAG,WAAW,UAAU,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;QACxF,OAAO,IAAI,CAAC,UAAU,IAAI,EAAE,CAAC;IACjC,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC,CAAC,8BAA8B;QAAC,MAAM,KAAK,CAAC;IAAC,CAAC;AACxE,CAAC;AAID;;;;GAIG;AACI,MAAM,YAAY,GAAG,KAAK,EAAE,WAAmB,EAAE,MAAc,EAAyC,EAAE;IAC7G,MAAM,WAAW,GAAG,iCAAiC,MAAM,SAAS,WAAW,GAAG,CAAC;IACnF,GAAG,CAAC,IAAI,CAAC,GAAG,WAAW,sBAAsB,CAAC,CAAC;IAC/C,IAAI,CAAC;QACD,MAAM,YAAY,GAAG,MAAM,sBAAsB,CAAC,MAAM,CAAC,CAAC;QAC1D,MAAM,WAAW,GAAG,YAAY,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,oBAAoB;QAC/E,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,mBAAmB,eAAe,WAAW,EAAE,EAAE;YAC7E,MAAM,EAAE,KAAK;YACb,OAAO,EAAE,EAAE,eAAe,EAAE,UAAU,WAAW,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;SAC5F,CAAC,CAAC;QACH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC,CAAC,8BAA8B;YAAC,MAAM,IAAI,KAAK,CAAC,WAAW,CAAC,CAAC;QAAC,CAAC;QAClF,OAAO,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;IACjC,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC,CAAC,8BAA8B;QAAC,MAAM,KAAK,CAAC;IAAC,CAAC;AACxE,CAAC,CAAC;AAbW,QAAA,YAAY,gBAavB;AAEF,4EAA4E;AAC5E,iFAAiF;AACjF,sDAAsD;AAC/C,MAAM,UAAU,GAAG,KAAK,EAAE,MAAc,EAAoC,EAAE;IACjF,MAAM,WAAW,GAAG,+BAA+B,MAAM,GAAG,CAAC;IAC7D,GAAG,CAAC,IAAI,CAAC,GAAG,WAAW,qBAAqB,CAAC,CAAC;IAC7C,IAAI,CAAC;QACF,MAAM,YAAY,GAAG,MAAM,sBAAsB,CAAC,MAAM,CAAC,CAAC;QAC1D,MAAM,WAAW,GAAG,YAAY,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,oBAAoB;QAC/E,MAAM,MAAM,GAAG,IAAI,eAAe,CAAC,EAAE,QAAQ,EAAE,MAAM,CAAC,gBAAM,CAAC,YAAY,CAAC,aAAa,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;QAClG,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,mBAAmB,WAAW,MAAM,CAAC,QAAQ,EAAE,EAAE,EAAE;YAC/E,MAAM,EAAE,KAAK;YACb,OAAO,EAAE,EAAE,eAAe,EAAE,UAAU,WAAW,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;SAC5F,CAAC,CAAC;QACH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC,CAAC,8BAA8B;YAAC,MAAM,IAAI,KAAK,CAAC,WAAW,CAAC,CAAC;QAAC,CAAC;QAClF,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;QACnC,GAAG,CAAC,IAAI,CAAC,GAAG,WAAW,UAAU,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;QACjF,OAAO,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC;IAC7B,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC,CAAC,8BAA8B;QAAC,MAAM,KAAK,CAAC;IAAC,CAAC;AACxE,CAAC,CAAC;AAhBW,QAAA,UAAU,cAgBrB"}