<%# views/users/edit_activity.ejs %>
<%# Das Layout 'layouts/main_layout' wird global in server.ts gesetzt. %>

<%# Seitenspezifische Variablen für das Layout definieren %>
<%
    // Sicherstellen, dass activity existiert, bevor auf activity_name zugegriffen wird
    let activityNameForTitle = 'Unbekannte Aktivität';
    if (locals.activity) {
        activityNameForTitle = activity.activity_name || `Aktivität ID ${activity.id}`;
    }
    pageTitle = 'Aktivität bearbeiten: ' + activityNameForTitle;
%>
<% pageSpecificClass = 'user-edit-activity-page' %>

<%# Seitenspezifische Stylesheets (optional) %>
<%_ contentFor('pageStylesheets') %>
    <style>
        .edit-activity-grid {
            display: grid;
            grid-template-columns: 1fr; /* Standardmäßig einspaltig */
            gap: 25px;
            margin-bottom: 20px;
        }

        /* Modal Styles */
        .modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.4);
            display: none; /* Standardmäßig ausgeblendet */
        }

        .modal-content {
            background-color: #fefefe;
            margin: 10% auto;
            padding: 20px;
            border: 1px solid #888;
            width: 80%;
            max-width: 500px;
            border-radius: 5px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            margin-top: -10px;
        }

        .close:hover,
        .close:focus {
            color: black;
            text-decoration: none;
        }

        .friend-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
        }

        .friend-item:hover {
            background-color: #f8f9fa;
        }

        .friend-item:last-child {
            border-bottom: none;
        }

        .friend-item label {
            display: block;
            cursor: pointer;
        }

        .friend-item input[type="checkbox"] {
            margin-right: 8px;
        }

        .form-actions {
            margin-top: 15px;
            text-align: right;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }
        /* Für größere Bildschirme ggf. mehrspaltig, wenn mehr Detailsektionen hinzukommen */
        /* @media (min-width: 992px) {
            .edit-activity-grid {
                grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            }
        } */

        .photo-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
            gap: 12px;
            margin-top: 15px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            background-color: #f9fafb;
            border-radius: 6px;
            min-height: 100px;
        }
        .photo-gallery .photo-item {
            position: relative;
            border: 1px solid #ddd;
            padding: 8px;
            background-color: #fff;
            text-align: center;
            border-radius: 4px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.05);
        }
        .photo-gallery .photo-item img {
            width: 100%;
            height: 100px;
            object-fit: cover;
            display: block;
            margin-bottom: 8px;
            border-radius: 3px;
        }

        .photo-gallery .photo-item a {
            display: block;
            text-decoration: none;
            cursor: pointer;
            position: relative;
        }

        .photo-gallery .photo-item a:hover img {
            opacity: 0.9;
            box-shadow: 0 0 5px rgba(0,0,0,0.3);
        }

        .photo-gallery .photo-item a:after {
            content: '🔍';
            position: absolute;
            top: 5px;
            right: 5px;
            background: rgba(255,255,255,0.7);
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            opacity: 0;
            transition: opacity 0.2s;
        }

        .photo-gallery .photo-item a:hover:after {
            opacity: 1;
        }
        .photo-gallery .photo-item input[type="checkbox"] {
            margin-right: 6px;
            vertical-align: middle;
        }
        .photo-gallery .photo-item label {
            font-size: 0.85em;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            cursor: pointer;
            text-align: left;
            word-break: break-all;
        }

        .linked-photos-list {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            margin-top: 10px;
        }
        .linked-photos-list .photo-item-linked {
            border: 1px solid #ccc;
            border-radius: 4px;
            overflow: hidden;
            position: relative;
        }
        .linked-photos-list .photo-item-linked img {
            width: 110px;
            height: 110px;
            object-fit: cover;
            display: block;
        }
        .linked-photos-list .unlink-photo-btn {
            position: absolute; top: 3px; right: 3px;
            background-color: rgba(220, 53, 69, 0.7);
            color: white; border: none; border-radius: 50%;
            width: 22px; height: 22px; line-height: 20px; text-align: center;
            font-size: 14px; font-weight: bold; cursor: pointer;
            padding: 0; box-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }
        .linked-photos-list .unlink-photo-btn:hover { background-color: #dc3545; }

        .loading-spinner, .no-photos-message {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            min-height: 80px;
            color: #6c757d;
            font-style: italic;
            grid-column: 1 / -1; /* Nimmt volle Breite im Grid ein */
        }
        .photo-gallery-actions {
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #eee;
        }
        .activity-details-summary dl {
            display: grid;
            grid-template-columns: max-content auto;
            gap: 8px 15px;
        }
        .activity-details-summary dt { font-weight: 600; color: #495057;}
        .activity-details-summary dd { margin-left: 0; }

        /* === INLINE TITLE EDITING === */
        .title-container {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .editable-title {
            margin: 0;
            padding: 2px 4px;
            border-radius: 3px;
            transition: background-color 0.2s ease;
        }

        .editable-title:hover {
            background-color: #f8f9fa;
        }

        .edit-icon {
            cursor: pointer;
            color: #007bff;
            font-size: 0.9em;
            transition: color 0.2s ease-in-out;
            opacity: 0.7;
        }

        .edit-icon:hover {
            color: #0056b3;
            opacity: 1;
        }

        .title-input {
            font-size: inherit;
            font-family: inherit;
            padding: 2px 6px;
            border: 1px solid #007bff;
            border-radius: 3px;
            background-color: #fff;
            min-width: 200px;
            outline: none;
        }

        .title-input:focus {
            border-color: #0056b3;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
        }
    </style>
<%_ %>

<%# Der pageTitle wird vom Layout verwendet %>
<h2 class="page-main-title"><%= locals.pageTitle %></h2>

<div class="page-specific-content">
    <% if (locals.activity) { %>
        <div class="edit-activity-grid">
            <section class="content-section activity-details-summary">
                <h3>Aktivitätsübersicht</h3>
                <dl>
                    <dt>Name:</dt>
                    <dd>
                        <div class="title-container">
                            <span id="activityTitle" class="editable-title"><%= activity.activity_name || '(Unbenannt)' %></span>
                            <i id="editTitleIcon" class="fas fa-pencil-alt edit-icon" title="Titel bearbeiten"></i>
                        </div>
                    </dd>
                    <dt>Datum:</dt><dd><%= activity.start_date_local ? new Date(activity.start_date_local).toLocaleString('de-DE', {dateStyle:'full', timeStyle:'short'}) : 'N/A' %></dd>
                    <dt>Sportart:</dt><dd><%= activity.sport_type || 'N/A' %></dd>
                    <dt>Distanz:</dt><dd><%= activity.distance != null ? (activity.distance / 1000).toFixed(2) + ' km' : 'N/A' %></dd>
                </dl>
                <p style="margin-top: 15px;">
                    <a href="/user/activity/<%= activity.id %>" class="button-link-small button-secondary">&laquo; Zurück zur Detailansicht</a>
                    | <a href="#" class="button-link-small button-success" id="shareActivityBtn">Mit Freunden teilen</a>
                </p>
            </section>



            <section class="content-section linked-photos-section">
                <h3>Bereits verknüpfte Fotos (<span id="linkedPhotoCount"><%= locals.linkedPhotos ? linkedPhotos.length : 0 %></span>)</h3>
                <div id="linkedPhotosContainer" class="linked-photos-list">
                    <% if (locals.linkedPhotos && linkedPhotos.length > 0) { %>
                        <% linkedPhotos.forEach(photo => { %>
                            <%
                                // Die URLs kommen jetzt fertig vom Backend aus mapDbRowToActivityPhoto
                                let displayThumbnailUrl = photo.computed_url_small || photo.computed_url_medium || photo.computed_url_original || '/img/placeholder_img.png';
                                let displayOriginalUrl = photo.computed_url_original || photo.external_url || '#';
                                const photoTitle = photo.caption || photo.base_filename || `Foto ID ${photo.id}`;
                            %>
                            <div class="photo-item-linked" id="linked-photo-<%= photo.id %>">
                                <a href="<%= displayOriginalUrl %>" target="_blank" title="Original anzeigen: <%= photoTitle %>">
                                    <img src="<%= displayThumbnailUrl %>" alt="<%= photoTitle %>">
                                </a>
                                <button type="button" class="unlink-photo-btn" data-photodb-id="<%= photo.id %>" title="Verknüpfung lösen">X</button>
                            </div>
                        <% }); %>
                    <% } else { %>
                        <p id="noLinkedPhotosMsg" class="no-photos-message" style="min-height:auto; padding: 5px 0;">Noch keine Fotos mit dieser Aktivität verknüpft.</p>
                    <% } %>
                </div>
            </section>

            <% if (activity.strava_id) { %>
            <section class="content-section strava-functions-section">
                <h3>Strava-Funktionen</h3>
                <p>Diese Aktivität wurde von Strava importiert (ID: <%= activity.strava_id %>).</p>
                <%- include('../partials/strava_api_limits', { activityId: activity.strava_id }) %>
                <form action="/user/activity/<%= activity.id %>/resync" method="POST" style="margin-top: 10px;">
                    <button type="submit" class="button button-warning"
                            title="Versucht, alle Daten (Details, Streams, Fotos) von Strava neu zu laden und die Aktivität serverseitig neu zu verarbeiten (z.B. Downhill-Segmente)."
                            onclick="return confirm('Möchten Sie wirklich alle Daten für diese Aktivität von Strava neu synchronisieren und serverseitig verarbeiten lassen? Dies kann API-Aufrufe bei Strava verbrauchen.');">
                        Aktivität neu von Strava synchronisieren
                    </button>
                </form>
            </section>
            <% } %>

            <section class="content-section equipment-section">
                <h3>Ausrüstung verwalten</h3>
                <% if (locals.linkedEquipmentDetails && linkedEquipmentDetails.length > 0) { %>
                    <div class="linked-equipment-display">
                        <ul>
                            <% linkedEquipmentDetails.forEach(item => { %>
                                <li>
                                    <strong><%= item.name %></strong> (<%= item.type || 'Allgemein' %>)
                                    <% if (item.notes) { %><small>Notiz: <%= item.notes %></small><% } %>
                                </li>
                            <% }); %>
                        </ul>
                    </div>
                <% } else { %>
                    <p class="no-equipment">Noch keine Ausrüstung für diese Aktivität ausgewählt.</p>
                <% } %>

                <h4 style="margin-top: 20px; margin-bottom: 10px;">Ausrüstung auswählen/ändern:</h4>
                <% if (locals.userEquipment && userEquipment.length > 0) { %>
                    <form action="/user/activity/<%= activity.id %>/equipment" method="POST">
                        <div class="equipment-list">
                            <% userEquipment.forEach(item => { %>
                                <% const isLinked = linkedEquipmentIds.includes(item.id); %>
                                <% const linkedDetail = linkedEquipmentDetails.find(linked => linked.id === item.id); %>
                                <% const currentNote = linkedDetail ? (linkedDetail.notes || '') : ''; %>
                                <div style="margin-bottom: 8px;">
                                    <label>
                                        <input type="checkbox" name="equipmentIds" value="<%= item.id %>" <%= isLinked ? 'checked' : '' %>>
                                        <%= item.name %> <span style="color:#777; font-size:0.9em;">(<%= item.type || 'Allgemein' %>)</span>
                                    </label>
                                    <div class="equipment-notes">
                                        <textarea name="notes[<%= item.id %>]" rows="1" placeholder="Spez. Notiz für diese Aktivität..."><%= currentNote %></textarea>
                                    </div>
                                </div>
                            <% }); %>
                        </div>
                        <button type="submit" class="button" style="margin-top: 15px;">Auswahl speichern</button>
                    </form>
                <% } else { %>
                    <p class="no-equipment" style="margin-top: 10px;">
                        Du hast noch keine Ausrüstung in deinem Profil angelegt.
                        <a href="/user/equipment">Jetzt Ausrüstung hinzufügen</a>.
                    </p>
                <% } %>
            </section>

            <section class="content-section google-photos-section">
                <h3>Google Photos hinzufügen</h3>
                <% if (locals.currentUser && currentUser.google_access_token) { %>
                    <p>
                        Suche nach Fotos aus deinem Google Photos Account, die im Zeitraum dieser Aktivität aufgenommen wurden.<br>
                        <small>
                            Zeitraum:
                            <strong><%= activity.start_date_local ? new Date(activity.start_date_local).toLocaleDateString('de-DE', {day: '2-digit', month: '2-digit', year: 'numeric', hour:'2-digit', minute: '2-digit'}) : '?' %></strong>
                            bis
                            <strong><%= activity.start_date_local && activity.elapsed_time ? new Date(new Date(activity.start_date_local).getTime() + activity.elapsed_time * 1000).toLocaleDateString('de-DE', {day: '2-digit', month: '2-digit', year: 'numeric', hour:'2-digit', minute: '2-digit'}) : '?' %></strong>
                        </small>
                    </p>
                    <!-- Google Photos Integration (vereinfacht) -->
                    <div class="google-photos-section">
                        <button type="button" id="simpleGooglePhotosBtn" class="button">
                            📷 Google Photos Picker
                        </button>
                        <div id="google-photos-status" class="google-photos-status" style="display: none;"></div>

                        <!-- Container für Picker Session -->
                        <div id="google-photos-picker-container" style="margin-top: 15px; display: none;">
                            <!-- Hier wird der Picker Link angezeigt -->
                        </div>

                        <!-- Info über bestehende Integration -->
                        <div class="google-photos-info" style="margin-top: 10px;">
                            <small>
                                <strong>Hinweis:</strong> Stelle sicher, dass Google Photos in den
                                <a href="/user/settings" target="_blank">Einstellungen</a> verbunden ist.
                            </small>
                        </div>

                        <!-- Alternative: Manueller Foto-Upload -->
                        <div class="manual-upload-section" style="margin-top: 15px;">
                            <h4>Alternative: Fotos manuell hochladen</h4>
                            <input type="file" id="manualPhotoUpload" accept="image/*" multiple style="margin-bottom: 10px;">
                            <button type="button" id="uploadPhotosBtn" class="button">
                                📤 Ausgewählte Fotos hochladen
                            </button>
                        </div>
                    </div>

                    <!-- Container für ausgewählte Fotos -->
                    <div id="selected-photos-container" class="selected-photos-container" style="display: none;">
                        <h4>Ausgewählte Fotos aus Google Photos</h4>
                        <div id="selected-photos-grid" class="selected-photos-grid">
                            <!-- Hier werden die ausgewählten Fotos angezeigt -->
                        </div>
                    </div>

                    <div id="googlePhotosResults" class="photo-gallery">
                        <div class="no-photos-message">
                            <strong>Hinweis:</strong> Die alte Google Photos API funktioniert seit April 2025 nicht mehr.
                            Verwende den neuen "📷 Fotos aus Google Photos auswählen" Button oben.
                        </div>
                    </div>
                    <div id="googlePhotosPagination" style="margin-top:10px; text-align:center;">
                        <button type="button" id="loadMoreGooglePhotosBtn" class="button-link-small button-secondary" style="display:none;">Mehr laden</button>
                    </div>

                    <form id="linkGooglePhotosForm" class="photo-gallery-actions" style="display: none;">
                        <button type="submit" class="button button-success">Ausgewählte Fotos verknüpfen</button>
                    </form>
                <% } else { %>
                    <p>
                        Bitte verbinde zuerst deinen Google Photos Account in den
                        <a href="/user/settings">Einstellungen</a>, um Fotos zu suchen und zu verknüpfen.
                    </p>
                <% } %>
            </section>
        </div>
    <% } else { %>
        <div class="message error-message">Aktivitätsdaten konnten nicht geladen werden oder die Aktivität existiert nicht.</div>
    <% } %>
</div>

<%# Modal für das Teilen mit Freunden %>
<div id="shareActivityModal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h3>Aktivität mit Freunden teilen</h3>
        <p>Wähle die Freunde aus, mit denen du diese Aktivität teilen möchtest:</p>
        <form id="shareActivityForm" action="/user/activity/<%= activity.id %>/share" method="POST">
            <div id="friendsList" style="margin: 15px 0; max-height: 300px; overflow-y: auto;">
                <p>Lade Freundesliste...</p>
            </div>
            <div class="form-actions">
                <button type="submit" class="button button-success">Teilen</button>
                <button type="button" class="button button-secondary" id="cancelShareBtn">Abbrechen</button>
            </div>
        </form>
    </div>
</div>

<%# Seitenspezifische Skripte %>
<%_ contentFor('pageScripts') %>
    <script>
        // Daten vom Server an das clientseitige JavaScript übergeben
        window.PAGE_ACTIVITY_DATA = <%- JSON.stringify(locals.activity || null) %>;
        // window.CURRENT_USER_DATA wird nicht explizit im edit_activity_page.js verwendet,
        // aber es ist gut, es konsistent zu übergeben, falls es später benötigt wird.
        window.CURRENT_USER_DATA = <%- JSON.stringify(locals.currentUser || null) %>;

        // === INLINE TITLE EDITING ===
        document.addEventListener('DOMContentLoaded', function() {
            const activityTitle = document.getElementById('activityTitle');
            const editTitleIcon = document.getElementById('editTitleIcon');

            if (activityTitle && editTitleIcon) {
                // Funktion zum Speichern des Titels im Backend
                async function saveTitleToBackend(newTitle) {
                    console.log('Titel wird im Backend gespeichert:', newTitle);
                    try {
                        const response = await fetch('/user/activity/<%= activity.id %>/update-title', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: 'activityTitle=' + encodeURIComponent(newTitle),
                        });

                        if (response.ok) {
                            console.log('Titel erfolgreich im Backend aktualisiert!');
                            // Optional: Kurze Bestätigung anzeigen
                            editTitleIcon.style.color = '#28a745';
                            setTimeout(() => {
                                editTitleIcon.style.color = '';
                            }, 1000);
                        } else {
                            console.error('Fehler beim Speichern des Titels im Backend:', response.statusText);
                            alert('Fehler beim Speichern des Titels. Bitte versuchen Sie es erneut.');
                        }
                    } catch (error) {
                        console.error('Netzwerkfehler beim Speichern des Titels:', error);
                        alert('Netzwerkfehler beim Speichern des Titels. Bitte versuchen Sie es erneut.');
                    }
                }

                // Event-Listener für das Stift-Icon
                editTitleIcon.addEventListener('click', function() {
                    // Ersetze den Titel durch ein Eingabefeld
                    const currentTitle = activityTitle.textContent;
                    const inputField = document.createElement('input');
                    inputField.type = 'text';
                    inputField.className = 'title-input';
                    inputField.value = currentTitle === '(Unbenannt)' ? '' : currentTitle;
                    inputField.maxLength = 255;

                    // Ersetze das span-Element durch das Eingabefeld
                    activityTitle.parentNode.replaceChild(inputField, activityTitle);
                    inputField.focus();
                    inputField.select();

                    // Event-Listener für das 'blur'-Ereignis (Verlust des Fokus)
                    inputField.addEventListener('blur', async function() {
                        const newTitle = inputField.value.trim();
                        const displayTitle = newTitle || '(Unbenannt)';

                        if (newTitle !== currentTitle && (newTitle !== '' || currentTitle !== '(Unbenannt)')) {
                            // Nur speichern, wenn der Titel geändert wurde
                            await saveTitleToBackend(newTitle);
                        }

                        activityTitle.textContent = displayTitle;
                        // Ersetze das Eingabefeld wieder durch das span-Element
                        inputField.parentNode.replaceChild(activityTitle, inputField);
                    });

                    // Event-Listener für die 'Enter'-Taste im Eingabefeld
                    inputField.addEventListener('keypress', function(event) {
                        if (event.key === 'Enter') {
                            inputField.blur(); // Simuliere einen Fokusverlust, um das Speichern auszulösen
                        }
                    });

                    // Event-Listener für die 'Escape'-Taste zum Abbrechen
                    inputField.addEventListener('keydown', function(event) {
                        if (event.key === 'Escape') {
                            activityTitle.textContent = currentTitle;
                            inputField.parentNode.replaceChild(activityTitle, inputField);
                        }
                    });
                });
            }
        });
    </script>
    <script src="/js/edit_activity_page.js" defer></script>

    <!-- Google Photos Picker Integration (vereinfacht) -->
    <link rel="stylesheet" href="/css/google-photos-picker.css">
    <script src="/js/googlePhotosPickerCorrect.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', async function() {
            const activityData = window.PAGE_ACTIVITY_DATA;
            const simpleGooglePhotosBtn = document.getElementById('simpleGooglePhotosBtn');
            const pickerContainer = document.getElementById('google-photos-picker-container');
            const statusElement = document.getElementById('google-photos-status');

            if (simpleGooglePhotosBtn && activityData) {
                simpleGooglePhotosBtn.addEventListener('click', async function() {
                    try {
                        // Zeige Loading
                        statusElement.textContent = 'Erstelle Google Photos Picker Session...';
                        statusElement.className = 'google-photos-status loading';
                        statusElement.style.display = 'block';

                        // Erstelle Picker Session direkt mit bestehender Authentifizierung
                        const response = await fetch('/user/api/google-photos-picker/session', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            }
                        });

                        const result = await response.json();

                        if (!response.ok || !result.success) {
                            throw new Error(result.error || 'Fehler beim Erstellen der Picker Session');
                        }

                        // Zeige Picker Link
                        pickerContainer.style.display = 'block';
                        pickerContainer.innerHTML = `
                            <div class="picker-session-info">
                                <h4>📷 Google Photos Picker bereit</h4>
                                <p>Klicke auf den Link, um deine Google Photos zu öffnen:</p>
                                <div class="picker-actions">
                                    <a href="${result.pickerUrl}" target="_blank" class="btn btn-primary">
                                        🔗 Google Photos öffnen
                                    </a>
                                </div>
                                <div class="picker-instructions">
                                    <small>
                                        <strong>Anleitung:</strong> Wähle deine Fotos in Google Photos aus und bestätige die Auswahl.
                                    </small>
                                </div>
                            </div>
                        `;

                        statusElement.textContent = 'Picker bereit! Klicke auf den Link oben.';
                        statusElement.className = 'google-photos-status success';

                    } catch (error) {
                        console.error('Fehler beim Erstellen der Picker Session:', error);
                        statusElement.textContent = 'Fehler: ' + error.message;
                        statusElement.className = 'google-photos-status error';

                        if (error.message.includes('Keine Google Photos Verbindung')) {
                            statusElement.innerHTML = 'Fehler: Keine Google Photos Verbindung gefunden. <a href="/user/settings" target="_blank">Jetzt in den Einstellungen verbinden</a>';
                        }
                    }
                });
            }
        });
    </script>
<%_ %>
