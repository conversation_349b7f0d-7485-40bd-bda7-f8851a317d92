<%# views/users/settings.ejs %>
<%# Das Layout wird global in server.ts auf 'layouts/main_layout' (oder Ihr Äquivalent) gesetzt. %>

<% pageTitle = 'Meine Einstellungen' %>
<% pageSpecificClass = 'user-settings-page' %>

<%# Seitenspezifische Stylesheets (optional) %>
<%_ contentFor('pageStylesheets') %>
    <%# <link rel="stylesheet" href="/css/user_settings_specific.css"> %>
    <style>
        /* Kollabierbare Sektionen */
        .settings-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
        }

        .collapsible-section {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background-color: #ffffff;
            overflow: hidden;
            transition: box-shadow 0.2s ease;
        }

        .collapsible-section:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .section-header {
            background-color: #f8f9fa;
            padding: 20px;
            cursor: pointer;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background-color 0.2s ease;
            user-select: none;
        }

        .section-header:hover {
            background-color: #e9ecef;
        }

        .section-header h3 {
            margin: 0;
            font-size: 1.3em;
            color: #495057;
            font-weight: 600;
        }

        .section-summary {
            font-size: 0.9em;
            color: #6c757d;
            margin-top: 5px;
        }

        .collapse-icon {
            font-size: 1.2em;
            color: #6c757d;
            transition: transform 0.3s ease;
        }

        .section-header.expanded .collapse-icon {
            transform: rotate(180deg);
        }

        .section-content {
            padding: 0 20px;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease, padding 0.3s ease;
        }

        .section-content.expanded {
            max-height: 2000px;
            padding: 20px;
        }

        .api-connections-grid {
            display: grid;
            gap: 20px;
            grid-template-columns: 1fr;
        }

        @media (min-width: 768px) {
            .api-connections-grid {
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            }
        }

        /* Status-Badges für die Zusammenfassung */
        .status-badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 500;
            margin-left: 8px;
        }

        .status-connected {
            background-color: #d1e7dd;
            color: #0f5132;
        }

        .status-disconnected {
            background-color: #f8d7da;
            color: #842029;
        }

        .status-partial {
            background-color: #fff3cd;
            color: #664d03;
        }

        /* Kartenvorschau Styles */
        #mapDefaultPreview {
            height: 200px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-top: 10px;
        }
        .api-connection-card {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            background: #fafafa;
            transition: all 0.2s ease;
        }
        .api-connection-card:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-color: #ccc;
        }
        .api-connection-card.connected {
            border-color: #28a745;
            background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
        }
        .api-connection-card.disconnected {
            border-color: #dc3545;
            background: linear-gradient(135deg, #fff8f8 0%, #f5e8e8 100%);
        }
        .api-connection-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
        }
        .api-connection-title {
            font-size: 1.1em;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .api-connection-status {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            padding: 4px 12px;
            font-size: 0.85em;
            font-weight: 600;
            border-radius: 20px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .status-connected {
            background-color: #28a745;
            color: white;
        }
        .status-disconnected {
            background-color: #dc3545;
            color: white;
        }
        .api-connection-info {
            margin-bottom: 15px;
            font-size: 0.9em;
            color: #666;
        }
        .api-connection-actions {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        .api-connection-actions .button {
            font-size: 0.85em;
            padding: 6px 12px;
        }

        #mapDefaultPreview { height: 250px; border: 1px solid #ccc; border-radius: 4px; margin-top:10px; }
    </style>
<%_ %>

<h2 class="page-main-title"><%= locals.pageTitle %></h2>

<div class="page-specific-content">

    <form action="/user/settings" method="POST">
        <div class="settings-grid">

            <!-- Profilinformationen -->
            <div class="collapsible-section">
                <div class="section-header" onclick="toggleSection('profile')">
                    <div>
                        <h3>Profilinformationen</h3>
                        <div class="section-summary">
                            Benutzername: <%= locals.userData ? userData.username : 'Nicht verfügbar' %>
                            <% if (locals.userData && userData.email) { %>
                                • E-Mail: <%= userData.email %>
                            <% } else { %>
                                • Keine E-Mail hinterlegt
                            <% } %>
                        </div>
                    </div>
                    <span class="collapse-icon">▼</span>
                </div>
                <div class="section-content" id="profile-content">
                    <div class="form-group">
                        <label for="username_display">Benutzername:</label>
                        <input type="text" id="username_display" name="username_display" value="<%= locals.userData ? userData.username : '' %>" disabled>
                        <small>Der Benutzername kann nicht geändert werden.</small>
                    </div>
                    <div class="form-group">
                        <label for="email">E-Mail-Adresse:</label>
                        <input type="email" id="email" name="email" value="<%= locals.userData && userData.email ? userData.email : '' %>" placeholder="<EMAIL>">
                        <small>Wird für Benachrichtigungen verwendet (optional, falls du welche erhalten möchtest).</small>
                    </div>
                    <div class="form-group">
                        <label>Passwort:</label>
                        <button type="button" class="button button-secondary button-link-small" onclick="alert('Passwort ändern Funktion noch nicht implementiert.')">Passwort ändern...</button>
                        <small>Hier können Sie später Ihr Passwort ändern.</small>
                    </div>
                </div>
            </div>

            <!-- API Verbindungen -->
            <div class="collapsible-section">
                <div class="section-header" onclick="toggleSection('api')">
                    <div>
                        <h3>API Verbindungen</h3>
                        <div class="section-summary">
                            <%
                                let connectedCount = 0;
                                let totalCount = 5;
                                if (locals.stravaConnected) connectedCount++;
                                if (locals.googleConnected) connectedCount++;
                                if (locals.googleDriveConnected) connectedCount++;
                                if (locals.komootConnected) connectedCount++;
                                if (locals.garminConnected) connectedCount++;
                            %>
                            <%= connectedCount %> von <%= totalCount %> Diensten verbunden
                            <% if (locals.stravaConnected) { %><span class="status-badge status-connected">Strava</span><% } %>
                            <% if (locals.googleConnected) { %><span class="status-badge status-connected">Google Photos</span><% } %>
                            <% if (locals.googleDriveConnected) { %><span class="status-badge status-connected">Google Drive</span><% } %>
                            <% if (locals.komootConnected) { %><span class="status-badge status-connected">Komoot</span><% } %>
                            <% if (locals.garminConnected) { %><span class="status-badge status-connected">Garmin</span><% } %>
                        </div>
                    </div>
                    <span class="collapse-icon">▼</span>
                </div>
                <div class="section-content" id="api-content">
                    <div class="api-connections-grid">

                    <%# Strava %>
                    <div class="api-connection-card <%= locals.stravaConnected ? 'connected' : 'disconnected' %>">
                        <div class="api-connection-header">
                            <h4 class="api-connection-title">
                                <i class="fab fa-strava" style="color: #FC4C02;"></i>
                                Strava
                            </h4>
                            <span class="api-connection-status <%= locals.stravaConnected ? 'status-connected' : 'status-disconnected' %>">
                                <i class="fas <%= locals.stravaConnected ? 'fa-check-circle' : 'fa-times-circle' %>"></i>
                                <%= locals.stravaConnected ? 'Verbunden' : 'Nicht verbunden' %>
                            </span>
                        </div>
                        <% if (locals.stravaConnected) { %>
                            <div class="api-connection-info">
                                Athlete ID: <%= locals.userData && userData.strava_athlete_id ? userData.strava_athlete_id : 'N/A' %>
                            </div>
                            <div class="api-connection-actions">
                                <a href="/auth/strava/login" class="button button-secondary">Erneut verbinden</a>
                            </div>
                        <% } else { %>
                            <div class="api-connection-info">
                                Verbinden Sie Ihr Strava-Konto, um Aktivitäten zu synchronisieren.
                            </div>
                            <div class="api-connection-actions">
                                <a href="/auth/strava/login" class="button">Mit Strava verbinden</a>
                            </div>
                        <% } %>
                    </div>

                    <%# Google Photos (Legacy) %>
                    <div class="api-connection-card <%= locals.googleConnected ? 'connected' : 'disconnected' %>">
                        <div class="api-connection-header">
                            <h4 class="api-connection-title">
                                <i class="fab fa-google" style="color: #4285F4;"></i>
                                Google Photos (Legacy)
                            </h4>
                            <span class="api-connection-status <%= locals.googleConnected ? 'status-connected' : 'status-disconnected' %>">
                                <i class="fas <%= locals.googleConnected ? 'fa-check-circle' : 'fa-times-circle' %>"></i>
                                <%= locals.googleConnected ? 'Verbunden' : 'Nicht verbunden' %>
                            </span>
                        </div>
                        <div class="api-connection-info">
                            <strong>⚠️ Veraltet:</strong> Diese API funktioniert seit April 2025 nicht mehr. Verwende stattdessen Google Photos Picker.
                        </div>
                        <% if (locals.googleConnected) { %>
                            <div class="api-connection-actions">
                                <a href="/auth/google/disconnect" class="button button-danger">Trennen</a>
                            </div>
                        <% } else { %>
                            <div class="api-connection-actions">
                                <button class="button button-secondary" disabled>Nicht mehr verfügbar</button>
                            </div>
                        <% } %>
                    </div>

                    <%# Google Photos Picker (neue API) %>
                    <div class="api-connection-card" id="google-photos-picker-card">
                        <div class="api-connection-header">
                            <h4 class="api-connection-title">
                                <i class="fab fa-google" style="color: #4285F4;"></i>
                                Google Photos Picker
                            </h4>
                            <span class="api-connection-status" id="google-photos-picker-status">
                                <i class="fas fa-spinner fa-spin"></i>
                                Wird geladen...
                            </span>
                        </div>
                        <div class="api-connection-info">
                            Neue Google Photos Integration für die Fotoauswahl bei Aktivitäten (seit 2025).
                        </div>
                        <div class="api-connection-actions" id="google-photos-picker-actions">
                            <a href="/auth/google-photos-picker" class="button" id="google-photos-picker-connect" style="display: none;">
                                Mit Google Photos Picker verbinden
                            </a>
                            <form method="POST" action="/auth/google-photos-picker/disconnect" style="display: none;" id="google-photos-picker-disconnect-form">
                                <button type="submit" class="button button-danger">Trennen</button>
                            </form>
                        </div>
                        <div class="api-connection-info" id="google-photos-picker-details" style="display: none;">
                            <small><strong>Berechtigungen:</strong> <span id="google-photos-picker-scope"></span></small>
                        </div>
                    </div>

                    <%# Google Drive %>
                    <div class="api-connection-card <%= locals.googleDriveConnected ? 'connected' : 'disconnected' %>">
                        <div class="api-connection-header">
                            <h4 class="api-connection-title">
                                <i class="fab fa-google-drive" style="color: #0F9D58;"></i>
                                Google Drive
                            </h4>
                            <span class="api-connection-status <%= locals.googleDriveConnected ? 'status-connected' : 'status-disconnected' %>">
                                <i class="fas <%= locals.googleDriveConnected ? 'fa-check-circle' : 'fa-times-circle' %>"></i>
                                <%= locals.googleDriveConnected ? 'Verbunden' : 'Nicht verbunden' %>
                            </span>
                        </div>
                        <% if (locals.googleDriveConnected) { %>
                            <div class="api-connection-info">
                                Zugriff auf GPX-Dateien in Ihrem Google Drive.
                            </div>
                            <div class="api-connection-actions">
                                <a href="/user/google-drive/files" class="button">Dateien verwalten</a>
                                <a href="/auth/google-drive/login" class="button button-secondary">Erneut verbinden</a>
                                <a href="/auth/google-drive/disconnect" class="button button-danger">Trennen</a>
                            </div>
                        <% } else { %>
                            <div class="api-connection-info">
                                Verbinden Sie Google Drive, um GPX-Dateien zu importieren.
                            </div>
                            <div class="api-connection-actions">
                                <a href="/auth/google-drive/login" class="button">Mit Google Drive verbinden</a>
                            </div>
                        <% } %>
                    </div>

                    <%# Komoot %>
                    <div class="api-connection-card <%= locals.komootConnected ? 'connected' : 'disconnected' %>">
                        <div class="api-connection-header">
                            <h4 class="api-connection-title">
                                <i class="fas fa-route" style="color: #6AA127;"></i>
                                Komoot
                            </h4>
                            <span class="api-connection-status <%= locals.komootConnected ? 'status-connected' : 'status-disconnected' %>">
                                <i class="fas <%= locals.komootConnected ? 'fa-check-circle' : 'fa-times-circle' %>"></i>
                                <%= locals.komootConnected ? 'Verbunden' : 'Nicht verbunden' %>
                            </span>
                        </div>
                        <% if (locals.komootConnected) { %>
                            <div class="api-connection-info">
                                Synchronisation von Komoot-Touren aktiviert.
                            </div>
                            <div class="api-connection-actions">
                                <a href="/user/komoot/settings" class="button">Einstellungen verwalten</a>
                            </div>
                        <% } else { %>
                            <div class="api-connection-info">
                                Verbinden Sie Komoot, um Touren zu synchronisieren.
                            </div>
                            <div class="api-connection-actions">
                                <a href="/user/komoot/settings" class="button">Mit Komoot verbinden</a>
                            </div>
                        <% } %>
                    </div>

                    <%# Garmin Connect %>
                    <div class="api-connection-card <%= locals.garminConnected ? 'connected' : 'disconnected' %>">
                        <div class="api-connection-header">
                            <h4 class="api-connection-title">
                                <i class="fas fa-watch" style="color: #007CC3;"></i>
                                Garmin Connect
                            </h4>
                            <span class="api-connection-status <%= locals.garminConnected ? 'status-connected' : 'status-disconnected' %>">
                                <i class="fas <%= locals.garminConnected ? 'fa-check-circle' : 'fa-times-circle' %>"></i>
                                <%= locals.garminConnected ? 'Verbunden' : 'Nicht verbunden' %>
                            </span>
                        </div>
                        <% if (locals.garminConnected) { %>
                            <div class="api-connection-info">
                                Synchronisation von Garmin Connect-Aktivitäten aktiviert.
                            </div>
                            <div class="api-connection-actions">
                                <a href="/user/garmin/activities" class="button">Aktivitäten anzeigen</a>
                                <a href="/user/garmin/sync" class="button button-success">Synchronisieren</a>
                                <a href="/user/garmin/settings" class="button button-secondary">Einstellungen</a>
                            </div>
                        <% } else { %>
                            <div class="api-connection-info">
                                Verbinden Sie Garmin Connect, um Aktivitäten zu synchronisieren.
                            </div>
                            <div class="api-connection-actions">
                                <a href="/user/garmin/settings" class="button">Mit Garmin Connect verbinden</a>
                            </div>
                        <% } %>
                    </div>

                    </div>
                    <p style="margin-top: 20px;"><small><i class="fas fa-info-circle"></i> Das erneute Verbinden kann erforderlich sein, wenn Berechtigungen abgelaufen sind oder geändert werden sollen.</small></p>
                </div>
            </div>

            <!-- Karten-Einstellungen -->
            <div class="collapsible-section">
                <div class="section-header" onclick="toggleSection('map')">
                    <div>
                        <h3>Karten-Einstellungen</h3>
                        <div class="section-summary">
                            Sichtbarkeit: <%= locals.userData && userData.map_visibility === 'public' ? 'Öffentlich' : 'Privat' %>
                            <% if (locals.userData && userData.default_map_zoom) { %>
                                • Zoom: <%= userData.default_map_zoom %>
                            <% } %>
                            <% if (locals.userData && userData.default_map_center_lat && userData.default_map_center_lng) { %>
                                • Zentrum: <%= parseFloat(userData.default_map_center_lat).toFixed(3) %>, <%= parseFloat(userData.default_map_center_lng).toFixed(3) %>
                            <% } %>
                        </div>
                    </div>
                    <span class="collapse-icon">▼</span>
                </div>
                <div class="section-content" id="map-content">
                    <div class="form-group">
                        <label for="map_visibility">Sichtbarkeit meiner öffentlichen Karte (<a href="/show/map/<%= currentUser.username %>" target="_blank">/show/map/<%= currentUser.username %></a>):</label>
                        <select id="map_visibility" name="map_visibility">
                            <option value="private" <%= locals.userData && userData.map_visibility === 'private' ? 'selected' : '' %>>Privat (nur ich sehe meine Karte unter /user/.../map)</option>
                            <option value="public" <%= locals.userData && userData.map_visibility === 'public' ? 'selected' : '' %>>Öffentlich (jeder mit dem Link kann die Karte sehen)</option>
                        </select>
                    </div>
                <div class="form-group">
                    <label for="default_map_zoom">Standard Zoom (für "Meine Karte"):</label>
                    <input type="number" id="default_map_zoom" name="default_map_zoom" min="1" max="20" value="<%= locals.userData && userData.default_map_zoom ? userData.default_map_zoom : '' %>" placeholder="z.B. 12">
                </div>
                <div class="form-group">
                    <label for="default_map_center_lat">Standard Kartenmitte (Latitude):</label>
                    <input type="text" id="default_map_center_lat" name="default_map_center_lat" value="<%= locals.userData && userData.default_map_center_lat ? userData.default_map_center_lat : '' %>" placeholder="z.B. 48.12345">
                </div>
                <div class="form-group">
                    <label for="default_map_center_lng">Standard Kartenmitte (Longitude):</label>
                    <input type="text" id="default_map_center_lng" name="default_map_center_lng" value="<%= locals.userData && userData.default_map_center_lng ? userData.default_map_center_lng : '' %>" placeholder="z.B. 8.12345">
                </div>
                <div class="form-group">
                    <label>Interaktive Karteneinstellung (Vorschau):</label>
                    <div id="mapDefaultPreview"></div>
                    <button type="button" id="setFromPreviewBtn" class="button-link-small button-secondary" style="margin-top: 5px;">Aktuelle Kartenansicht als Standard setzen</button>
                    <small>Zoomen und verschieben Sie die Karte oben, dann klicken Sie den Button.</small>
                </div>
                </div>
            </div>

            <!-- E-Mail Benachrichtigungen -->
            <div class="collapsible-section">
                <div class="section-header" onclick="toggleSection('notifications')">
                    <div>
                        <h3>E-Mail Benachrichtigungen</h3>
                        <div class="section-summary">
                            <%
                                let notificationCount = 0;
                                if (locals.userData && userData.notify_on_friend_request_email) notificationCount++;
                                if (locals.userData && userData.notify_on_activity_shared_email) notificationCount++;
                            %>
                            <%= notificationCount %> von 2 Benachrichtigungen aktiviert
                            <% if (!locals.userData || !userData.email) { %>
                                <span class="status-badge status-disconnected">Keine E-Mail hinterlegt</span>
                            <% } %>
                        </div>
                    </div>
                    <span class="collapse-icon">▼</span>
                </div>
                <div class="section-content" id="notifications-content">
                    <div class="form-group">
                        <label>
                            <input type="checkbox" name="notify_on_friend_request_email" value="true" <%= locals.userData && userData.notify_on_friend_request_email ? 'checked' : '' %>>
                            Bei neuen Freundschaftsanfragen
                        </label>
                        <input type="hidden" name="notify_on_friend_request_email_was_submitted" value="1">
                    </div>
                     <div class="form-group">
                        <label>
                            <input type="checkbox" name="notify_on_activity_shared_email" value="true" <%= locals.userData && userData.notify_on_activity_shared_email ? 'checked' : '' %>>
                            Wenn eine Aktivität mit mir geteilt wird
                        </label>
                        <input type="hidden" name="notify_on_activity_shared_email_was_submitted" value="1">
                    </div>
                    <%# Weitere Benachrichtigungstypen hier %>
                </div>
            </div>

            <!-- Erweiterte Einstellungen -->
            <div class="collapsible-section">
                <div class="section-header" onclick="toggleSection('advanced')">
                    <div>
                        <h3>Erweiterte Einstellungen</h3>
                        <div class="section-summary">
                            Sportgruppen, Kartenstile und weitere Anpassungen
                        </div>
                    </div>
                    <span class="collapse-icon">▼</span>
                </div>
                <div class="section-content" id="advanced-content">
                    <div class="form-group">
                        <label>Sportart-Obergruppen definieren:</label>
                        <a href="/user/sport-groups" class="button button-secondary button-link-small">Gruppen verwalten</a>
                        <small>Definiere eigene Gruppen wie "Ausdauersport", "Wintersport" und ordne Sportarten zu.</small>
                    </div>
                    <div class="form-group">
                        <label>Karten-Layer-Stile anpassen:</label>
                        <a href="/user/map-styles" class="button button-secondary button-link-small">Stile anpassen</a>
                        <small>Passe Farben, Linienstärken etc. für verschiedene Aktivitätstypen auf deiner Karte an.</small>
                    </div>
                </div>
            </div>

        </div> <%# Ende .settings-grid %>

        <div style="margin-top: 30px; padding-top:20px; border-top: 1px solid #eee;">
            <button type="submit" class="button button-success">Alle Einstellungen speichern</button>
        </div>
    </form>
</div>

<%# Seitenspezifische Skripte %>
<%_ contentFor('pageScripts') %>
    <%# Leaflet JS für die Kartenvorschau (nur laden, wenn diese Seite aktiv ist) %>
    <link rel="stylesheet" href="/libs/leaflet/leaflet.css" crossorigin=""/>
    <script src="/libs/leaflet/leaflet.js" crossorigin=""></script>
    <script>
        // Kollabierbare Sektionen
        function toggleSection(sectionId) {
            const content = document.getElementById(sectionId + '-content');
            const header = content.previousElementSibling;
            const icon = header.querySelector('.collapse-icon');

            if (content.classList.contains('expanded')) {
                content.classList.remove('expanded');
                header.classList.remove('expanded');
            } else {
                content.classList.add('expanded');
                header.classList.add('expanded');
            }

            // Speichere den Zustand im localStorage
            const expandedSections = JSON.parse(localStorage.getItem('expandedSections') || '[]');
            const index = expandedSections.indexOf(sectionId);

            if (content.classList.contains('expanded')) {
                if (index === -1) expandedSections.push(sectionId);
            } else {
                if (index > -1) expandedSections.splice(index, 1);
            }

            localStorage.setItem('expandedSections', JSON.stringify(expandedSections));
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Lade gespeicherte Zustände
            const expandedSections = JSON.parse(localStorage.getItem('expandedSections') || '["profile"]'); // Profil standardmäßig geöffnet

            expandedSections.forEach(sectionId => {
                const content = document.getElementById(sectionId + '-content');
                const header = content?.previousElementSibling;

                if (content && header) {
                    content.classList.add('expanded');
                    header.classList.add('expanded');
                }
            });

            // Logik für die interaktive Kartenvorschau
            const latInput = document.getElementById('default_map_center_lat');
            const lngInput = document.getElementById('default_map_center_lng');
            const zoomInput = document.getElementById('default_map_zoom');
            const previewMapDiv = document.getElementById('mapDefaultPreview');
            const setFromPreviewBtn = document.getElementById('setFromPreviewBtn');

            if (previewMapDiv && latInput && lngInput && zoomInput && setFromPreviewBtn && typeof L !== 'undefined') {
                let currentLat = parseFloat(latInput.value) || (window.APP_CONFIG_USER_SETTINGS && window.APP_CONFIG_USER_SETTINGS.globalDefaultMapCenterLat) || 48.1114;
                let currentLng = parseFloat(lngInput.value) || (window.APP_CONFIG_USER_SETTINGS && window.APP_CONFIG_USER_SETTINGS.globalDefaultMapCenterLng) || 8.5058;
                let currentZoom = parseInt(zoomInput.value, 10) || (window.APP_CONFIG_USER_SETTINGS && window.APP_CONFIG_USER_SETTINGS.globalDefaultMapZoom) || 12;

                const previewMap = L.map(previewMapDiv).setView([currentLat, currentLng], currentZoom);

                // Verwende einen BaseLayer aus der globalen Config
                const baseLayersConfig = (window.APP_CONFIG_USER_SETTINGS && window.APP_CONFIG_USER_SETTINGS.baseLayers) || [{name: "OSM", url: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', options:{}}];
                const activeBaseLayer = baseLayersConfig.find(l => l.active) || baseLayersConfig[0];
                if(activeBaseLayer) {
                    L.tileLayer(activeBaseLayer.url, activeBaseLayer.options).addTo(previewMap);
                }


                previewMap.on('moveend zoomend', function() {
                    // Optional: Felder direkt aktualisieren oder nur beim Klick auf den Button
                    // latInput.value = previewMap.getCenter().lat.toFixed(7);
                    // lngInput.value = previewMap.getCenter().lng.toFixed(7);
                    // zoomInput.value = previewMap.getZoom();
                });

                setFromPreviewBtn.addEventListener('click', function() {
                    latInput.value = previewMap.getCenter().lat.toFixed(7);
                    lngInput.value = previewMap.getCenter().lng.toFixed(7);
                    zoomInput.value = previewMap.getZoom();
                    alert('Aktuelle Kartenansicht wurde in die Formularfelder übernommen. Bitte speichern Sie die Einstellungen, um sie zu übernehmen.');
                });

                // Wenn die Formularfelder geändert werden, Karte aktualisieren
                [latInput, lngInput, zoomInput].forEach(input => {
                    input.addEventListener('change', () => {
                        const newLat = parseFloat(latInput.value);
                        const newLng = parseFloat(lngInput.value);
                        const newZoom = parseInt(zoomInput.value, 10);
                        if (!isNaN(newLat) && !isNaN(newLng) && !isNaN(newZoom)) {
                            previewMap.setView([newLat, newLng], newZoom);
                        }
                    });
                });

            } else {
                console.warn("Elemente für Kartenvorschau nicht gefunden oder Leaflet nicht geladen.");
                if(previewMapDiv) previewMapDiv.innerHTML = "<p style='text-align:center; padding-top:20px; color:#777;'>Kartenvorschau konnte nicht geladen werden.</p>";
            }

            // Globale Config für diese Seite (wird vom Controller befüllt)
            // window.APP_CONFIG_USER_SETTINGS enthält z.B. globale Map-Defaults
            // console.log("USER SETTINGS PAGE: APP_CONFIG_USER_SETTINGS:", JSON.stringify(window.APP_CONFIG_USER_SETTINGS, null, 2));

            // Lade Google Photos Picker Status
            loadGooglePhotosPickerStatus();
        });

        // Google Photos Picker Status laden
        async function loadGooglePhotosPickerStatus() {
            try {
                const response = await fetch('/auth/google-photos-picker/status');
                const status = await response.json();

                const card = document.getElementById('google-photos-picker-card');
                const statusElement = document.getElementById('google-photos-picker-status');
                const connectBtn = document.getElementById('google-photos-picker-connect');
                const disconnectForm = document.getElementById('google-photos-picker-disconnect-form');
                const detailsElement = document.getElementById('google-photos-picker-details');
                const scopeElement = document.getElementById('google-photos-picker-scope');

                if (status.connected) {
                    // Verbunden
                    card.className = 'api-connection-card connected';
                    statusElement.innerHTML = '<i class="fas fa-check-circle"></i> Verbunden';
                    statusElement.className = 'api-connection-status status-connected';
                    connectBtn.style.display = 'none';
                    disconnectForm.style.display = 'block';

                    if (status.scope) {
                        detailsElement.style.display = 'block';
                        scopeElement.textContent = status.scope;
                    }
                } else {
                    // Nicht verbunden
                    card.className = 'api-connection-card disconnected';
                    statusElement.innerHTML = '<i class="fas fa-times-circle"></i> Nicht verbunden';
                    statusElement.className = 'api-connection-status status-disconnected';
                    connectBtn.style.display = 'block';
                    disconnectForm.style.display = 'none';
                    detailsElement.style.display = 'none';
                }

            } catch (error) {
                console.error('Fehler beim Laden des Google Photos Picker Status:', error);
                const statusElement = document.getElementById('google-photos-picker-status');
                statusElement.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Fehler beim Laden';
                statusElement.className = 'api-connection-status status-disconnected';
            }
        }
    </script>
<%_ %>
