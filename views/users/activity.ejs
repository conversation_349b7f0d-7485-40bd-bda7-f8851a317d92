<%# views/users/activity_detail.ejs %>
<%# Das Layout 'layouts/main_layout' wird verwendet. %>

<%# Seitenspezifische Variablen für das Layout definieren %>
<% pageTitle = (locals.activity ? (activity.activity_name || `ID ${activity.id}`) : 'Unbekannt') %>
<% pageSpecificClass = 'user-activity-detail-page' %>

<%# Seitenspezifische Stylesheets (optional) %>
<%_ contentFor('pageStylesheets') %>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <link rel="stylesheet" href="/css/activity_map_with_charts.css" />
    <link rel="stylesheet" href="/css/mobile_layout.css" />
    <link rel="stylesheet" href="/css/activity.css" />
<%_ %>

<h2 class="page-main-title"><%= locals.pageTitle %></h2>

<div class="page-specific-content">
    <% if (locals.activity) { %>
        <% if (!locals.isPublicView) { %>
        <!-- Desktop Action Links -->
        <div class="action-links">
            <p>
                <% if (locals.currentUser && currentUser.username) { %>
                    <a href="/user/<%= encodeURIComponent(currentUser.username) %>/map" class="button-link-small">&laquo; Meine Karte</a> |
                    <a href="/user/activities" class="button-link-small">&laquo; Meine Aktivitäten</a>
                    <a href="/show/activity_pi_control/<%= activity.id %>" class="button-link-small" target="_blank">3D Animation</a>
                <% } %>
                <% if (isOwner) { %>
                     | <a href="/user/activity/<%= activity.id %>/edit" class="button-link-small">Bearbeiten & Ausrüstung</a>
                     | <a href="#" class="button-link-small button-success" id="shareActivityBtn">Mit Freunden teilen</a>
                     | <a href="#" class="button-link-small button-info" id="addToTripBtn">Zu Reise hinzufügen</a>
                     | <a href="#" class="button-link-small button-danger" id="deleteActivityBtn">Aktivität löschen</a>
                <% } %>
            </p>
        </div>

        <!-- Mobile Action Links - Kompakte Version -->
        <div class="mobile-action-links">
            <div class="mobile-action-compact">
                <div class="mobile-action-row">
                    <% if (locals.currentUser && currentUser.username) { %>
                    <a href="/user/<%= encodeURIComponent(currentUser.username) %>/map" class="mobile-action-btn-compact secondary">
                        🗺️ Karte
                    </a>
                    <a href="/user/activities" class="mobile-action-btn-compact secondary">
                        📋 Aktivitäten
                    </a>
                    <% } %>
                    <a href="/show/activity_pi_control/<%= activity.id %>" class="mobile-action-btn-compact primary" target="_blank">
                        🎬 3D
                    </a>
                    <% if (isOwner) { %>
                    <!-- Dropdown für weitere Aktionen -->
                    <div class="mobile-action-dropdown">
                        <button class="mobile-action-btn-compact" id="moreActionsBtn">
                            ⋯ Mehr
                        </button>
                        <div class="mobile-action-dropdown-content" id="moreActionsDropdown">
                            <a href="/user/activity/<%= activity.id %>/edit">✏️ Bearbeiten & Ausrüstung</a>
                            <a href="#" id="shareActivityBtnMobile">👥 Mit Freunden teilen</a>
                            <a href="#" id="addToTripBtnMobile">🧳 Zu Reise hinzufügen</a>
                            <a href="#" id="deleteActivityBtnMobile" style="color: #dc3545;">🗑️ Aktivität löschen</a>
                        </div>
                    </div>
                    <% } %>
                </div>
            </div>
        </div>
        <% } else { %>
        <div class="action-links">
            <p>
                <em>Öffentliche Ansicht einer Aktivität</em>
                <% if (activity.id) { %>
                    <a href="/show/activity_pi_control/<%= activity.id %>" class="button-link-small" target="_blank">3D Animation</a>
                <% } %>
            </p>
        </div>
        <% } %>

        <section class="content-section activity-map-section">
            <h3>Aktivitätskarte</h3>
            <%- include('../partials/activity_map_with_charts', { activity: activity }) %>
        </section>

        <div class="detail-grid">
            <section class="content-section activity-info">
                <h3>Aktivitätsdaten</h3>
                <dl>
                    <dt>Name:</dt>
                    <dd>
                        <% if (isOwner) { %>
                        <div class="title-container">
                            <span id="activityTitle" class="editable-title"><%= activity.activity_name || '(Unbenannt)' %></span>
                            <i id="editTitleIcon" class="fas fa-pencil-alt edit-icon" title="Titel bearbeiten"></i>
                        </div>
                        <% } else { %>
                        <%= activity.activity_name || '(Unbenannt)' %>
                        <% } %>
                    </dd>
                    <dt>Datum:</dt><dd><%= activity.start_date_local ? new Date(activity.start_date_local).toLocaleString('de-DE', {dateStyle:'full', timeStyle:'short'}) : 'N/A' %></dd>
                    <dt>Sportart:</dt><dd><%= activity.sport_type || 'N/A' %></dd>
                    <dt>Distanz:</dt><dd><%= activity.distance != null ? (activity.distance / 1000).toFixed(2) + ' km' : 'N/A' %></dd>
                    <dt>Höhenmeter:</dt><dd><%= activity.total_elevation_gain != null ? Math.round(activity.total_elevation_gain) + ' m' : 'N/A' %></dd>
                    <dt>Bewegungszeit:</dt><dd>
                        <% if (activity.moving_time != null) {
                            const hours = Math.floor(activity.moving_time / 3600);
                            const minutes = Math.floor((activity.moving_time % 3600) / 60);
                            %><%= hours > 0 ? hours + 'h ' : '' %><%= minutes %>m<%
                        } else { %>N/A<% } %>
                    </dd>
                    <% if (activity.private_note) { %>
                        <dt>Private Notiz:</dt><dd><pre><%= activity.private_note %></pre></dd>
                    <% } %>
                    <% if (activity.strava_id) { %>
                        <dt>Strava ID:</dt>
                        <dd>
                            <a href="https://www.strava.com/activities/<%= activity.strava_id %>" target="_blank"><%= activity.strava_id %></a>
                        </dd>
                    <% } %>
                    <% if (isOwner && activity.share_uuid) { %>
                        <dt>Öffentlicher Link:</dt>
                        <dd>
                            <div class="share-link-container">
                                <input type="text" id="shareLink" value="https://map.mdiehm.org/show/activity/<%=activity.share_uuid%>" readonly class="share-link-input">
                                <button type="button" id="copyShareLinkBtn" class="button-link-small">Kopieren</button>
                            </div>
                            <small class="share-link-note">Dieser Link kann mit jedem geteilt werden, auch ohne Anmeldung.</small>
                        </dd>
                    <% } %>
                </dl>
            </section>

            <section class="content-section equipment-section">
                <h3>Verwendete Ausrüstung</h3>
                <% if (locals.linkedEquipmentDetails && linkedEquipmentDetails.length > 0) { %>
                    <div class="linked-equipment-display">
                        <ul>
                            <% linkedEquipmentDetails.forEach(item => { %>
                                <li>
                                    <strong><%= item.name %></strong> (<%= item.type || 'Allgemein' %>)
                                    <% if (item.notes) { %><small>Notiz: <%= item.notes %></small><% } %>
                                </li>
                            <% }); %>
                        </ul>
                    </div>
                <% } else { %>
                    <p class="no-equipment">Keine Ausrüstung für diese Aktivität ausgewählt.</p>
                <% } %>

                <% if (isOwner) { %>
                    <p style="margin-top: 15px;">
                        <a href="/user/activity/<%= activity.id %>/edit" class="button-link-small">Bearbeiten & Ausrüstung</a>
                    </p>
                <% } %>
            </section>
        </div>

      
    <% } else { %>
        <div class="message error-message">Aktivitätsdaten konnten nicht geladen werden oder die Aktivität existiert nicht.</div>
    <% } %>
</div>

<%# Modal für das Teilen mit Freunden %>
<div id="shareActivityModal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h3>Aktivität mit Freunden teilen</h3>
        <p>Wähle die Freunde aus, mit denen du diese Aktivität teilen möchtest:</p>
        <form id="shareActivityForm" action="/user/activity/<%= activity.id %>/share" method="POST">
            <div id="friendsList" style="margin: 15px 0; max-height: 300px; overflow-y: auto;">
                <p>Lade Freundesliste...</p>
            </div>
            <div class="form-actions">
                <button type="submit" class="button button-success">Teilen</button>
                <button type="button" class="button button-secondary" id="cancelShareBtn">Abbrechen</button>
            </div>
        </form>
    </div>
</div>

<%# Modal für das Löschen der Aktivität %>
<div id="deleteActivityModal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h3>Aktivität löschen</h3>
        <p>Möchtest du diese Aktivität wirklich löschen?</p>
        <p><strong>Achtung:</strong> Diese Aktion kann nicht rückgängig gemacht werden. Alle Daten, GPX-Dateien und verknüpften Fotos werden unwiderruflich gelöscht.</p>

        <div class="activity-delete-info">
            <p><strong>Aktivität:</strong> <%= activity.activity_name || '(Unbenannt)' %></p>
            <p><strong>Datum:</strong> <%= activity.start_date_local ? new Date(activity.start_date_local).toLocaleString('de-DE', {dateStyle:'full', timeStyle:'short'}) : 'N/A' %></p>
            <p><strong>Sportart:</strong> <%= activity.sport_type || 'N/A' %></p>
            <p><strong>Distanz:</strong> <%= activity.distance != null ? (activity.distance / 1000).toFixed(2) + ' km' : 'N/A' %></p>
        </div>

        <form id="deleteActivityForm" action="/user/activities/delete/<%= activity.id %>" method="POST">
            <div class="form-actions">
                <button type="submit" class="button button-danger">Endgültig löschen</button>
                <button type="button" class="button button-secondary" id="cancelDeleteBtn">Abbrechen</button>
            </div>
        </form>
    </div>
</div>

<%# Seitenspezifische Skripte (optional) %>
<%_ contentFor('pageScripts') %>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-annotation@2.1.2/dist/chartjs-plugin-annotation.min.js"></script>
    <script>
      // Prüfe, ob die Plugins korrekt geladen wurden
      document.addEventListener('DOMContentLoaded', function() {
        console.log('Chart.js loaded:', typeof Chart !== 'undefined');
        console.log('Chart.js version:', Chart.version);
        console.log('Annotation plugin loaded:', typeof ChartAnnotation !== 'undefined' || (Chart && Chart.Annotation));
      });
    </script>
    <script src="/js/activityMapWithCharts.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Mobile Dropdown-Funktionalität
            const moreActionsBtn = document.getElementById('moreActionsBtn');
            const moreActionsDropdown = document.getElementById('moreActionsDropdown');

            if (moreActionsBtn && moreActionsDropdown) {
                moreActionsBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    const dropdown = moreActionsBtn.parentElement;
                    dropdown.classList.toggle('show');
                });

                // Schließe Dropdown wenn außerhalb geklickt wird
                document.addEventListener('click', function(e) {
                    if (!moreActionsBtn.contains(e.target)) {
                        const dropdown = moreActionsBtn.parentElement;
                        dropdown.classList.remove('show');
                    }
                });
            }

            // Teilen-Modal
            const shareBtn = document.getElementById('shareActivityBtn');
            const shareModal = document.getElementById('shareActivityModal');
            const shareCloseBtn = shareModal.querySelector('.close');
            const shareCancelBtn = document.getElementById('cancelShareBtn');
            const friendsList = document.getElementById('friendsList');

            // Löschen-Modal
            const deleteBtn = document.getElementById('deleteActivityBtn');
            const deleteModal = document.getElementById('deleteActivityModal');
            const deleteCloseBtn = deleteModal.querySelector('.close');
            const deleteCancelBtn = document.getElementById('cancelDeleteBtn');

            // Funktion zum Öffnen des Teilen-Modals
            function openShareModal() {
                shareModal.style.display = 'block';

                // Freundesliste laden
                fetch('/user/api/friends')
                    .then(response => response.json())
                    .then(data => {
                        if (data.friends && data.friends.length > 0) {
                            let html = '';
                            data.friends.forEach(friend => {
                                html += `
                                    <div class="friend-item">
                                        <label>
                                            <input type="checkbox" name="friendIds" value="${friend.friend_user_id}">
                                            ${friend.friend_username}
                                        </label>
                                    </div>
                                `;
                            });
                            friendsList.innerHTML = html;
                        } else {
                            friendsList.innerHTML = '<p>Du hast noch keine Freunde, mit denen du diese Aktivität teilen könntest.</p>';
                        }
                    })
                    .catch(error => {
                        console.error('Fehler beim Laden der Freundesliste:', error);
                        friendsList.innerHTML = '<p class="error">Fehler beim Laden der Freundesliste.</p>';
                    });
            }

            // Teilen-Modal öffnen (Desktop)
            shareBtn.addEventListener('click', function(e) {
                e.preventDefault();
                openShareModal();
            });

            // Teilen-Modal öffnen (Mobile)
            const shareActivityBtnMobile = document.getElementById('shareActivityBtnMobile');
            if (shareActivityBtnMobile) {
                shareActivityBtnMobile.addEventListener('click', function(e) {
                    e.preventDefault();
                    openShareModal();
                });
            }

            // Funktion zum Öffnen des Löschen-Modals
            function openDeleteModal() {
                deleteModal.style.display = 'block';
            }

            // Löschen-Modal öffnen (Desktop)
            if (deleteBtn) {
                deleteBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    openDeleteModal();
                });
            }

            // Löschen-Modal öffnen (Mobile)
            const deleteActivityBtnMobile = document.getElementById('deleteActivityBtnMobile');
            if (deleteActivityBtnMobile) {
                deleteActivityBtnMobile.addEventListener('click', function(e) {
                    e.preventDefault();
                    openDeleteModal();
                });
            }

            // Share-Link kopieren
            const copyShareLinkBtn = document.getElementById('copyShareLinkBtn');
            const shareLinkInput = document.getElementById('shareLink');

            if (copyShareLinkBtn && shareLinkInput) {
                copyShareLinkBtn.addEventListener('click', function() {
                    shareLinkInput.select();
                    shareLinkInput.setSelectionRange(0, 99999); // Für mobile Geräte

                    try {
                        document.execCommand('copy');
                        copyShareLinkBtn.textContent = 'Kopiert!';
                        copyShareLinkBtn.style.backgroundColor = '#28a745';

                        setTimeout(() => {
                            copyShareLinkBtn.textContent = 'Kopieren';
                            copyShareLinkBtn.style.backgroundColor = '';
                        }, 2000);
                    } catch (err) {
                        console.error('Fehler beim Kopieren:', err);
                        copyShareLinkBtn.textContent = 'Fehler';
                        setTimeout(() => {
                            copyShareLinkBtn.textContent = 'Kopieren';
                        }, 2000);
                    }
                });
            }

            // === INLINE TITLE EDITING ===
            <% if (isOwner) { %>
            const activityTitle = document.getElementById('activityTitle');
            const editTitleIcon = document.getElementById('editTitleIcon');

            if (activityTitle && editTitleIcon) {
                // Funktion zum Speichern des Titels im Backend
                async function saveTitleToBackend(newTitle) {
                    console.log('Titel wird im Backend gespeichert:', newTitle);
                    try {
                        const response = await fetch('/user/activity/<%= activity.id %>/update-title', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: 'activityTitle=' + encodeURIComponent(newTitle),
                        });

                        if (response.ok) {
                            console.log('Titel erfolgreich im Backend aktualisiert!');
                            // Optional: Kurze Bestätigung anzeigen
                            editTitleIcon.style.color = '#28a745';
                            setTimeout(() => {
                                editTitleIcon.style.color = '';
                            }, 1000);
                        } else {
                            console.error('Fehler beim Speichern des Titels im Backend:', response.statusText);
                            alert('Fehler beim Speichern des Titels. Bitte versuchen Sie es erneut.');
                        }
                    } catch (error) {
                        console.error('Netzwerkfehler beim Speichern des Titels:', error);
                        alert('Netzwerkfehler beim Speichern des Titels. Bitte versuchen Sie es erneut.');
                    }
                }

                // Event-Listener für das Stift-Icon
                editTitleIcon.addEventListener('click', function() {
                    // Ersetze den Titel durch ein Eingabefeld
                    const currentTitle = activityTitle.textContent;
                    const inputField = document.createElement('input');
                    inputField.type = 'text';
                    inputField.className = 'title-input';
                    inputField.value = currentTitle === '(Unbenannt)' ? '' : currentTitle;
                    inputField.maxLength = 255;

                    // Ersetze das span-Element durch das Eingabefeld
                    activityTitle.parentNode.replaceChild(inputField, activityTitle);
                    inputField.focus();
                    inputField.select();

                    // Event-Listener für das 'blur'-Ereignis (Verlust des Fokus)
                    inputField.addEventListener('blur', async function() {
                        const newTitle = inputField.value.trim();
                        const displayTitle = newTitle || '(Unbenannt)';

                        if (newTitle !== currentTitle && (newTitle !== '' || currentTitle !== '(Unbenannt)')) {
                            // Nur speichern, wenn der Titel geändert wurde
                            await saveTitleToBackend(newTitle);
                        }

                        activityTitle.textContent = displayTitle;
                        // Ersetze das Eingabefeld wieder durch das span-Element
                        inputField.parentNode.replaceChild(activityTitle, inputField);
                    });

                    // Event-Listener für die 'Enter'-Taste im Eingabefeld
                    inputField.addEventListener('keypress', function(event) {
                        if (event.key === 'Enter') {
                            inputField.blur(); // Simuliere einen Fokusverlust, um das Speichern auszulösen
                        }
                    });

                    // Event-Listener für die 'Escape'-Taste zum Abbrechen
                    inputField.addEventListener('keydown', function(event) {
                        if (event.key === 'Escape') {
                            activityTitle.textContent = currentTitle;
                            inputField.parentNode.replaceChild(activityTitle, inputField);
                        }
                    });
                });
            }
            <% } %>

            // Teilen-Modal schließen
            shareCloseBtn.addEventListener('click', function() {
                shareModal.style.display = 'none';
            });

            shareCancelBtn.addEventListener('click', function() {
                shareModal.style.display = 'none';
            });

            // Löschen-Modal schließen
            deleteCloseBtn.addEventListener('click', function() {
                deleteModal.style.display = 'none';
            });

            deleteCancelBtn.addEventListener('click', function() {
                deleteModal.style.display = 'none';
            });

            // Modals schließen, wenn außerhalb geklickt wird
            window.addEventListener('click', function(event) {
                if (event.target === shareModal) {
                    shareModal.style.display = 'none';
                }
                if (event.target === deleteModal) {
                    deleteModal.style.display = 'none';
                }
            });
        });
    </script>
<%_ %>


<%# Modal für Reise-Auswahl %>
<% if (!locals.isPublicView && isOwner) { %>
<div id="addToTripModal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h3>Aktivität zu Reise hinzufügen</h3>
        <form id="addToTripForm" method="POST">
            <div id="tripsList">
                <p>Lade Reisen...</p>
            </div>
            <div class="form-actions">
                <button type="button" id="cancelAddToTripBtn" class="button button-secondary">Abbrechen</button>
                <button type="submit" class="button button-primary">Hinzufügen</button>
            </div>
        </form>
    </div>
</div>
<% } %>

<% contentFor('pageScripts') %>
<script>
document.addEventListener('DOMContentLoaded', function() {
    <% if (!locals.isPublicView && isOwner) { %>
    // Zu Reise hinzufügen Modal
    const addToTripBtn = document.getElementById('addToTripBtn');
    const addToTripBtnMobile = document.getElementById('addToTripBtnMobile');
    const addToTripModal = document.getElementById('addToTripModal');
    const addToTripForm = document.getElementById('addToTripForm');
    const tripsList = document.getElementById('tripsList');

    // Funktion zum Öffnen des Trip-Modals
    function openAddToTripModal() {
        addToTripModal.style.display = 'block';
        loadUserTrips();
    }

    if (addToTripBtn && addToTripModal) {
        addToTripBtn.addEventListener('click', function(e) {
            e.preventDefault();
            openAddToTripModal();
        });
    }

    if (addToTripBtnMobile && addToTripModal) {
        addToTripBtnMobile.addEventListener('click', function(e) {
            e.preventDefault();
            openAddToTripModal();
        });

        // Modal schließen
        addToTripModal.querySelector('.close').addEventListener('click', function() {
            addToTripModal.style.display = 'none';
        });

        document.getElementById('cancelAddToTripBtn').addEventListener('click', function() {
            addToTripModal.style.display = 'none';
        });

        // Form submit
        addToTripForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const selectedTrip = document.querySelector('input[name="tripId"]:checked');
            if (!selectedTrip) {
                alert('Bitte wählen Sie eine Reise aus.');
                return;
            }

            const tripId = selectedTrip.value;
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/user/trip/${tripId}/add-activity`;

            const activityInput = document.createElement('input');
            activityInput.type = 'hidden';
            activityInput.name = 'activityId';
            activityInput.value = '<%= activity.id %>';
            form.appendChild(activityInput);

            document.body.appendChild(form);
            form.submit();
        });
    }

    function loadUserTrips() {
        tripsList.innerHTML = '<p>Lade Reisen...</p>';

        fetch('/user/api/trips')
            .then(response => response.json())
            .then(trips => {
                if (trips.length === 0) {
                    tripsList.innerHTML = '<p>Keine Reisen gefunden. <a href="/user/trip/new">Erste Reise erstellen</a></p>';
                    return;
                }

                let html = '<div class="trips-selection">';
                trips.forEach(trip => {
                    html += `
                        <div class="trip-item">
                            <label>
                                <input type="radio" name="tripId" value="${trip.id}">
                                <strong>${trip.title}</strong>
                                ${trip.start_date || trip.end_date ?
                                    `<br><small>${trip.start_date ? new Date(trip.start_date).toLocaleDateString('de-DE') : ''} ${trip.start_date && trip.end_date ? '-' : ''} ${trip.end_date ? new Date(trip.end_date).toLocaleDateString('de-DE') : ''}</small>`
                                    : ''}
                            </label>
                        </div>
                    `;
                });
                html += '</div>';
                tripsList.innerHTML = html;
            })
            .catch(error => {
                console.error('Fehler beim Laden der Reisen:', error);
                tripsList.innerHTML = '<p>Fehler beim Laden der Reisen.</p>';
            });
    }
    <% } %>
});
</script>
<%_ %>
