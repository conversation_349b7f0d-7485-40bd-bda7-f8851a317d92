<%# views/partials/activity_map_with_charts.ejs %>
<%# Wiederverwendbares Template für die Anzeige einer Aktivitätskarte mit Diagrammen %>

<%#
  Erwartete Parameter:
  - activity: Die Aktivitätsdaten
  - mapId: Eine eindeutige ID für die Karte (optional, Standard: 'activity-map')
  - chartId: Eine eindeutige ID für das Diagramm (optional, Standard: 'activity-chart')
  - height: <PERSON><PERSON><PERSON> der Karte (optional, Standard: '400px')
  - showPhotos: Ob Fotos angezeigt werden sollen (optional, Standard: true)
  - showPhotosOnMap: Ob Fotos auf der Karte angezeigt werden sollen (optional, Standard: true)
  - showPhotosGallery: Ob Fotos angezeigt werden sollen (optional, Standard: true)
  - showControls: Ob Steuerelemente angezeigt werden sollen (optional, Standard: true)
%>

<%
  // Standardwerte für Parameter
  const mapId = locals.mapId || 'activity-map';
  const chartId = locals.chartId || 'activity-chart';
  const mapHeight = locals.height || '400px';
  const showPhotos = locals.showPhotos !== undefined ? locals.showPhotos : true;
  const showPhotosOnMap = locals.showPhotos !== undefined ? locals.showPhotos : false;
  const showPhotosGallery = locals.showPhotos !== undefined ? locals.showPhotos : true;
  const showControls = locals.showControls !== undefined ? locals.showControls : true;
%>

<link rel="stylesheet" href="/css/chart-options.css">
<link rel="stylesheet" href="/css/google-photos-picker.css">
<div class="activity-map-with-charts-container" id="<%= mapId %>-chart-container">
  <div class="map-chart-grid">
    <% if (showControls && showPhotos) { %>
    <!-- Foto-Steuerung über der Karte -->
    <div class="map-controls-top">
      <fieldset class="map-controls">
        <legend>Karte</legend>
        <label class="toggle-switch">
          <input type="checkbox" id="photo-toggle-<%= mapId %>" <% if (showPhotosOnMap) { %>checked<% } %>>
          <span class="toggle-label">Fotos anzeigen</span>
        </label>
      </fieldset>

      <!-- Google Photos Integration -->
      <fieldset class="google-photos-controls">
        <legend>Google Photos</legend>
        <button type="button" id="google-photos-btn-<%= mapId %>" class="btn btn-secondary">
          📷 Fotos aus Google Photos hinzufügen
        </button>
        <div id="google-photos-status-<%= mapId %>" class="google-photos-status" style="display: none;"></div>
      </fieldset>
    </div>
    <% } %>

    <div id="<%= mapId %>" class="activity-map" style="height: <%= mapHeight %>;"></div>

    <div class="chart-container">
      <canvas id="<%= chartId %>" class="activity-chart"></canvas>
    </div>

    <% if (showControls) { %>
    <!-- Diagramm-Steuerung unter dem Diagramm -->
    <div class="chart-controls-bottom">
      <fieldset class="chart-type-selector">
        <legend>Diagrammtyp</legend>
        <label>
          <input type="radio" name="chart-type-<%= mapId %>" value="elevation" checked> <span>Höhe</span>
        </label>
        <label>
          <input type="radio" name="chart-type-<%= mapId %>" value="speed"> <span>Geschwindigkeit</span>
        </label>
        <label>
          <input type="radio" name="chart-type-<%= mapId %>" value="heartrate"> <span>Puls</span>
        </label>
      </fieldset>
      <fieldset class="x-axis-selector">
        <legend>X-Achse</legend>
        <label>
          <input type="radio" name="x-axis-<%= mapId %>" value="distance" checked> <span>Distanz</span>
        </label>
        <label>
          <input type="radio" name="x-axis-<%= mapId %>" value="time"> <span>Zeit</span>
        </label>
      </fieldset>
    </div>
    <% } %>
  </div>

  <!-- Container für ausgewählte Google Photos -->
  <div id="selected-photos-container" class="selected-photos-container" style="display: none;">
    <h4>Ausgewählte Fotos aus Google Photos</h4>
    <div id="selected-photos-grid" class="selected-photos-grid">
      <!-- Hier werden die ausgewählten Fotos angezeigt -->
    </div>
  </div>

  <!-- Status-Container für Google Photos -->
  <div id="google-photos-loading" class="google-photos-loading" style="display: none;">
    Lade Google Photos...
  </div>
  <div id="google-photos-error" class="google-photos-error" style="display: none;">
    <!-- Fehlermeldungen werden hier angezeigt -->
  </div>

  <% if (activity && activity.id) { %>
  <script>
    // Daten für die Karte und Diagramme
    window.ACTIVITY_DATA = window.ACTIVITY_DATA || {};
    window.ACTIVITY_DATA['<%= activity.id %>'] = {
      id: <%= activity.id %>,
      strava_id: <%= activity.strava_id || 'null' %>,
      name: "<%= activity.activity_name ? activity.activity_name.replace(/"/g, '\\"') : '' %>",
      sport_type: "<%= activity.sport_type || '' %>",
      start_lat: <%= activity.start_lat || 'null' %>,
      start_lng: <%= activity.start_lng || 'null' %>,
      mapId: "<%= mapId %>",
      chartId: "<%= chartId %>",
      showPhotos: <%= showPhotos %>,
      apiEndpoints: {
        streamData: "/api/geojson/stream_data/<%= activity.id %>",
        trackGeoJson: "/api/geojson/track_geojson/<%= activity.id %>",
        gpxAsGeoJson: "/api/geojson/gpx_as_geojson/<%= activity.id %>",
        photos: "/api/geojson/photos/for_activity/<%= activity.id %>",
        googlePhotosConfig: "/user/api/google-photos-picker/config",
        googlePhotosProcess: "/user/api/google-photos-picker/process"
      },
      startDate: "<%= activity.start_date_local || '' %>",
      endDate: "<%= activity.start_date_local || '' %>" // Für eintägige Aktivitäten
    };

    // Google Photos Picker initialisieren
    window.currentActivityId = <%= activity.id %>;
  </script>

  <!-- Google Photos Picker JavaScript -->
  <script src="/js/googlePhotoPicker.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', async function() {
      const activityId = <%= activity.id %>;
      const mapId = "<%= mapId %>";
      const googlePhotosBtn = document.getElementById(`google-photos-btn-${mapId}`);
      const googlePhotosStatus = document.getElementById(`google-photos-status-${mapId}`);

      if (googlePhotosBtn) {
        // Prüfe Google Photos Picker Status
        try {
          const configResponse = await fetch('/user/api/google-photos-picker/config');
          const config = await configResponse.json();

          if (!config.enabled) {
            googlePhotosBtn.disabled = true;
            googlePhotosBtn.textContent = '📷 Google Photos nicht verfügbar';
            googlePhotosBtn.title = 'Google Photos Picker API ist nicht konfiguriert oder deaktiviert';
            return;
          }

          // Initialisiere Google Photos Picker
          await initializeGooglePhotoPicker(config.clientId);

          googlePhotosBtn.addEventListener('click', async function() {
            const activityData = window.ACTIVITY_DATA[activityId];
            if (!activityData) {
              alert('Aktivitätsdaten nicht gefunden');
              return;
            }

            // Berechne Datumsbereich für die Aktivität (Start bis Ende des Tages)
            const startDate = new Date(activityData.startDate);
            const endDate = new Date(activityData.endDate);
            endDate.setHours(23, 59, 59, 999); // Ende des Tages

            await openPhotoPickerForActivity(activityId, startDate, endDate);
          });

        } catch (error) {
          console.error('Fehler beim Initialisieren von Google Photos Picker:', error);
          googlePhotosBtn.disabled = true;
          googlePhotosBtn.textContent = '📷 Google Photos Fehler';
          googlePhotosBtn.title = 'Fehler beim Laden der Google Photos Integration';
        }
      }
    });
  </script>
  <% } %>
</div>
