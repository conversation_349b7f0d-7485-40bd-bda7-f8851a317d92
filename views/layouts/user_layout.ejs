<%# views/layouts/user_layout.ejs %>
<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= typeof pageTitle !== 'undefined' ? pageTitle : 'Meine Seite' %> - Master-Map</title>
    <link rel="stylesheet" href="/css/user_layout.css"> <%# Ihre globale User-CSS %>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <%# Platzhalter für seitenspezifische Stylesheets %>
    <%- defineContent('pageStylesheets') %>
</head>
<body class="user-page <%= typeof pageSpecificClass !== 'undefined' ? pageSpecificClass : '' %>">

    <%- include('../users/partials/user_header') %>

    <main class="user-content-wrapper">
        <div class="user-content-inner">
            <%# Globale Nachrichten-Boxen, falls gewünscht im Layout %>
            <% if (locals.successMessage && locals.successMessage.length > 0) { %>
                <div class="message success-message">
                    <% successMessage.forEach(function(msg) { %>
                        <p><%- msg %></p>
                    <% }); %>
                </div>
            <% } %>
            <% if (locals.errorMessage && locals.errorMessage.length > 0) { %>
                <div class="message error-message">
                    <% errorMessage.forEach(function(msg) { %>
                        <p><%- msg %></p>
                    <% }); %>
                </div>
            <% } %>
            <% if (locals.infoMessage && locals.infoMessage.length > 0) { %>
                <div class="message info-message">
                    <% infoMessage.forEach(function(msg) { %>
                        <p><%- msg %></p>
                    <% }); %>
                </div>
            <% } %>

            <%- body %>
        </div>
    </main>

    <%# Platzhalter für seitenspezifische Skripte %>
    <%- defineContent('pageScripts') %>

</body>
</html>