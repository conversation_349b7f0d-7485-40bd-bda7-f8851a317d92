<%# views/layouts/main_layout.ejs %>
<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= locals.pageTitle || (locals.config && locals.config.app ? locals.config.app.name : 'Master-Map') %></title>

    <%# Hier werden die CSS-Dateien bedingt geladen %>
    <% if (locals.isAdmin) { %>
        <link rel="stylesheet" href="/css/admin.css">
    <% } else { %>
        <%# Basis User Layout immer laden %>
        <link rel="stylesheet" href="/css/user_layout.css">
        <%# Mobile CSS immer laden (CSS enthält eigene Media Queries) %>
        <link rel="stylesheet" href="/css/mobile_layout.css">
    <% } %>

    <%# Dropdown-Menü Styles (inkl. responsive Hamburgermenü) %>
    <link rel="stylesheet" href="/css/dropdown_menu.css">

    <%# Material Icons einbinden %>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons" />

    <%# Font Awesome für Icons %>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">

    <%# Platzhalter für zusätzliche, seitenspezifische Stylesheets %>
    <%- defineContent('pageStylesheets') %>
</head>
<body class="<%= locals.isAdmin ? 'admin-page' : 'user-page' %> <%= typeof pageSpecificClass !== 'undefined' ? pageSpecificClass : '' %>">

    <% if (locals.currentUser) { %>
        <%# Bedingte Einbindung des Headers (wie in vorheriger Lösung) %>
        <% if (locals.isAdmin) { %>
            <%- include('../admin/partials/header') %>
        <% } else { %>
            <%- include('../users/partials/user_header') %>
        <% } %>
    <% } %>

    <%# Hauptinhalt mit bedingter Struktur/Klassen %>
    <% if (locals.isAdmin) { %>
        <main class="content"> <%# Admin-Seiten nutzen vielleicht eine einfachere 'content'-Klasse %>
            <%# Flash-Nachrichten für Admin-Seiten %>
            <% if (locals.successMessage && locals.successMessage.length > 0) { %>
                <div class="message success-message">
                    <% successMessage.forEach(function(msg) { %>
                        <p><%- msg %></p>
                    <% }); %>
                </div>
            <% } %>
            <% if (locals.errorMessage && locals.errorMessage.length > 0) { %>
                <div class="message error-message">
                    <% errorMessage.forEach(function(msg) { %>
                        <p><%- msg %></p>
                    <% }); %>
                </div>
            <% } %>
            <% if (locals.infoMessage && locals.infoMessage.length > 0) { %>
                <div class="message info-message">
                    <% infoMessage.forEach(function(msg) { %>
                        <p><%- msg %></p>
                    <% }); %>
                </div>
            <% } %>
            <%- body %>
        </main>
    <% } else { %>
        <main class="user-content-wrapper">
            <div class="user-content-inner">
                <%# Flash-Nachrichten für User-Seiten %>
                <% if (locals.successMessage && locals.successMessage.length > 0) { %>
                    <div class="message success-message">
                        <% successMessage.forEach(function(msg) { %>
                            <p><%- msg %></p>
                        <% }); %>
                    </div>
                <% } %>
                <% if (locals.errorMessage && locals.errorMessage.length > 0) { %>
                    <div class="message error-message">
                        <% errorMessage.forEach(function(msg) { %>
                            <p><%- msg %></p>
                        <% }); %>
                    </div>
                <% } %>
                <% if (locals.infoMessage && locals.infoMessage.length > 0) { %>
                    <div class="message info-message">
                        <% infoMessage.forEach(function(msg) { %>
                            <p><%- msg %></p>
                        <% }); %>
                    </div>
                <% } %>
                <%- body %>
            </div>
        </main>
        <footer class="user-main-footer">
            <p>&copy; <%= locals.currentYear || new Date().getFullYear() %> <%= locals.config.app.name || 'Master-Map' %>. Alle Rechte vorbehalten.</p>
        </footer>
    <% } %>

    <%# Bedingte Einbindung von Skripten %>
    <% if (locals.currentUser && !locals.isAdmin) { %>
        <script src="/js/notification_handler.js" defer></script>
    <% } %>

    <%# Hamburgermenü JavaScript für responsive Navigation %>
    <% if (locals.currentUser) { %>
        <script src="/js/hamburger_menu.js" defer></script>
    <% } %>

    <%- defineContent('pageScripts') %>
</body>
</html>