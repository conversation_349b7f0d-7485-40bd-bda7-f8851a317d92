/* Google Photos Picker Styles */

.google-photos-controls {
    margin-left: 15px;
    display: inline-block;
    vertical-align: top;
}

.google-photos-controls legend {
    font-size: 0.9em;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

#google-photos-btn-activity-map,
[id^="google-photos-btn-"] {
    background: linear-gradient(135deg, #4285f4, #34a853);
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.9em;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

#google-photos-btn-activity-map:hover,
[id^="google-photos-btn-"]:hover {
    background: linear-gradient(135deg, #3367d6, #2d8f47);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(66, 133, 244, 0.3);
}

#google-photos-btn-activity-map:disabled,
[id^="google-photos-btn-"]:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.google-photos-status {
    margin-top: 5px;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.8em;
}

.google-photos-status.loading {
    background-color: #e3f2fd;
    color: #1976d2;
    border: 1px solid #bbdefb;
}

.google-photos-status.error {
    background-color: #ffebee;
    color: #c62828;
    border: 1px solid #ffcdd2;
}

.google-photos-status.success {
    background-color: #e8f5e8;
    color: #2e7d32;
    border: 1px solid #c8e6c9;
}

/* Selected Photos Container */
.selected-photos-container {
    margin-top: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.selected-photos-container h4 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
    font-size: 1.1em;
}

.selected-photos-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
}

.selected-photo {
    background: white;
    border-radius: 8px;
    padding: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.2s ease;
}

.selected-photo:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.selected-photo img {
    max-width: 100%;
    max-height: 150px;
    border-radius: 4px;
    margin-bottom: 8px;
}

.selected-photo p {
    margin: 5px 0;
    font-size: 0.9em;
    color: #666;
    word-break: break-word;
}

.selected-photo button {
    background-color: #28a745;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 0.8em;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.selected-photo button:hover {
    background-color: #218838;
}

.selected-photo button:disabled {
    background-color: #6c757d;
    cursor: not-allowed;
}

/* Loading Spinner */
.google-photos-loading {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: #1976d2;
    font-size: 0.9em;
}

.google-photos-loading::before {
    content: "";
    width: 16px;
    height: 16px;
    border: 2px solid #e3f2fd;
    border-top: 2px solid #1976d2;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error Messages */
.google-photos-error {
    background-color: #ffebee;
    color: #c62828;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #ffcdd2;
    margin-top: 10px;
    font-size: 0.9em;
}

/* Integration Status */
.google-photos-integration-status {
    background-color: #fff3cd;
    color: #856404;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #ffeaa7;
    margin: 20px 0;
}

.google-photos-integration-status h4 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #856404;
}

.google-photos-integration-status ul {
    margin: 10px 0;
    padding-left: 20px;
}

.google-photos-integration-status li {
    margin-bottom: 5px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .google-photos-controls {
        margin-left: 0;
        margin-top: 10px;
        display: block;
        width: 100%;
    }
    
    .selected-photos-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 10px;
    }
    
    .selected-photo {
        padding: 8px;
    }
    
    .selected-photo img {
        max-height: 120px;
    }
    
    #google-photos-btn-activity-map,
    [id^="google-photos-btn-"] {
        width: 100%;
        justify-content: center;
        padding: 10px;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .selected-photos-container {
        background-color: #2d3748;
        border-color: #4a5568;
    }
    
    .selected-photos-container h4 {
        color: #e2e8f0;
    }
    
    .selected-photo {
        background: #4a5568;
        color: #e2e8f0;
    }
    
    .selected-photo p {
        color: #cbd5e0;
    }
    
    .google-photos-integration-status {
        background-color: #2d3748;
        color: #e2e8f0;
        border-color: #4a5568;
    }
    
    .google-photos-integration-status h4 {
        color: #e2e8f0;
    }
}
