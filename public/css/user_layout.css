/* public/css/user_layout.css */

/* Globale Resets und Basis-Setup */
*,
*::before,
*::after {
    box-sizing: border-box; /* <PERSON><PERSON><PERSON><PERSON><PERSON>, dass Padding/Border die Gesamtbreite erhöhen */
    margin: 0;
    padding: 0;
}

html {
    line-height: 1.15; /* 1 */
    -webkit-text-size-adjust: 100%; /* 2 */
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
    background-color: #f8f9fa; /* Hintergrund von welcome-page-body */
    color: #343a40;
    padding: 0; /* Entferne jegliches Padding vom Body, es sei denn spezifisch benötigt */
}



body.user-page, body.simple-page-layout {
    margin: 0;
    padding: 0;
    height: 100%; /* Für simple_layout wichtig, um Zentrierung zu ermöglichen */
}

body.user-page {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
    background-color: #f4f7f6;
    color: #343a40;
    line-height: 1.6;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

body.simple-page-layout {
    background-color: #f0f2f5; /* Leicht anderer, neutraler Hintergrund */
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: 1rem;
    box-sizing: border-box;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    color: #343a40;
}
.simple-content-box {
    background-color: #ffffff;
    padding: 2.5rem 3rem; /* Mehr Padding */
    border-radius: 8px;
    box-shadow: 0 6px 18px rgba(0,0,0,0.1);
    width: 100%;
    max-width: 400px; /* Optimale Breite für Login/Fehler-Boxen */
    text-align: center;
}
/* Styling für Nachrichten wird von .message aus user_layout.css übernommen */
.message {
    text-align: left;
    margin-bottom: 1.5rem; /* Mehr Abstand unter Nachrichten */
}

/* Responsive Anpassungen */
@media only screen and  (max-width: 768px) {
    .simple-content-box {
    }            
}

/* --- User Header Styling --- */
.user-main-header {
    background-color: #ffffff;
    padding: 2px;
    border-bottom: 1px solid #e0e0e0;
    box-shadow: 0 1px 3px rgba(0,0,0,0.04);
    flex-shrink: 0; /* Verhindert Schrumpfen */
    position: fixed; /* Header ist immer sichtbar */
    top: 0;
    left: 0;
    right: 0;
    /*width: 100%; Probleme beim Header der mobile view */
    z-index: 1000; /* Stellt sicher, dass der Header über der Karte liegt */
}
.user-header-content { display: flex; justify-content: space-between; align-items: center; max-width: 1200px; margin: 0 auto; }
.user-header-title { margin: 0; font-size: 1.5em; }
.user-header-title a { text-decoration: none; color: #212529; font-weight: 600; }
.user-header-title a:hover { color: #007bff; }
.user-main-nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 30px; /* Erhöhter Abstand zwischen Einträgen */
}
.user-main-nav li a {
    text-decoration: none;
    color: #495057;
    font-size: 0.95em;
    padding: 8px 4px;
    position: relative;
    transition: color 0.2s ease-in-out;
}

/* Pseudo-Element für den Unterstrich, immer vorhanden aber transparent */
.user-main-nav li a::after {
    content: '';
    position: absolute;
    bottom: 0px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: transparent; /* Standardmäßig transparent */
    transition: background-color 0.2s ease-in-out;
}

.user-main-nav li a:hover,
.user-main-nav li a.active {
    color: #007bff;
}

.user-main-nav li a:hover::after,
.user-main-nav li a.active::after {
    background-color: #007bff; /* Beim Hover oder aktiv blau */
}
/* Notification Badge */
.notification-badge {
    background-color: #dc3545;
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 0.7em;
    position: absolute;
    top: -8px;
    right: -8px;
    border: 2px solid white;
    display: inline-block;
    min-width: 18px;
    text-align: center;
    font-weight: bold;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

/* Notifications Dropdown */
.notifications-dropdown {
    position: absolute;
    background-color: white;
    border: 1px solid #dee2e6;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    right: 0;
    top: 45px;
    width: 320px;
    max-width: calc(100vw - 40px); /* Mehr Abstand zu den Seiten */
    max-height: 400px;
    overflow: hidden;
    z-index: 1001;
    border-radius: 8px;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

/* Responsive Anpassungen */
@media (max-width: 768px) {
    .notifications-dropdown {
        width: 300px;
        max-width: calc(100vw - 30px);
    }
}

@media (max-width: 480px) {
    .notifications-dropdown {
        width: calc(100vw - 20px);
        right: 10px;
        left: 10px;
        max-width: none;
    }
}

.notifications-dropdown ul {
    list-style: none;
    padding: 0;
    margin: 0;
    max-height: 300px;
    overflow-y: auto;
    display: block !important; /* Überschreibt jedes Flexbox-Layout */
    flex-direction: column !important; /* Falls doch Flexbox verwendet wird */
    width: 100%;
    gap: 0 !important; /* Entfernt jegliche Gaps */
}

/* Dropdown Header */
#dropdownContentHeader {
    padding: 12px 16px;
    border-bottom: 1px solid #e9ecef;
    font-weight: 600;
    text-align: center;
    background-color: #f8f9fa;
    color: #495057;
    font-size: 0.9em;
}

/* Individual Notification Items */
.notifications-dropdown li {
    display: block !important; /* Explizit als Block-Element */
    width: 100% !important;
    float: none !important; /* Verhindert Float-Layout */
    flex: none !important; /* Verhindert Flex-Item-Verhalten */
    padding: 12px 16px;
    border-bottom: 1px solid #f1f3f4;
    cursor: pointer;
    font-size: 0.85em;
    transition: background-color 0.2s ease;
    position: relative;
    line-height: 1.4;
    box-sizing: border-box;
    clear: both; /* Verhindert Float-Probleme */
}

.notifications-dropdown li:last-child {
    border-bottom: none;
}

.notifications-dropdown li:hover {
    background-color: #f8f9fa;
}

/* Unread notifications */
.notifications-dropdown li:not(.is-read) {
    background-color: #fff;
    border-left: 3px solid #007bff;
}

.notifications-dropdown li:not(.is-read):hover {
    background-color: #f0f8ff;
}

/* Read notifications */
.notifications-dropdown li.is-read {
    background-color: #fafafa;
    opacity: 0.8;
}

.notifications-dropdown li.is-read:hover {
    background-color: #f0f0f0;
}

/* Notification message */
.notifications-dropdown li .notif-message {
    display: -webkit-box;
    -webkit-line-clamp: 3; /* Erlaubt bis zu 3 Zeilen für mehr Kontext */
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 6px;
    color: #212529;
    font-weight: 500;
    line-height: 1.4;
    word-wrap: break-word;
    hyphens: auto;
}

.notifications-dropdown li.is-read .notif-message {
    color: #6c757d;
    font-weight: 400;
}

/* Notification timestamp */
.notifications-dropdown li .notif-time {
    display: block;
    color: #868e96;
    font-size: 0.75em;
    margin-top: 4px;
    font-weight: 400;
}

.notifications-dropdown li.is-read .notif-time {
    color: #adb5bd;
}

/* Unread indicator dot */
.notifications-dropdown li:not(.is-read)::before {
    content: '';
    position: absolute;
    left: 8px;
    top: 50%;
    transform: translateY(-50%);
    width: 6px;
    height: 6px;
    background-color: #007bff;
    border-radius: 50%;
}

.notifications-dropdown li:not(.is-read) .notif-message {
    margin-left: 12px;
}

.notifications-dropdown li:not(.is-read) .notif-time {
    margin-left: 12px;
}

/* View all notifications link */
.notifications-dropdown .view-all-notifications {
    display: block;
    text-align: center;
    padding: 12px 16px;
    background-color: #f8f9fa;
    text-decoration: none;
    color: #007bff;
    font-weight: 600;
    font-size: 0.85em;
    border-top: 1px solid #e9ecef;
    transition: background-color 0.2s ease;
}

.notifications-dropdown .view-all-notifications:hover {
    background-color: #e9ecef;
    color: #0056b3;
    text-decoration: none;
}

/* Loading state */
#dropdownContentLoading {
    padding: 20px;
    text-align: center;
    color: #6c757d;
    font-size: 0.9em;
}

/* Empty state */
.notifications-dropdown .no-notifications {
    padding: 30px 20px;
    text-align: center;
    color: #6c757d;
    font-size: 0.9em;
}

/* Scrollbar styling for webkit browsers */
.notifications-dropdown ul::-webkit-scrollbar {
    width: 6px;
}

.notifications-dropdown ul::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.notifications-dropdown ul::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.notifications-dropdown ul::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* === INLINE TITLE EDITING === */
.title-container {
    display: flex;
    align-items: center;
    gap: 8px;
}

.editable-title {
    margin: 0;
    padding: 2px 4px;
    border-radius: 3px;
    transition: background-color 0.2s ease;
}

.editable-title:hover {
    background-color: #f8f9fa;
}

.edit-icon {
    cursor: pointer;
    color: #007bff;
    font-size: 0.9em;
    transition: color 0.2s ease-in-out;
    opacity: 0.7;
}

.edit-icon:hover {
    color: #0056b3;
    opacity: 1;
}

.title-input {
    font-size: inherit;
    font-family: inherit;
    padding: 2px 6px;
    border: 1px solid #007bff;
    border-radius: 3px;
    background-color: #fff;
    min-width: 200px;
    outline: none;
}

.title-input:focus {
    border-color: #0056b3;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

/* Spinner animation for loading */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* --- Haupt-Content-Bereich für User-Seiten --- */
main.user-content-wrapper {
    flex-grow: 1;
    width: 100%;
    padding: 20px 25px; /* Außenabstand für den Wrapper */
    padding-top: 93px; /* 73px Header-Höhe + 20px Standard-Padding */
    box-sizing: border-box;
}
main.user-content-wrapper .user-content-inner {
    max-width: 1200px; /* Maximale Breite des Inhaltsblocks */
    margin: 0 auto;    /* Zentriert den Inhaltsblock */
    background-color: #ffffff;
    padding: 25px 30px; /* Innenabstand des Inhaltsblocks */
    border-radius: 8px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.07);
}
main.user-content-wrapper h2.page-main-title {
    margin-top: 0;
    font-size: 1.8em;
    color: #343a40;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 10px;
    margin-bottom: 25px;
}

/* --- Footer --- */
.user-main-footer {
    text-align: center;
    padding: 0;
    border-top: 1px solid #e0e0e0;
    margin-top: auto; /* Drückt den Footer nach unten, wenn der Inhalt kurz ist */
    background-color: #f8f9fa;
    flex-shrink: 0; /* Verhindert Schrumpfen */
    font-size: 0.9em;
    color: #6c757d;
}

/* --- Globale Nachrichten-Boxen (aus user_layout.css übernommen) --- */
.message { padding: 15px 20px; margin-bottom: 20px; border: 1px solid transparent; border-radius: 5px; font-size: 0.95em; }
.success-message { color: #0f5132; background-color: #d1e7dd; border-color: #badbcc; }
.error-message { color: #842029; background-color: #f8d7da; border-color: #f5c2c7; }
.info-message { color: #055160; background-color: #cff4fc; border-color: #b6effb; }

/* --- Globale Sektionen (aus user_layout.css übernommen) --- */
.content-section { margin-bottom: 30px; padding: 20px; background-color: #fdfdfd; border: 1px solid #e9ecef; border-radius: 6px; }
.content-section h3 { margin-top: 0; font-size: 1.4em; color: #495057; border-bottom: 1px solid #f1f1f1; padding-bottom: 10px; margin-bottom: 20px; }

/* --- Globale Formular-Stile (aus user_layout.css übernommen) --- */
.form-group { margin-bottom: 1.25rem; }
.form-group label { display: block; font-weight: 600; margin-bottom: 0.5rem; color: #495057; font-size: 0.9em; }
.form-group input[type="text"], .form-group input[type="email"], .form-group input[type="password"], .form-group input[type="number"], .form-group input[type="date"], .form-group input[type="search"], .form-group select, .form-group textarea { width: 100%; padding: 0.65rem 0.9rem; border: 1px solid #ced4da; border-radius: 0.25rem; box-sizing: border-box; font-size: 0.95rem; transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out; }
.form-group input:focus, .form-group select:focus, .form-group textarea:focus { border-color: #86b7fe; outline: 0; box-shadow: 0 0 0 0.25rem rgba(13,110,253,.25); }
.form-group small { display: block; margin-top: 0.3rem; color: #6c757d; font-size: 0.8rem; }
.form-group input[type="checkbox"] { width: auto; margin-right: 0.5rem; vertical-align: middle; height:1em; width:1em;}
.form-group label input[type="checkbox"] { font-weight: normal; font-size: 0.95em; }

/* --- Globale Button-Stile (aus user_layout.css übernommen) --- */
button, .button, input[type="submit"], .button-link { display: inline-block; font-weight: 500; color: #fff; text-align: center; vertical-align: middle; cursor: pointer; background-color: #0d6efd; border: 1px solid #0d6efd; padding: 0.5rem 1rem; font-size: 0.95rem; line-height: 1.5; border-radius: 0.25rem; text-decoration: none; transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out; }
button:hover, .button:hover, input[type="submit"]:hover, .button-link:hover { background-color: #0b5ed7; border-color: #0a58ca; color: #fff; }
.button-delete { background-color: #dc3545; border-color: #dc3545; }
.button-delete:hover { background-color: #bb2d3b; border-color: #b02a37; }
.button-success { background-color: #198754; border-color: #198754; }
.button-success:hover { background-color: #157347; border-color: #146c43; }
.button-secondary { background-color: #6c757d; border-color: #6c757d; }
.button-secondary:hover { background-color: #5a6268; border-color: #545b62; }
.button-link-small { padding: 0.25rem 0.5rem; font-size: 0.8rem; }

/* --- Globale Tabellen-Stile (aus user_layout.css übernommen) --- */
.data-table { width: 100%; border-collapse: collapse; margin-top: 20px; font-size: 0.9em; background-color: #fff; }
.data-table th, .data-table td { border: 1px solid #e0e0e0; padding: 10px 12px; text-align: left; vertical-align: middle; }
.data-table thead th { background-color: #f1f3f5; font-weight: 600; white-space: nowrap; color: #495057; }
.data-table tbody tr:nth-child(odd) { background-color: #fcfcfc; }
.data-table tbody tr:hover { background-color: #f0f5fa; }
.data-table td.actions { white-space: nowrap; width: 1%; text-align: right; }
.data-table td.actions form { display: inline-block; margin: 0 2px; }
.data-table .number { text-align: right; }
.data-table th a { text-decoration: none; color: inherit; }
.data-table th a.sort-asc::after { content: " ▲"; font-size: 0.8em; }
.data-table th a.sort-desc::after { content: " ▼"; font-size: 0.8em; }

/* Pagination (aus user_layout.css übernommen) */
.pagination { margin: 25px 0; text-align: center; }
.pagination a, .pagination span { margin: 0 4px; padding: 6px 12px; border: 1px solid #dee2e6; text-decoration: none; color: #0d6efd; border-radius: 0.25rem; background-color: #fff;}
.pagination a:hover { background-color: #e9ecef; }
.pagination span.current { background-color: #0d6efd; color: white; border-color: #0d6efd; }
.pagination span.disabled { color: #6c757d; border-color: #e9ecef; background-color: #f8f9fa; }

/* Spezifische Grid-Layouts (aus user_layout.css übernommen) */
.settings-grid, .equipment-grid, .friends-grid { display: grid; grid-template-columns: 1fr; gap: 25px; }
@media (min-width: 992px) {
    .settings-grid { grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); }
    .equipment-grid { grid-template-columns: minmax(300px, 1fr) 2fr; }
    .friends-grid { grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); }
}

/* Mobile Anpassungen */
@media (max-width: 768px) {
    /* Header-Anpassungen */
    .user-main-header {
        padding: 8px 15px; /* Noch kompakterer Header auf Mobilgeräten */
    }

    .user-header-title {
        font-size: 1.3em;
    }

    /* Navigation mobile styles moved to dropdown_menu.css */

    /* Content-Wrapper-Anpassungen */
    main.user-content-wrapper {
        /*padding: 10px;*/
        padding: 0px;
        padding-top: 70px; /* Erhöhter Abstand für den Header in der mobilen Ansicht */
    }

    main.user-content-wrapper .user-content-inner {
        padding: 2px;
        /*border-radius: 6px;*/
    }

    /* Formular-Anpassungen */
    .form-group input[type="text"],
    .form-group input[type="email"],
    .form-group input[type="password"],
    .form-group input[type="number"],
    .form-group input[type="date"],
    .form-group input[type="search"],
    .form-group select,
    .form-group textarea {
        padding: 0.5rem 0.75rem;
        font-size: 0.9rem;
    }

    /* Button-Anpassungen */
    button, .button, input[type="submit"], .button-link {
        padding: 0.4rem 0.8rem;
        font-size: 0.9rem;
    }

    /* Tabellen-Anpassungen für mobile Geräte */
    .data-table {
        font-size: 0.8em;
    }

    .data-table th, .data-table td {
        padding: 8px 10px;
    }

    /* Basis Mobile-Anpassungen (erweiterte Anpassungen in mobile_layout.css) */

    /* Filter-Formular-Anpassungen */
    .filter-form .filter-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }
}

/* Mobile Cards standardmäßig verstecken (werden nur auf mobilen Geräten angezeigt) */
.mobile-cards-container {
    display: none;
}

/* Equipment-Tabelle responsive machen */
.user-equipment-page .data-table {
    width: 100%;
    table-layout: auto;
    border-collapse: collapse;
}

.user-equipment-page .data-table th,
.user-equipment-page .data-table td {
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    vertical-align: top;
}

.user-equipment-page .data-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    white-space: nowrap;
}

/* Spaltenbreiten optimieren */
.user-equipment-page .data-table th:nth-child(1), /* Name */
.user-equipment-page .data-table td:nth-child(1) {
    width: 15%;
    min-width: 120px;
}

.user-equipment-page .data-table th:nth-child(2), /* Typ */
.user-equipment-page .data-table td:nth-child(2) {
    width: 10%;
    min-width: 80px;
}

.user-equipment-page .data-table th:nth-child(3), /* Beschreibung */
.user-equipment-page .data-table td:nth-child(3) {
    width: 20%;
    min-width: 150px;
}

.user-equipment-page .data-table th:nth-child(4), /* Notizen */
.user-equipment-page .data-table td:nth-child(4) {
    width: 20%;
    min-width: 150px;
}

.user-equipment-page .data-table th:nth-child(5), /* Aktivitäten */
.user-equipment-page .data-table td:nth-child(5) {
    width: 8%;
    min-width: 70px;
    text-align: center;
}

.user-equipment-page .data-table th:nth-child(6), /* Distanz */
.user-equipment-page .data-table td:nth-child(6) {
    width: 10%;
    min-width: 80px;
    text-align: right;
}

.user-equipment-page .data-table th:nth-child(7), /* Dauer */
.user-equipment-page .data-table td:nth-child(7) {
    width: 10%;
    min-width: 80px;
    text-align: right;
}

.user-equipment-page .data-table th:nth-child(8), /* Aktionen */
.user-equipment-page .data-table td:nth-child(8) {
    width: 15%;
    min-width: 140px;
    text-align: center;
}

/* Equipment-Grid Layout anpassen für bessere Nutzung der verfügbaren Breite */
@media (min-width: 992px) {
    .user-equipment-page .equipment-grid {
        grid-template-columns: 1fr; /* Alle Sections in einer Spalte */
        gap: 25px;
    }

    /* List-Section soll die volle Breite nutzen */
    .user-equipment-page .equipment-grid .list-section {
        grid-column: 1 / -1; /* Nimmt die volle Breite ein */
    }

    /* Form-Section und Sport-Type-Section nebeneinander, wenn beide vorhanden */
    .user-equipment-page .equipment-grid .form-section {
        grid-column: 1;
    }

    .user-equipment-page .equipment-grid .sport-type-equipment-section {
        grid-column: 1;
    }
}

/* Für sehr große Bildschirme: Form und Sport-Type nebeneinander */
@media (min-width: 1200px) {
    .user-equipment-page .equipment-grid {
        grid-template-columns: 1fr 1fr; /* Zwei Spalten für Form und Sport-Type */
        grid-template-rows: auto auto;
    }

    .user-equipment-page .equipment-grid .form-section {
        grid-column: 1;
        grid-row: 1;
    }

    .user-equipment-page .equipment-grid .sport-type-equipment-section {
        grid-column: 2;
        grid-row: 1;
    }

    .user-equipment-page .equipment-grid .list-section {
        grid-column: 1 / -1; /* Volle Breite über beide Spalten */
        grid-row: 2;
    }
}

/* Filter Formulare (aus user_layout.css übernommen) */
.filter-form { margin-bottom: 25px; padding: 20px; background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 6px; }
.filter-form .filter-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(220px, 1fr)); gap: 15px 20px; align-items: flex-end; }
.filter-form .filter-grid .form-group { margin-bottom: 0; }
.filter-form .actions { grid-column: 1 / -1; margin-top: 15px; padding-top: 15px; border-top: 1px solid #dee2e6; }

/* Spezifische Styles für die Kartenansicht (map.ejs, public_map.ejs) */
body.user-map-page #map, body.public-map-page #map { /* Stellt sicher, dass die Karte die Höhe füllt, wenn der Header da ist */
    height: calc(100vh - 55px); /* 55px ist die neue Header-Höhe (reduziert von 73px) */
    width: 100%;
    position: relative; /* Wichtig für die Positionierung */
    z-index: 1; /* Unter dem Header */
    /*margin-top: 55px; /* Abstand für den fixen Header */
}
/* Wenn kein Header da ist (z.B. simple_layout für eine Vollbildkarte ohne Nav) */
body.fullscreen-map-page #map { height: 100vh; width: 100%; }

/* Spezielle Anpassungen für die Kartenansicht */
body.user-map-page main.user-content-wrapper,
body.public-map-page main.user-content-wrapper {
    padding: 0;
    padding-top: 55px; /* Nur der Header-Abstand, kein zusätzliches Padding */
}

/* Mobile Anpassungen für die Kartenansicht */
@media (max-width: 768px) {
    body.user-map-page main.user-content-wrapper,
    body.public-map-page main.user-content-wrapper {
        padding-top: 55px; /* Konsistenter Abstand auch in der mobilen Ansicht */
    }
}

body.user-map-page main.user-content-wrapper .user-content-inner,
body.public-map-page main.user-content-wrapper .user-content-inner {
    max-width: none; /* Keine Begrenzung der Breite */
    margin: 0; /* Kein Margin */
    padding: 0; /* Kein Padding */
    background-color: transparent; /* Kein Hintergrund */
    border-radius: 0; /* Keine abgerundeten Ecken */
    box-shadow: none; /* Kein Schatten */
}

/* Styles für das Overlay auf der Karte */
#overlay {
    position: fixed;
    display: none; /* Wird per JS gesteuert */
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0,0,0,0.6);
    z-index: 1002; /* Über dem PanelLayers Control */
    cursor: progress;
}
#overlay #text{
    position: absolute;
    top: 50%;
    left: 50%;
    font-size: 1.2em;
    color: white;
    transform: translate(-50%,-50%);
    -ms-transform: translate(-50%,-50%);
    background-color: #333;
    padding: 15px 25px;
    border-radius: 5px;
}

