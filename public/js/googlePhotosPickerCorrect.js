/**
 * Google Photos Picker - Korrekte Implementierung basierend auf Google's offiziellem Beispiel
 * Verwendet die neue Google Photos Picker API (2024/2025)
 */

class GooglePhotosPickerCorrect {
    constructor() {
        this.currentSession = null;
        this.isInitialized = false;
        this.activityId = null;
    }

    /**
     * Initialisiert den Google Photos Picker
     */
    async initialize(activityId) {
        this.activityId = activityId;
        this.isInitialized = true;
        console.log('Google Photos Picker (korrekt) initialisiert für Aktivität:', activityId);
    }

    /**
     * Startet den Google Photos Picker Prozess
     */
    async startPickerSession(callback) {
        try {
            console.log('Starte neue Google Photos Picker Session...');
            
            // Zeige Loading
            this.showStatus('Erstelle Picker Session...', 'loading');

            // Erstelle neue Session
            const response = await fetch('/user/api/google-photos-picker/session', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const result = await response.json();

            if (!response.ok || !result.success) {
                throw new Error(result.error || 'Fehler beim <PERSON> der Picker Session');
            }

            this.currentSession = result.session;
            console.log('Picker Session erstellt:', this.currentSession.id);
            console.log('Picker URL:', result.pickerUrl);

            // Zeige Picker URL
            this.showPickerLink(result.pickerUrl);

            // Starte Polling für ausgewählte Fotos
            this.startPollingForPhotos(callback);

        } catch (error) {
            console.error('Fehler beim Starten der Picker Session:', error);
            this.showStatus('Fehler: ' + error.message, 'error');
            if (callback) {
                callback({ error: error.message });
            }
        }
    }

    /**
     * Zeigt den Picker Link an
     */
    showPickerLink(pickerUrl) {
        const container = document.getElementById('google-photos-picker-container');
        if (!container) {
            console.warn('Picker container nicht gefunden');
            return;
        }

        container.innerHTML = `
            <div class="picker-session-info">
                <h4>📷 Google Photos Picker bereit</h4>
                <p>Klicke auf den Link unten, um deine Google Photos zu öffnen und Fotos auszuwählen:</p>
                <div class="picker-actions">
                    <a href="${pickerUrl}" target="_blank" class="btn btn-primary picker-link">
                        🔗 Google Photos öffnen
                    </a>
                    <button type="button" id="check-photos-btn" class="btn btn-secondary">
                        ✅ Ausgewählte Fotos prüfen
                    </button>
                </div>
                <div class="picker-instructions">
                    <small>
                        <strong>Anleitung:</strong>
                        <ol>
                            <li>Klicke auf "Google Photos öffnen"</li>
                            <li>Wähle deine gewünschten Fotos aus</li>
                            <li>Bestätige deine Auswahl in Google Photos</li>
                            <li>Komme zurück und klicke "Ausgewählte Fotos prüfen"</li>
                        </ol>
                    </small>
                </div>
            </div>
        `;

        // Event Listener für "Fotos prüfen" Button
        const checkBtn = document.getElementById('check-photos-btn');
        if (checkBtn) {
            checkBtn.addEventListener('click', () => {
                this.checkSelectedPhotos();
            });
        }

        this.showStatus('Picker bereit! Klicke auf den Link oben.', 'success');
    }

    /**
     * Prüft manuell auf ausgewählte Fotos
     */
    async checkSelectedPhotos() {
        if (!this.currentSession) {
            this.showStatus('Keine aktive Session', 'error');
            return;
        }

        try {
            this.showStatus('Prüfe ausgewählte Fotos...', 'loading');

            const response = await fetch(`/user/api/google-photos-picker/session/${this.currentSession.id}/photos`);
            const result = await response.json();

            if (!response.ok || !result.success) {
                throw new Error(result.error || 'Fehler beim Abrufen der Fotos');
            }

            console.log('Ausgewählte Fotos:', result.photos);

            if (result.photos && result.photos.length > 0) {
                this.displaySelectedPhotos(result.photos);
                this.showStatus(`${result.photos.length} Foto(s) gefunden!`, 'success');
            } else {
                this.showStatus('Noch keine Fotos ausgewählt. Bitte wähle Fotos in Google Photos aus.', 'info');
            }

        } catch (error) {
            console.error('Fehler beim Prüfen der Fotos:', error);
            this.showStatus('Fehler: ' + error.message, 'error');
        }
    }

    /**
     * Startet automatisches Polling für ausgewählte Fotos
     */
    startPollingForPhotos(callback) {
        if (!this.currentSession) return;

        console.log('Starte Polling für ausgewählte Fotos...');
        
        const pollInterval = setInterval(async () => {
            try {
                const response = await fetch(`/user/api/google-photos-picker/session/${this.currentSession.id}/photos`);
                const result = await response.json();

                if (result.success && result.photos && result.photos.length > 0) {
                    console.log('Fotos automatisch erkannt:', result.photos.length);
                    clearInterval(pollInterval);
                    
                    this.displaySelectedPhotos(result.photos);
                    this.showStatus(`${result.photos.length} Foto(s) automatisch erkannt!`, 'success');
                    
                    if (callback) {
                        callback({ photos: result.photos });
                    }
                }
            } catch (error) {
                console.error('Polling Fehler:', error);
                // Polling weiterlaufen lassen
            }
        }, 5000); // Alle 5 Sekunden prüfen

        // Stoppe Polling nach 5 Minuten
        setTimeout(() => {
            clearInterval(pollInterval);
            console.log('Polling gestoppt nach 5 Minuten');
        }, 300000);
    }

    /**
     * Zeigt die ausgewählten Fotos an
     */
    displaySelectedPhotos(photos) {
        const container = document.getElementById('selected-photos-container');
        if (!container) {
            console.warn('Selected photos container nicht gefunden');
            return;
        }

        container.style.display = 'block';
        const grid = document.getElementById('selected-photos-grid');
        if (!grid) return;

        grid.innerHTML = '';

        photos.forEach((photo, index) => {
            const photoElement = document.createElement('div');
            photoElement.className = 'selected-photo';
            photoElement.innerHTML = `
                <img src="${photo.baseUrl}=w200-h200" alt="Foto ${index + 1}" loading="lazy">
                <p>${photo.filename || `Foto ${index + 1}`}</p>
                <div class="photo-info">
                    <small>
                        ${photo.mediaMetadata?.creationTime ? 
                            new Date(photo.mediaMetadata.creationTime).toLocaleDateString() : 
                            'Unbekanntes Datum'
                        }
                    </small>
                </div>
                <button onclick="addGooglePhotoToActivity('${photo.id}', '${photo.baseUrl}', this)" 
                        data-photo-id="${photo.id}" class="btn btn-sm btn-success">
                    Zu Aktivität hinzufügen
                </button>
            `;
            grid.appendChild(photoElement);
        });
    }

    /**
     * Zeigt Status-Nachrichten an
     */
    showStatus(message, type = 'info') {
        const statusElement = document.getElementById('google-photos-status');
        if (!statusElement) return;

        statusElement.textContent = message;
        statusElement.className = `google-photos-status ${type}`;
        statusElement.style.display = 'block';

        // Auto-hide nach 10 Sekunden (außer bei loading)
        if (type !== 'loading') {
            setTimeout(() => {
                statusElement.style.display = 'none';
            }, 10000);
        }
    }
}

// Globale Instanz
let googlePhotosPickerCorrect = null;

/**
 * Initialisiert den korrekten Google Photos Picker
 */
async function initializeCorrectGooglePhotosPicker(activityId) {
    if (!googlePhotosPickerCorrect) {
        googlePhotosPickerCorrect = new GooglePhotosPickerCorrect();
    }
    await googlePhotosPickerCorrect.initialize(activityId);
    return googlePhotosPickerCorrect;
}

/**
 * Startet den Google Photos Picker für eine Aktivität
 */
async function startGooglePhotosPickerForActivity(activityId) {
    try {
        if (!googlePhotosPickerCorrect) {
            await initializeCorrectGooglePhotosPicker(activityId);
        }

        await googlePhotosPickerCorrect.startPickerSession((result) => {
            if (result.error) {
                console.error('Picker Fehler:', result.error);
            } else if (result.photos) {
                console.log('Fotos ausgewählt:', result.photos.length);
            }
        });

    } catch (error) {
        console.error('Fehler beim Starten des Google Photos Pickers:', error);
        alert('Fehler: ' + error.message);
    }
}

/**
 * Fügt ein Google Photo zur Aktivität hinzu
 */
async function addGooglePhotoToActivity(photoId, baseUrl, buttonElement) {
    const button = buttonElement;
    
    try {
        if (button) {
            button.disabled = true;
            button.textContent = 'Wird hinzugefügt...';
        }

        // Hier würde die Integration mit deinem bestehenden Foto-System erfolgen
        console.log('Füge Google Photo hinzu:', { photoId, baseUrl });
        
        // Simuliere API-Aufruf
        await new Promise(resolve => setTimeout(resolve, 1000));

        if (button) {
            button.textContent = '✓ Hinzugefügt';
            button.style.backgroundColor = '#28a745';
        }

        alert('Foto erfolgreich hinzugefügt! (Simulation)');

    } catch (error) {
        console.error('Fehler beim Hinzufügen des Fotos:', error);
        
        if (button) {
            button.disabled = false;
            button.textContent = 'Zu Aktivität hinzufügen';
        }
        
        alert('Fehler beim Hinzufügen des Fotos: ' + error.message);
    }
}
