/**
 * Google Photos Picker API Integration
 * Ersetzt die veraltete Google Photos Library API für die Fotoauswahl
 */

class GooglePhotoPicker {
    constructor(apiKey, clientId) {
        this.apiKey = apiKey;
        this.clientId = clientId;
        this.accessToken = null;
        this.isInitialized = false;
        this.pickerCallback = null;
    }

    /**
     * Initialisiert die Google APIs
     */
    async initialize() {
        if (this.isInitialized) return;

        try {
            // Lade Google APIs
            await this.loadGoogleAPIs();
            
            // Initialisiere Google Auth
            await gapi.load('auth2', () => {
                gapi.auth2.init({
                    client_id: this.clientId
                });
            });

            // Initialisiere Picker API
            await new Promise((resolve) => {
                gapi.load('picker', resolve);
            });

            this.isInitialized = true;
            console.log('Google Photos Picker initialized successfully');
        } catch (error) {
            console.error('Failed to initialize Google Photos Picker:', error);
            throw error;
        }
    }

    /**
     * Lädt die Google APIs dynamisch
     */
    loadGoogleAPIs() {
        return new Promise((resolve, reject) => {
            if (window.gapi) {
                resolve();
                return;
            }

            const script = document.createElement('script');
            script.src = 'https://apis.google.com/js/api.js';
            script.onload = () => {
                gapi.load('client:auth2:picker', resolve);
            };
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    /**
     * Authentifiziert den Benutzer und öffnet den Photo Picker
     */
    async openPhotoPicker(callback, options = {}) {
        try {
            if (!this.isInitialized) {
                await this.initialize();
            }

            this.pickerCallback = callback;

            // Authentifiziere den Benutzer
            const authInstance = gapi.auth2.getAuthInstance();
            const user = await authInstance.signIn({
                scope: 'https://www.googleapis.com/auth/photoslibrary.readonly'
            });

            this.accessToken = user.getAuthResponse().access_token;

            // Erstelle und öffne den Picker
            this.createPicker(options);

        } catch (error) {
            console.error('Error opening photo picker:', error);
            if (callback) {
                callback({ error: 'Fehler beim Öffnen des Photo Pickers: ' + error.message });
            }
        }
    }

    /**
     * Erstellt den Google Photos Picker
     */
    createPicker(options = {}) {
        const defaultOptions = {
            enableFeatures: [
                google.picker.Feature.MULTISELECT_ENABLED,
                google.picker.Feature.NAV_HIDDEN
            ],
            maxItems: options.maxItems || 10,
            mimeTypes: 'image/*'
        };

        const mergedOptions = { ...defaultOptions, ...options };

        // Erstelle Photos View
        const photosView = new google.picker.PhotosView()
            .setType(google.picker.PhotosView.Type.PHOTOS)
            .setQuery(''); // Leere Query für alle Fotos

        // Konfiguriere Picker
        const picker = new google.picker.PickerBuilder()
            .setAppId(this.clientId)
            .setOAuthToken(this.accessToken)
            .addView(photosView)
            .setCallback(this.handlePickerResponse.bind(this))
            .setTitle('Wähle Fotos aus Google Photos')
            .setSize(800, 600);

        // Füge Features hinzu
        if (mergedOptions.enableFeatures) {
            mergedOptions.enableFeatures.forEach(feature => {
                picker.enableFeature(feature);
            });
        }

        // Öffne den Picker
        picker.build().setVisible(true);
    }

    /**
     * Behandelt die Antwort vom Picker
     */
    handlePickerResponse(data) {
        if (data.action === google.picker.Action.PICKED) {
            const selectedPhotos = data.docs.map(doc => ({
                id: doc.id,
                name: doc.name,
                url: doc.url,
                thumbnailUrl: doc.thumbnails ? doc.thumbnails[0].url : null,
                mimeType: doc.mimeType,
                sizeBytes: doc.sizeBytes,
                lastEditedUtc: doc.lastEditedUtc
            }));

            if (this.pickerCallback) {
                this.pickerCallback({ photos: selectedPhotos });
            }
        } else if (data.action === google.picker.Action.CANCEL) {
            if (this.pickerCallback) {
                this.pickerCallback({ cancelled: true });
            }
        }
    }

    /**
     * Sucht Fotos basierend auf einem Datumsbereich (Fallback-Methode)
     * Hinweis: Die Picker API unterstützt keine direkte Datumsfilterung
     */
    async searchPhotosByDateRange(startDate, endDate, callback) {
        // Da die Picker API keine Datumsfilterung unterstützt,
        // öffnen wir den Picker und lassen den Benutzer manuell auswählen
        const options = {
            title: `Wähle Fotos vom ${startDate.toLocaleDateString()} bis ${endDate.toLocaleDateString()}`
        };

        await this.openPhotoPicker((result) => {
            if (result.photos) {
                // Filtere die ausgewählten Fotos nach Datum (falls möglich)
                const filteredPhotos = this.filterPhotosByDate(result.photos, startDate, endDate);
                callback({ photos: filteredPhotos });
            } else {
                callback(result);
            }
        }, options);
    }

    /**
     * Filtert Fotos nach Datum (begrenzte Funktionalität)
     */
    filterPhotosByDate(photos, startDate, endDate) {
        return photos.filter(photo => {
            if (!photo.lastEditedUtc) return true; // Behalte Fotos ohne Datum
            
            const photoDate = new Date(photo.lastEditedUtc);
            return photoDate >= startDate && photoDate <= endDate;
        });
    }
}

// Globale Instanz für einfache Verwendung
let googlePhotoPicker = null;

/**
 * Initialisiert den Google Photos Picker
 */
async function initializeGooglePhotoPicker(apiKey, clientId) {
    if (!googlePhotoPicker) {
        googlePhotoPicker = new GooglePhotoPicker(apiKey, clientId);
        await googlePhotoPicker.initialize();
    }
    return googlePhotoPicker;
}

/**
 * Öffnet den Google Photos Picker für Aktivitäten
 */
async function openPhotoPickerForActivity(activityId, startDate, endDate) {
    try {
        if (!googlePhotoPicker) {
            throw new Error('Google Photos Picker ist nicht initialisiert');
        }

        // Zeige Loading-Indikator
        showPhotoPickerLoading(true);

        await googlePhotoPicker.searchPhotosByDateRange(
            new Date(startDate),
            new Date(endDate),
            (result) => {
                showPhotoPickerLoading(false);
                
                if (result.error) {
                    showPhotoPickerError(result.error);
                } else if (result.cancelled) {
                    console.log('Photo picker was cancelled');
                } else if (result.photos) {
                    displaySelectedPhotos(result.photos);
                }
            }
        );
    } catch (error) {
        showPhotoPickerLoading(false);
        showPhotoPickerError('Fehler beim Öffnen des Photo Pickers: ' + error.message);
    }
}

/**
 * Zeigt/versteckt den Loading-Indikator
 */
function showPhotoPickerLoading(show) {
    const loadingElement = document.getElementById('google-photos-loading');
    if (loadingElement) {
        loadingElement.style.display = show ? 'block' : 'none';
    }
}

/**
 * Zeigt Fehlermeldungen an
 */
function showPhotoPickerError(message) {
    const errorElement = document.getElementById('google-photos-error');
    if (errorElement) {
        errorElement.textContent = message;
        errorElement.style.display = 'block';
        
        // Verstecke Fehler nach 5 Sekunden
        setTimeout(() => {
            errorElement.style.display = 'none';
        }, 5000);
    } else {
        alert(message);
    }
}

/**
 * Zeigt die ausgewählten Fotos an
 */
function displaySelectedPhotos(photos) {
    const container = document.getElementById('selected-photos-container');
    const grid = document.getElementById('selected-photos-grid');

    if (!container || !grid) {
        console.warn('Selected photos container not found');
        return;
    }

    // Container anzeigen und Grid leeren
    container.style.display = 'block';
    grid.innerHTML = '';

    if (photos.length === 0) {
        grid.innerHTML = '<p>Keine Fotos ausgewählt.</p>';
        return;
    }

    photos.forEach((photo, index) => {
        const photoElement = document.createElement('div');
        photoElement.className = 'selected-photo';
        photoElement.innerHTML = `
            <img src="${photo.thumbnailUrl || photo.url}" alt="${photo.name || 'Foto'}"
                 loading="lazy">
            <p>${photo.name || `Foto ${index + 1}`}</p>
            <button onclick="addPhotoToActivity('${photo.id}', '${photo.url}', this)"
                    data-photo-id="${photo.id}">
                Zu Aktivität hinzufügen
            </button>
        `;
        grid.appendChild(photoElement);
    });
}

/**
 * Fügt ein Foto zur Aktivität hinzu
 */
async function addPhotoToActivity(photoId, photoUrl, buttonElement) {
    const button = buttonElement || document.querySelector(`[data-photo-id="${photoId}"]`);

    try {
        if (button) {
            button.disabled = true;
            button.textContent = 'Wird hinzugefügt...';
        }

        // API-Anfrage an den Server
        const response = await fetch('/user/api/google-photos-picker/process', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                activityId: window.currentActivityId,
                photos: [{
                    id: photoId,
                    url: photoUrl,
                    name: `Google Photos Bild ${photoId}`
                }]
            })
        });

        const result = await response.json();

        if (response.ok && result.success) {
            if (button) {
                button.textContent = '✓ Hinzugefügt';
                button.style.backgroundColor = '#28a745';
            }

            // Zeige Erfolgsmeldung
            showPhotoPickerSuccess(`${result.processedCount} Foto(s) erfolgreich hinzugefügt!`);

            // Optional: Seite nach kurzer Verzögerung neu laden
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        } else {
            throw new Error(result.error || 'Fehler beim Hinzufügen des Fotos');
        }
    } catch (error) {
        console.error('Error adding photo to activity:', error);

        if (button) {
            button.disabled = false;
            button.textContent = 'Zu Aktivität hinzufügen';
        }

        showPhotoPickerError('Fehler beim Hinzufügen des Fotos: ' + error.message);
    }
}

/**
 * Zeigt eine Erfolgsmeldung an
 */
function showPhotoPickerSuccess(message) {
    const statusElement = document.querySelector('[id^="google-photos-status-"]');
    if (statusElement) {
        statusElement.textContent = message;
        statusElement.className = 'google-photos-status success';
        statusElement.style.display = 'block';

        // Verstecke Nachricht nach 5 Sekunden
        setTimeout(() => {
            statusElement.style.display = 'none';
        }, 5000);
    }
}
