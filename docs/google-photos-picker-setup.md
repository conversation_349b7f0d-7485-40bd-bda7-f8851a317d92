# Google Photos Picker API Setup

## Übersicht

Aufgrund der Änderungen von Google am 1. April 2025 wurde die Google Photos Library API stark eingeschränkt. Die Scopes `photoslibrary.readonly`, `photoslibrary.sharing` und `photoslibrary` wurden entfernt. Al<PERSON> hat Google die **Google Photos Picker API** eingeführt.

## Was wurde geändert?

### V<PERSON><PERSON> (bis 31. März 2025):
- ❌ `https://www.googleapis.com/auth/photoslibrary.readonly` - **ENTFERNT**
- ❌ Direkter API-Zugriff auf alle Benutzerfotos
- ❌ Automatische Suche nach Fotos basierend auf Datumsbereich

### Jetzt (ab 1. April 2025):
- ✅ Google Photos Picker API - Benutzer wählt Fotos manuell aus
- ✅ Sicherere, benutzergesteuerte Fotoauswahl
- ✅ Keine OAuth-Scopes für die Picker API erforderlich

## Google Developer Console Setup

### 1. APIs aktivieren

Gehe zur [Google Cloud Console](https://console.cloud.google.com/) und aktiviere:

1. **Google Photos Library API** (für grundlegende Funktionalität)
2. **Google Picker API** (falls verfügbar)

### 2. API-Schlüssel erstellen

1. Gehe zu "APIs & Services" → "Credentials"
2. Klicke auf "Create Credentials" → "API Key"
3. Kopiere den API-Schlüssel
4. **Wichtig**: Beschränke den API-Schlüssel auf deine Domain für Sicherheit

### 3. OAuth 2.0 Client konfigurieren

Die bestehende OAuth-Konfiguration kann weiterverwendet werden:
- **Client ID**: Bereits vorhanden
- **Client Secret**: Bereits vorhanden
- **Authorized redirect URIs**: Bereits konfiguriert

## Umgebungsvariablen

Füge diese Variablen zu deiner `.env` Datei hinzu:

```bash
# Bestehende Google OAuth Konfiguration (bereits vorhanden)
GOOGLE_CLIENT_ID=deine_client_id
GOOGLE_CLIENT_SECRET=dein_client_secret
GOOGLE_REDIRECT_URI=http://localhost:3000/auth/google/callback

# Neue Picker API Konfiguration
GOOGLE_PICKER_API_KEY=dein_api_schluessel
GOOGLE_PICKER_ENABLED=true
```

## Implementierung

### Backend

Die Implementierung umfasst:

1. **Controller**: `src/controllers/googlePhotoPickerController.ts`
   - Konfiguration bereitstellen
   - Ausgewählte Fotos verarbeiten
   - Integration Status abrufen

2. **Routen**: In `src/routes/userRoutes.ts`
   - `/user/api/google-photos-picker/config` - Picker-Konfiguration
   - `/user/api/google-photos-picker/process` - Foto-Verarbeitung
   - `/user/api/google-photos-picker/status` - Integration Status

3. **Konfiguration**: `src/config/config.ts`
   - Neue Picker API Einstellungen
   - Erweiterte GooglePhotosConfig Interface

### Frontend

1. **JavaScript**: `public/js/googlePhotoPicker.js`
   - Google Photos Picker API Integration
   - Foto-Auswahl und -Verarbeitung
   - Benutzerinteraktion

2. **CSS**: `public/css/google-photos-picker.css`
   - Styling für Picker-Integration
   - Responsive Design
   - Dark Mode Support

3. **Template**: `views/partials/activity_map_with_charts.ejs`
   - UI-Integration in Aktivitätsansichten
   - Button für Foto-Auswahl
   - Status-Anzeigen

## Funktionsweise

### 1. Benutzer-Workflow

1. Benutzer klickt auf "📷 Fotos aus Google Photos hinzufügen"
2. Google Photos Picker öffnet sich in einem Popup
3. Benutzer authentifiziert sich bei Google (falls nötig)
4. Benutzer wählt Fotos aus seiner Google Photos Bibliothek
5. Ausgewählte Fotos werden zur Aktivität hinzugefügt

### 2. Technischer Ablauf

1. Frontend lädt Google APIs dynamisch
2. Picker wird mit OAuth-Token initialisiert
3. Benutzer-Auswahl wird an Backend gesendet
4. Backend verarbeitet und speichert Foto-Referenzen
5. UI wird aktualisiert

## Einschränkungen der neuen API

### Was funktioniert:
- ✅ Manuelle Foto-Auswahl durch Benutzer
- ✅ Zugriff auf alle Benutzerfotos
- ✅ Mehrfachauswahl von Fotos
- ✅ Thumbnail-Anzeige

### Was nicht mehr funktioniert:
- ❌ Automatische Suche nach Datumsbereich
- ❌ Programmatischer Zugriff ohne Benutzerinteraktion
- ❌ Bulk-Download aller Fotos
- ❌ Direkte API-Abfragen

## Migration von der alten API

### Alte Implementierung (deaktiviert):
```javascript
// Diese Funktion wirft jetzt einen Fehler
await googlePhotosService.findPhotosByDateRangeForUser(userId, startDate, endDate);
```

### Neue Implementierung:
```javascript
// Benutzer wählt Fotos manuell aus
await openPhotoPickerForActivity(activityId, startDate, endDate);
```

## Fehlerbehebung

### Häufige Probleme:

1. **"Google Photos Picker nicht verfügbar"**
   - Prüfe `GOOGLE_PICKER_API_KEY` in .env
   - Prüfe `GOOGLE_PICKER_ENABLED=true`
   - Stelle sicher, dass APIs aktiviert sind

2. **"Insufficient authentication scopes"**
   - Das ist der alte Fehler - die neue Picker API benötigt keine Scopes
   - Stelle sicher, dass die neue Implementierung verwendet wird

3. **Picker öffnet sich nicht**
   - Prüfe Browser-Konsole auf JavaScript-Fehler
   - Stelle sicher, dass Google APIs geladen werden
   - Prüfe Popup-Blocker-Einstellungen

### Debug-Informationen:

```javascript
// Status der Integration prüfen
fetch('/user/api/google-photos-picker/status')
  .then(response => response.json())
  .then(status => console.log(status));
```

## Sicherheitshinweise

1. **API-Schlüssel beschränken**: Beschränke den Google API-Schlüssel auf deine Domain
2. **HTTPS verwenden**: Google APIs erfordern HTTPS in der Produktion
3. **Client-seitige Validierung**: Validiere Foto-Auswahl im Backend
4. **Rate Limiting**: Implementiere Rate Limiting für API-Aufrufe

## Weiterführende Links

- [Google Photos Picker API Dokumentation](https://developers.google.com/photos/picker)
- [Google Photos API Updates](https://developers.google.com/photos/support/updates)
- [Migration Guide](https://developers.googleblog.com/en/google-photos-picker-api-launch-and-library-api-updates/)
