-- <PERSON><PERSON><PERSON> für Google Photos Picker API
-- Diese Spalten speichern die Tokens für die neue Google Photos Picker API

ALTER TABLE users 
ADD COLUMN google_photos_picker_access_token TEXT NULL AFTER google_drive_folder_id,
ADD COLUMN google_photos_picker_refresh_token TEXT NULL AFTER google_photos_picker_access_token,
ADD COLUMN google_photos_picker_expires_at BIGINT NULL AFTER google_photos_picker_refresh_token,
ADD COLUMN google_photos_picker_scope TEXT NULL AFTER google_photos_picker_expires_at;

-- Kommentar für die neuen Spalten
ALTER TABLE users 
MODIFY COLUMN google_photos_picker_access_token TEXT NULL COMMENT 'Google Photos Picker API Access Token',
MODIFY COLUMN google_photos_picker_refresh_token TEXT NULL COMMENT 'Google Photos Picker API Refresh Token',
MODIFY COLUMN google_photos_picker_expires_at BIGINT NULL COMMENT 'Google Photos Picker API Token Expiry (Unix timestamp)',
MODIFY COLUMN google_photos_picker_scope TEXT NULL COMMENT 'Google Photos Picker API Scopes';
