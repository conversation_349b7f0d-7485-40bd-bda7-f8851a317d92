<!doctype html>
<html lang="en">

	<head>
		<!-- Required meta tags -->
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
		<title>Photo Picker</title>

	  <link href="/styles.css" rel="stylesheet">

	  <script src="/js/jquery.slim.min.js"></script>
	  <script src="/qrcode.js"></script>

		<script src="/js/picker.js"></script>

	</head>

	<body>


		<div id="modal" class="relative z-10" style="display:none;">
		  
		  <div class="fixed inset-0 bg-gray-50 bg-opacity-75 transition-opacity" aria-hidden="true"></div>

		  <div class="fixed inset-8 z-10 bg-white border border-gray-400 rounded-lg overflow-y-auto">
		  	
		  	<button onclick="closeModal()" class='px-2 py-1 m-2 border border-red-500 text-red-500 hover:text-white hover:bg-red-500 hover:border-red-800 rounded-md text-sm'>Close</button>
		    <div id="downloadVideo" class="p-2 text-lg text-gray-500" style="display: none;">Fetching video...</div>

		    <div class="flex flex-col items-center p-4 text-center sm:items-center sm:p-0">
		    	
		    	<img id="largeImg" style="display: none;" />
		    	<video id="largeVideo" controls  style="display: none;" />
		    	<table id="metadataTable">

		    	</table>
		    </div>
		  </div>

		  </div>
		</div>


    <div class="text-right">
      <a href="/disconnect"
        class="inline-block cursor-pointer py-1 px-2 m-2 border border-gray-400 hover:border-blue-400 hover:bg-blue-50 rounded-md text-sm">
        Disconnect from Google Photos
      </a>
    </div>


    <div class="text-left">
      <h1 href="/disconnect"
        class="text-xl text-gray-500 px-10 py-2">
        Selected media items
        <a href="/new_session" class="text-blue-500 ml-4 text-sm hover:underline">Change selection</a>
      </h1>
      <div class="mx-10 mt-8 text-xs text-gray-500 font-bold">Click on an image to see media item details</div>
    </div>


    <div>
    	<p id="loading" class="p-16 text-gray-500 text-lg">Loading...</p>
    </div>

    <div id="imageList" class="p-8 flex flex-wrap">

    </div>


    <div class="flex justify-between">
    	<div class="px-8">
	    	<button id="prev" onclick="history.back()" style="display:none;"
	    class="cursor-pointer rounded-md m-2 py-2 px-4 text-sm border border-blue-500 text-blue-800 hover:text-white hover:bg-blue-500 hover:border-blue-800">Previous</button>
	  	</div>

	  	<div class="px-8">
	    	<a id="next" href="/list" style="display:none;"
	    class="cursor-pointer rounded-md m-2 py-2 px-4 text-sm border border-blue-500 text-blue-800 hover:text-white hover:bg-blue-500 hover:border-blue-800"
	    >Next</a>
	  	</div>

	  </div>

	</body>

	<script type="text/javascript">
		
		let mediaItems = []

		const urlParams = new URLSearchParams(window.location.search);
		let thisPageToken = urlParams.get("pageToken")
		let nextPageToken = null

		if(thisPageToken) {
			$("#prev").show()			
		}

		const loadList = async () => {	
			const imageResponse = await fetchImages(thisPageToken)

			if('nextPageToken' in imageResponse.images) {
				nextPageToken = imageResponse.images.nextPageToken
				$("#next").attr("href", "/list?pageToken="+nextPageToken)
				$("#next").show()
			}

			mediaItems = imageResponse.images.mediaItems

			$("#loading").hide()

			mediaItems.forEach((mediaItem, idx) => {
				const imgId = `mediaItem-${idx}`
				$("#imageList").append(`<img class='thumbnail cursor-pointer rounded-md m-2' id='${imgId}' />`)

				// Select thumbnail size to display in list (here that is max 128px x 128px)
				const sizeParams = "=w128-h128"

				loadImageIntoImg(imgId, mediaItem.mediaFile.baseUrl+sizeParams)
			})

		}

		loadList()


		$(document).on('click', '.thumbnail', e => {
			e.stopPropagation();
    	e.stopImmediatePropagation();

    	const imageClickedId = parseInt(e.target.id.replace(/.*\-/, ""))

    	const mediaItem = mediaItems[imageClickedId]

    	let metadata = []
			
			$("#metadataTable").html("")

    	const meta = mediaItem.mediaFile.mediaFileMetadata

			if(meta) {
				Object.keys(meta).forEach(key => {
					const value = meta[key]
					if(typeof value === 'object') {
						Object.keys(value).forEach(value_key => {
							metadata.push([value_key, value[value_key]])
						})
					} else {
						metadata.push([key, value])
					}
				})

				let metadatatags = []
				metadata.forEach(value => {
					metadatatags.push(`<tr><td>${value[0]}</td><td>${value[1]}</td></tr>`)
				})

				$("#metadataTable").html(metadatatags.join())

			}

    	$("#largeImg").hide()
    	$("#largeVideo").hide()
    	if(mediaItem.type == "VIDEO") {
				loadImageIntoVideo("largeVideo", mediaItem.mediaFile.baseUrl)
    	} else {
				loadImageIntoImg("largeImg", mediaItem.mediaFile.baseUrl) // shows largeImg    		
    	}
			$("#modal").show()
		})



		const closeModal = () => {
  		$('#modal').hide()
		}

	</script>

</html>






