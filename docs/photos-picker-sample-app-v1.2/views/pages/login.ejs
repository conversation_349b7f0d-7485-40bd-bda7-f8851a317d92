<!DOCTYPE html>
<html lang="en">

  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="icon" type="image/png" href="photos_36dp.png" />
    <link href="/styles.css" rel="stylesheet">

    <title>Picker Sample App</title>
  </head>

  <body>

    <div class="p-8">
      <h1 class="text-xl pb-1 text-gray-600">Google Photos Picker API Example</h1>


      <div class="flex p-8 pt-12 pb-12 border rounded-xl border-gray-200">
        <div class="flex-1 min-w-96  mr-12">
          <span id="naming_device" style="display:none;">
            <h1 class="text-2xl font-medium mb-4">Creating device...</h1>
          </span>
          <span id="name_device">
            <h1 class="text-2xl font-medium mb-4">Connect to Google Photos</h1>

                <a class="p-4 px-8 border border-gray-400 rounded-xl inline-block hover:bg-blue-50 cursor-pointer hover:border-blue-400" id="google_photos_button" href="/auth/google">
                    <div class="inline-block mr-4">
                      <img src="photos_36dp.png" alt="Google Photos Logo" style="width:36px;height:36px;">
                    </div>
                    <div class="inline-block">
                      <h2 class='text-lg font-semibold'>Google Photos</h2>
                      <p class="text-md">Pick images and videos from your library</p>
                    </div>
                </a>

          </span>

        </div>

      </div>

    </div>

  </body>

  <script type="text/javascript">


  </script>

</html>
