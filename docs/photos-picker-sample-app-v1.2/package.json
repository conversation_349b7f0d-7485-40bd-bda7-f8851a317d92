{"name": "picker-server-auth", "version": "1.0.0", "description": "Google Photos Picker API Sample", "private": true, "license": "Apache-2.0", "author": "Google LLC", "main": "app.js", "type": "module", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "serve": "node app.js", "css": "npx tailwindcss -i styles.css -o ./static/styles.css --watch", "server": "npx nodemon --ignore sessions/ --ignore persist-session/ --ignore persist-media-items/ app.js", "dev": "concurrently 'npm:css' 'npm:server'"}, "dependencies": {"axios": "^1.6.7", "body-parser": "^1.20.2", "ejs": "^3.1.9", "express": "^4.18.3", "express-session": "^1.18.0", "jquery": "^3.7.1", "node-fetch": "^3.3.2", "node-persist": "^4.0.1", "passport": "^0.7.0", "passport-google-id-token": "^0.4.7", "passport-google-oauth2": "^0.2.0", "passport-google-oauth20": "^2.0.0", "safevalues": "^0.5.2", "session-file-store": "^1.5.0", "use-object-url": "^1.0.0", "uuid": "^9.0.1"}, "devDependencies": {"concurrently": "^8.2.2", "nodemon": "^3.1.4", "tailwindcss": "^3.4.4"}}