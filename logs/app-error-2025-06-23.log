{"level":"error","message":"[GPhotosSvc Auth User:1] <PERSON><PERSON> beim <PERSON>uern des Google Access Tokens:","metadata":{"error":"invalid_grant","error_description":"Bad Request"},"module":"services/googlePhotosService.js","timestamp":"2025-06-23 05:11:26"}
{"level":"error","message":"[API FindGPhotos User:1 ActDB:1238] Error finding Google Photos: Google Photos Authentifizierung fehlgeschlagen (Token Refresh). Bitte erneut verbinden.","metadata":{"stack":"Error: Google Photos Authentifizierung fehlgeschlagen (Token Refresh). Bitte erneut verbinden.\n    at getAuthenticatedClient (/var/www/html/map/src/services/googlePhotosService.ts:144:19)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at Object.findPhotosByDateRangeForUser (/var/www/html/map/src/services/googlePhotosService.ts:27:26)\n    at findGooglePhotosForActivityApi (/var/www/html/map/src/controllers/userController.ts:370:61)"},"module":"controllers/userController.js","timestamp":"2025-06-23 05:11:26"}
{"level":"error","message":"[GPhotosSvc FindByDateRange User:1] Fehler beim Suchen von Medienobjekten:","metadata":{"error":{"code":403,"errors":[{"domain":"global","message":"Request had insufficient authentication scopes.","reason":"forbidden"}],"message":"Request had insufficient authentication scopes.","status":"PERMISSION_DENIED"}},"module":"services/googlePhotosService.js","timestamp":"2025-06-23 05:12:10"}
{"level":"error","message":"[API FindGPhotos User:1 ActDB:1238] Error finding Google Photos: Fehler bei der Google Photos API: Request had insufficient authentication scopes.","metadata":{"stack":"Error: Fehler bei der Google Photos API: Request had insufficient authentication scopes.\n    at Object.findPhotosByDateRangeForUser (/var/www/html/map/src/services/googlePhotosService.ts:82:15)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at findGooglePhotosForActivityApi (/var/www/html/map/src/controllers/userController.ts:370:61)"},"module":"controllers/userController.js","timestamp":"2025-06-23 05:12:10"}
{"level":"error","message":"[GPhotosSvc FindByDateRange User:1] Fehler beim Suchen von Medienobjekten:","metadata":{"error":{"code":403,"errors":[{"domain":"global","message":"Request had insufficient authentication scopes.","reason":"forbidden"}],"message":"Request had insufficient authentication scopes.","status":"PERMISSION_DENIED"}},"module":"services/googlePhotosService.js","timestamp":"2025-06-23 05:12:22"}
{"level":"error","message":"[API FindGPhotos User:1 ActDB:1238] Error finding Google Photos: Fehler bei der Google Photos API: Request had insufficient authentication scopes.","metadata":{"stack":"Error: Fehler bei der Google Photos API: Request had insufficient authentication scopes.\n    at Object.findPhotosByDateRangeForUser (/var/www/html/map/src/services/googlePhotosService.ts:82:15)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at findGooglePhotosForActivityApi (/var/www/html/map/src/controllers/userController.ts:370:61)"},"module":"controllers/userController.js","timestamp":"2025-06-23 05:12:22"}
{"level":"error","message":"[GPhotosSvc FindByDateRange User:1] Fehler beim Suchen von Medienobjekten:","metadata":{"error":{"code":403,"errors":[{"domain":"global","message":"Request had insufficient authentication scopes.","reason":"forbidden"}],"message":"Request had insufficient authentication scopes.","status":"PERMISSION_DENIED"}},"module":"services/googlePhotosService.js","timestamp":"2025-06-23 05:14:29"}
{"level":"error","message":"[API FindGPhotos User:1 ActDB:1238] Error finding Google Photos: Fehler bei der Google Photos API: Request had insufficient authentication scopes.","metadata":{"stack":"Error: Fehler bei der Google Photos API: Request had insufficient authentication scopes.\n    at Object.findPhotosByDateRangeForUser (/var/www/html/map/src/services/googlePhotosService.ts:82:15)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at findGooglePhotosForActivityApi (/var/www/html/map/src/controllers/userController.ts:370:61)"},"module":"controllers/userController.js","timestamp":"2025-06-23 05:14:29"}
{"level":"error","message":"[GPhotosSvc FindByDateRange User:1] Fehler beim Suchen von Medienobjekten:","metadata":{"error":{"code":403,"errors":[{"domain":"global","message":"Request had insufficient authentication scopes.","reason":"forbidden"}],"message":"Request had insufficient authentication scopes.","status":"PERMISSION_DENIED"}},"module":"services/googlePhotosService.js","timestamp":"2025-06-23 05:29:48"}
{"level":"error","message":"[API FindGPhotos User:1 ActDB:1238] Error finding Google Photos: Fehler bei der Google Photos API: Request had insufficient authentication scopes.","metadata":{"stack":"Error: Fehler bei der Google Photos API: Request had insufficient authentication scopes.\n    at Object.findPhotosByDateRangeForUser (/var/www/html/map/src/services/googlePhotosService.ts:96:15)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at findGooglePhotosForActivityApi (/var/www/html/map/src/controllers/userController.ts:370:61)"},"module":"controllers/userController.js","timestamp":"2025-06-23 05:29:49"}
{"level":"error","message":"[GPhotosSvc Auth User:1] Keine Google Access Tokens für User 1 gefunden.","metadata":{},"module":"services/googlePhotosService.js","timestamp":"2025-06-23 05:30:10"}
{"level":"error","message":"[API FindGPhotos User:1 ActDB:1238] Error finding Google Photos: Google Photos ist für diesen Benutzer nicht (oder nicht mehr korrekt) verbunden. Bitte in den Einstellungen erneut verbinden.","metadata":{"stack":"Error: Google Photos ist für diesen Benutzer nicht (oder nicht mehr korrekt) verbunden. Bitte in den Einstellungen erneut verbinden.\n    at getAuthenticatedClient (/var/www/html/map/src/services/googlePhotosService.ts:115:15)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at Object.findPhotosByDateRangeForUser (/var/www/html/map/src/services/googlePhotosService.ts:27:26)\n    at findGooglePhotosForActivityApi (/var/www/html/map/src/controllers/userController.ts:370:61)"},"module":"controllers/userController.js","timestamp":"2025-06-23 05:30:10"}
{"level":"error","message":"[GPhotosSvc FindByDateRange User:1] Fehler beim Suchen von Medienobjekten:","metadata":{"error":{"code":403,"errors":[{"domain":"global","message":"Request had insufficient authentication scopes.","reason":"forbidden"}],"message":"Request had insufficient authentication scopes.","status":"PERMISSION_DENIED"}},"module":"services/googlePhotosService.js","timestamp":"2025-06-23 05:30:31"}
{"level":"error","message":"[API FindGPhotos User:1 ActDB:1238] Error finding Google Photos: Fehler bei der Google Photos API: Request had insufficient authentication scopes.","metadata":{"stack":"Error: Fehler bei der Google Photos API: Request had insufficient authentication scopes.\n    at Object.findPhotosByDateRangeForUser (/var/www/html/map/src/services/googlePhotosService.ts:96:15)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at findGooglePhotosForActivityApi (/var/www/html/map/src/controllers/userController.ts:370:61)"},"module":"controllers/userController.js","timestamp":"2025-06-23 05:30:31"}
